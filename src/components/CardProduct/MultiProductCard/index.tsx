import { Flex, Box, Image, Text } from '@chakra-ui/react';
import ButtonWhatsapp from '../../ButtonWhatsapp';
import { MessageProductCard } from '../../../types/Message';

interface MultiProductCardProps {
  card: MessageProductCard;
  onOpen: () => void;
}

const MultiProductCard = ({ card, onOpen }: MultiProductCardProps) => {
  const firstProduct = card.messageProductCardItems[0];
  return (
    <>
      <Flex alignItems="center" mb={2}>
        {firstProduct.productVariant.imageUrl && (
          <Image
            src={firstProduct.productVariant.imageUrl}
            alt={firstProduct.name}
            width="60px"
            height="60px"
            objectFit="cover"
            borderRadius="6px"
            mr={3}
          />
        )}
        <Box>
          <Text fontSize="14px" fontWeight="bold" color="#1c1c1c">
            {card.headerText}
          </Text>
          <Text fontSize="12px" color="gray.600">
            {card.messageProductCardItems.length}{' '}
            {card.messageProductCardItems.length === 1 ? 'item' : 'itens'}
          </Text>
        </Box>
      </Flex>

      {card.bodyText && (
        <Text fontSize="13px" color="gray.800" mb={1}>
          {card.bodyText}
        </Text>
      )}
      {card.footerText && (
        <Text fontSize="12px" color="gray.500" mb={2}>
          {card.footerText}
        </Text>
      )}

      <ButtonWhatsapp onClick={onOpen}>Mostrar itens</ButtonWhatsapp>
    </>
  );
};

export default MultiProductCard;
