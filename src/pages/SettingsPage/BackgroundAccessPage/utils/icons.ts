import { IconType } from 'react-icons';
import {
  FaUsers,
  FaComments,
  FaEnvelope,
  FaBoxes,
  FaChartBar,
  FaCog,
  FaBug,
  FaHome,
  FaMailBulk,
  FaRobot,
  FaFileAlt,
} from 'react-icons/fa';

export const getModuleIcon = (permission: string): IconType => {
  switch (permission) {
    case 'Chat':
      return FaComments;
    case 'Clientes':
      return FaUsers;
    case 'Campanhas':
      return FaEnvelope;
    case 'Templates':
      return FaFileAlt;
    case 'Templates de Email':
      return FaMailBulk;
    case 'Automações':
      return FaRobot;
    case 'Configurações':
      return FaCog;
    case 'Relatórios':
      return FaChartBar;
    case 'Ferramentas de Debug':
      return FaBug;
    case 'Tela de Início':
      return FaHome;
    case 'Produtos':
      return FaBoxes;
    default:
      return FaFileAlt;
  }
};
