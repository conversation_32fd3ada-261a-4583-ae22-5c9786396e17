export const IOSPushNotificationsUtils = {
  isIOS(): boolean {
    const userAgent =
      navigator.userAgent || navigator.vendor || (window as any).opera;
    return (
      /iPad|iPhone|iPod/.test(userAgent) ||
      (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1)
    );
  },

  isIOSSafari(): boolean {
    const userAgent =
      navigator.userAgent || navigator.vendor || (window as any).opera;
    return (
      /iPad|iPhone|iPod/.test(userAgent) &&
      /Safari/.test(userAgent) &&
      !/Chrome/.test(userAgent)
    );
  },

  isPushSupported(): boolean {
    return (
      'serviceWorker' in navigator &&
      'PushManager' in window &&
      'Notification' in window
    );
  },

  isIOSPushSupported(): boolean {
    if (!this.isIOS()) {
      return this.isPushSupported();
    }

    const isStandalone = (window.navigator as any).standalone === true;
    const isPWAInstalled = window.matchMedia(
      '(display-mode: standalone)',
    ).matches;

    return this.isPushSupported() && (isStandalone || isPWAInstalled);
  },

  getIOSInfo() {
    const userAgent = navigator.userAgent;
    const isStandalone = (window.navigator as any).standalone === true;
    const isPWAInstalled = window.matchMedia(
      '(display-mode: standalone)',
    ).matches;

    return {
      isIOS: this.isIOS(),
      isIOSSafari: this.isIOSSafari(),
      isStandalone,
      isPWAInstalled,
      userAgent,
      pushSupported: this.isPushSupported(),
      iosPushSupported: this.isIOSPushSupported(),
    };
  },

  async requestIOSPermission(): Promise<NotificationPermission> {
    if (!this.isIOS()) {
      return Notification.requestPermission();
    }

    const isStandalone = (window.navigator as any).standalone === true;
    const isPWAInstalled = window.matchMedia(
      '(display-mode: standalone)',
    ).matches;

    if (!isStandalone && !isPWAInstalled) {
      throw new Error(
        'Para receber notificações no iOS, instale o app como PWA. Toque no botão de compartilhar e selecione "Adicionar à Tela Inicial".',
      );
    }

    return Notification.requestPermission();
  },

  async registerIOSServiceWorker(): Promise<ServiceWorkerRegistration> {
    if (!this.isIOS()) {
      throw new Error('Este método é específico para dispositivos iOS');
    }

    try {
      const registration =
        await navigator.serviceWorker.register('/service-worker.js');
      await navigator.serviceWorker.ready;
      return registration;
    } catch (error) {
      console.error('Erro ao registrar service worker no iOS:', error);
      throw new Error('Falha ao configurar notificações no iOS');
    }
  },

  async subscribeToIOSPush(
    vapidPublicKey: string,
  ): Promise<PushSubscription | null> {
    if (!this.isIOS()) {
      throw new Error('Este método é específico para dispositivos iOS');
    }

    try {
      const registration = await this.registerIOSServiceWorker();
      const existingSubscription =
        await registration.pushManager.getSubscription();

      if (existingSubscription) {
        return existingSubscription;
      }

      const base64 = vapidPublicKey
        .replace(/-/g, '+')
        .replace(/_/g, '/')
        .padEnd(
          vapidPublicKey.length + ((4 - (vapidPublicKey.length % 4)) % 4),
          '=',
        );

      const applicationServerKey = Uint8Array.from(atob(base64), (c) =>
        c.charCodeAt(0),
      );

      const subscription = await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey,
      });

      return subscription;
    } catch (error) {
      console.error('Erro ao inscrever para notificações push no iOS:', error);

      if (error instanceof Error) {
        if (error.message.includes('permission')) {
          throw new Error('Permissão negada para notificações no iOS');
        } else if (error.message.includes('service worker')) {
          throw new Error('Service worker não está disponível no iOS');
        }
      }

      throw new Error('Falha ao configurar notificações push no iOS');
    }
  },

  getIOSErrorMessage(error: any): string {
    if (!this.isIOS()) {
      return error instanceof Error ? error.message : 'Erro desconhecido';
    }

    const iosInfo = this.getIOSInfo();

    if (!iosInfo.isPWAInstalled && !iosInfo.isStandalone) {
      return 'Para receber notificações no iOS, instale o app como PWA. Toque no botão de compartilhar e selecione "Adicionar à Tela Inicial".';
    }

    if (!iosInfo.pushSupported) {
      return 'Seu navegador no iOS não suporta notificações push.';
    }

    if (error instanceof Error) {
      if (error.message.includes('permission')) {
        return 'Permissão negada para notificações. Verifique as configurações do Safari.';
      }
      if (error.message.includes('service worker')) {
        return 'Service worker não está disponível. Tente recarregar a página.';
      }
    }

    return 'Erro ao configurar notificações no iOS. Tente instalar como PWA.';
  },

  isPWAInstalled(): boolean {
    return (
      (window.navigator as any).standalone === true ||
      window.matchMedia('(display-mode: standalone)').matches
    );
  },

  showPWAInstallInstructions(): void {
    if (!this.isIOS()) {
      return;
    }

    const instructions = [
      'Para receber notificações no iOS:',
      '1. Toque no botão de compartilhar (ícone quadrado com seta)',
      '2. Role para baixo e toque em "Adicionar à Tela Inicial"',
      '3. Toque em "Adicionar"',
      '4. Abra o app da tela inicial e tente ativar as notificações novamente',
    ];

    if (window.alert) {
      window.alert(instructions.join('\n'));
    }
  },
};
