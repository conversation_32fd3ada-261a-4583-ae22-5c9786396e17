import { useState, useEffect, useCallback } from 'react';
import {
  WebPushService,
  WebPushCredentials,
} from '../services/web-push.service';
import { WebPushUtils } from '../utils/web-push.utils';
import { IOSPushNotificationsUtils } from '../utils/ios-push-notifications.utils';
import { useMutation, UseMutationResult, useQuery } from 'react-query';
import { apiRoutes } from '../constants/api-routes';
import { AxiosResponse } from 'axios';

interface UsePushNotificationsReturn {
  isSupported: boolean;
  isEnabled: boolean;
  permission: NotificationPermission;
  isLoading: boolean;
  isIOS: boolean;
  isPWAInstalled: boolean;
  enablePushNotifications: () => Promise<void>;
  disablePushNotifications: () => Promise<void>;
  showPWAInstallInstructions: () => void;
  sendTestPushNotificationMutation: UseMutationResult<
    AxiosResponse<void, any>,
    unknown,
    void,
    unknown
  >;
}

export const usePushNotifications = (): UsePushNotificationsReturn => {
  const [vapidPublicKey, setVapidPublicKey] = useState<string | null>(null);
  const [isSupported, setIsSupported] = useState<boolean>(false);
  const [credentials, setCredentials] = useState<WebPushCredentials | null>(
    null,
  );
  const [isEnabled, setIsEnabled] = useState<boolean>(false);
  const [permission, setPermission] =
    useState<NotificationPermission>('default');
  const [isIOS, setIsIOS] = useState<boolean>(false);
  const [isPWAInstalled, setIsPWAInstalled] = useState<boolean>(false);

  const { isLoading: isLoadingVapidPublicKey } = useQuery({
    queryKey: apiRoutes.getWebPushVapidPublicKey(),
    queryFn: async () => {
      const { data } = await WebPushService.getWebPushVapidPublicKey();
      return data;
    },
    onSuccess: (data) => {
      setVapidPublicKey(data.vapidPublicKey ?? null);
    },
  });

  const {
    isLoading: isLoadingValidateWebPushCredentials,
    mutateAsync: validateWebPushCredentialsMutateAsync,
  } = useMutation({
    mutationFn: WebPushService.validateWebPushCredentials,
    onSuccess: () => {
      setIsEnabled(true);
    },
  });

  const {
    mutateAsync: subscribeToWebPushMutateAsync,
    isLoading: isLoadingSubscribeToWebPush,
  } = useMutation({
    mutationFn: WebPushService.subscribeToWebPush,
    onSuccess: () => {
      setIsEnabled(true);
    },
  });

  const {
    mutateAsync: unsubscribeFromWebPushMutationAsync,
    isLoading: isLoadingUnsubscribeFromWebPush,
  } = useMutation({
    mutationFn: WebPushService.removeWebPushSubscription,
    onSuccess: () => {
      setIsEnabled(false);
      setCredentials(null);
    },
  });

  const sendTestPushNotificationMutation = useMutation({
    mutationFn: async () => {
      if (!credentials) {
        throw new Error('Subscription is not set');
      }

      return await WebPushService.sendWebPushTestNotification({
        credentials,
      });
    },
  });

  const checkStatus = useCallback(async (): Promise<void> => {
    const iosInfo = IOSPushNotificationsUtils.getIOSInfo();
    setIsIOS(iosInfo.isIOS);
    setIsPWAInstalled(iosInfo.isPWAInstalled);

    if (iosInfo.isIOS) {
      const supported = iosInfo.iosPushSupported;
      setIsSupported(supported);

      if (supported) {
        const permissionStatus = IOSPushNotificationsUtils.isIOS()
          ? await IOSPushNotificationsUtils.requestIOSPermission()
          : WebPushUtils.getPermissionStatus();
        setPermission(permissionStatus);

        const subscription =
          await WebPushUtils.getSubscriptionToPushNotifications();
        if (subscription) {
          const parsedSubscription =
            WebPushUtils.parseSubscription(subscription);
          setCredentials(parsedSubscription);
          const valid = (
            await validateWebPushCredentialsMutateAsync({
              credentials: parsedSubscription,
            })
          ).data.isValid;
          setIsEnabled(valid);
        }
      }
    } else {
      const supported = WebPushUtils.isSupported();
      setIsSupported(supported);

      if (supported) {
        const permissionStatus = WebPushUtils.getPermissionStatus();
        setPermission(permissionStatus);
        const subscription =
          await WebPushUtils.getSubscriptionToPushNotifications();

        if (subscription) {
          const parsedSubscription =
            WebPushUtils.parseSubscription(subscription);
          setCredentials(parsedSubscription);
          const valid = (
            await validateWebPushCredentialsMutateAsync({
              credentials: parsedSubscription,
            })
          ).data.isValid;
          setIsEnabled(valid);
        }
      }
    }
  }, [vapidPublicKey]);

  const enablePushNotifications = useCallback(async (): Promise<void> => {
    if (!vapidPublicKey) {
      throw new Error('VAPID_PUBLIC_KEY is not set');
    }

    try {
      let subscription: PushSubscription | null = null;

      if (IOSPushNotificationsUtils.isIOS()) {
        if (!IOSPushNotificationsUtils.isPWAInstalled()) {
          IOSPushNotificationsUtils.showPWAInstallInstructions();
          throw new Error(
            'Para receber notificações no iOS, instale o app como PWA',
          );
        }

        subscription =
          await IOSPushNotificationsUtils.subscribeToIOSPush(vapidPublicKey);
      } else {
        subscription =
          await WebPushUtils.locallySubscribeToPushNotifications(
            vapidPublicKey,
          );
      }

      if (!subscription) {
        throw new Error('Falha ao criar inscrição para notificações push');
      }

      const parsedSubscription = WebPushUtils.parseSubscription(subscription);
      setCredentials(parsedSubscription);
      await subscribeToWebPushMutateAsync({
        credentials: parsedSubscription,
      });
    } catch (error) {
      console.error('Erro ao ativar notificações push:', error);
      if (IOSPushNotificationsUtils.isIOS()) {
        const errorMessage =
          IOSPushNotificationsUtils.getIOSErrorMessage(error);
        throw new Error(errorMessage);
      } else {
        throw new Error(
          error instanceof Error
            ? error.message
            : 'Erro desconhecido ao ativar notificações',
        );
      }
    }
  }, [vapidPublicKey, subscribeToWebPushMutateAsync, credentials]);

  const disablePushNotifications = useCallback(async (): Promise<void> => {
    try {
      await WebPushUtils.locallyUnsubscribeFromPushNotifications();
      await unsubscribeFromWebPushMutationAsync();
      setIsEnabled(false);
    } catch (error) {
      console.error('Erro ao desativar notificações push:', error);
      throw new Error('Erro ao desativar notificações push');
    }
  }, [unsubscribeFromWebPushMutationAsync]);

  const showPWAInstallInstructions = useCallback(() => {
    IOSPushNotificationsUtils.showPWAInstallInstructions();
  }, []);

  useEffect(() => {
    checkStatus();
  }, [checkStatus]);

  return {
    isSupported,
    isEnabled,
    permission,
    isLoading:
      isLoadingVapidPublicKey ||
      isLoadingValidateWebPushCredentials ||
      isLoadingSubscribeToWebPush ||
      isLoadingUnsubscribeFromWebPush,
    isIOS,
    isPWAInstalled,
    enablePushNotifications,
    disablePushNotifications,
    showPWAInstallInstructions,
    sendTestPushNotificationMutation,
  };
};
