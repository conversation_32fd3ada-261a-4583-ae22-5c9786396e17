import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Box, Button, Text } from '@chakra-ui/react';
import { MessageWithCardsIncludes } from '../../../../types/Message';
import {
  CreateInfoLogDto,
  LogsService,
} from '../../../../services/logs.service';
import { useMutation } from 'react-query';
import { ConversationWithIncludes } from '../../../../types/Conversation';
import { AgentsService } from '../../../../services/agents.service';
import { colors } from '../../../../constants/colors';
import RiveLoadingAnimation from '../../../../components/RiveLoadingAnimation';

const animationAppearDissapearKeyframes = {
  '@keyframes appearFromBottom': {
    '0%': {
      opacity: 0,
      transform: 'translateY(20px)',
    },
    '100%': {
      opacity: 1,
      transform: 'translateY(0)',
    },
  },
  '@keyframes disappearToBottom': {
    '0%': {
      opacity: 1,
      transform: 'translateY(0)',
    },
    '100%': {
      opacity: 0,
      transform: 'translateY(20px)',
    },
  },
};

const borderAnimationSx = {
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderRadius: 'md',
    padding: '2px',
    background:
      'linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff)',
    backgroundSize: '400% 400%',
    mask: 'linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)',
    maskComposite: 'xor',
    WebkitMask:
      'linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)',
    WebkitMaskComposite: 'xor',
    animation: 'aiGradientBorder 3s ease-in-out infinite',
  },
  '@keyframes aiGradientBorder': {
    '0%': {
      backgroundPosition: '0% 50%',
      opacity: 1,
    },
    '33.33%': {
      backgroundPosition: '100% 50%',
      opacity: 1,
    },
    '66.66%': {
      backgroundPosition: '0% 50%',
      opacity: 1,
    },
    '90%': {
      backgroundPosition: '100% 50%',
      opacity: 1,
    },
    '100%': {
      backgroundPosition: '0% 50%',
      opacity: 1,
    },
  },
};

interface AiSuggestionBoxProps {
  currentUserId: string;
  conversation: ConversationWithIncludes;
  messages: MessageWithCardsIncludes[];
  setCurrentSuggestion: (value: string) => void;
  currentSuggestion: string;
  currentTextMessage: string;
}

function AiSuggestionBox({
  currentTextMessage,
  conversation,
  currentUserId,
  messages,
  setCurrentSuggestion,
  currentSuggestion,
}: AiSuggestionBoxProps) {
  const ALLOWED_COMPANIES_TO_TEST =
    import.meta.env.VITE_ALLOWED_COMPANIES_TO_TEST || '';
  const IS_LOCAL_DEVELOPMENT_MODE = import.meta.env.VITE_ENV === 'development';
  const lastProcessedMessageRef = useRef<string | null>(null);
  const lastSentShowwSuggestionLogRef = useRef<string | null>();
  const [aiAutoGetSuggestionTimeoutId, setAiAutoGetSuggestionTimeoutId] =
    useState<NodeJS.Timeout | null>(null);
  const [displayedSuggestion, setDisplayedSuggestion] = useState<string>('');
  const [isTypingAnimation, setIsTypingAnimation] = useState(false);

  const showSuggestion = useMemo(() => {
    if (currentSuggestion && !currentTextMessage) {
      sendShowSuggestionLog();
      return true;
    }
    return false;
  }, [currentSuggestion, currentTextMessage]);

  const aiReplySuggestionsMutation = useMutation(
    async (conversationId: string): Promise<string> => {
      if (
        !conversationId ||
        ALLOWED_COMPANIES_TO_TEST === '' ||
        !ALLOWED_COMPANIES_TO_TEST.split(',').includes(
          conversation?.companyId || '',
        )
      ) {
        return '';
      }

      const { data } = await AgentsService.getAIReplySuggestions(
        conversation?.id,
      );
      return Array.isArray(data.suggestions)
        ? data.suggestions.length > 0
          ? data.suggestions[0]
          : ''
        : data.suggestions;
    },
    {
      onSuccess: (data: string) => {
        if (data.length > 0) {
          setCurrentSuggestion(data);
        }
      },
      onError: (error) => {},
    },
  );

  useEffect(() => {
    setCurrentSuggestion('');
    setDisplayedSuggestion('');
    setIsTypingAnimation(false);
  }, [conversation.id]);

  useEffect(() => {
    if (!showSuggestion) {
      setDisplayedSuggestion('');
      setIsTypingAnimation(false);
      return;
    }
    setIsTypingAnimation(true);
    setDisplayedSuggestion('');

    const totalDuration = 500;
    const charactersPerStep = Math.max(
      1,
      Math.ceil(currentSuggestion.length / 10),
    );
    const stepDuration =
      totalDuration / Math.ceil(currentSuggestion.length / charactersPerStep);

    let currentIndex = 0;

    const typeInterval = setInterval(() => {
      currentIndex += charactersPerStep;

      if (currentIndex >= currentSuggestion.length) {
        setDisplayedSuggestion(currentSuggestion);
        setIsTypingAnimation(false);
        clearInterval(typeInterval);
      } else {
        setDisplayedSuggestion(currentSuggestion.substring(0, currentIndex));
      }
    }, stepDuration);

    return () => {
      clearInterval(typeInterval);
      setIsTypingAnimation(false);
    };
  }, [currentSuggestion, showSuggestion]);

  useEffect(() => {
    if (!conversation.id || !messages.length) return;

    const lastMessage = messages[0];

    if (
      lastMessage &&
      !lastMessage.fromSystem &&
      !currentSuggestion &&
      !aiReplySuggestionsMutation.isLoading &&
      !showSuggestion &&
      lastProcessedMessageRef.current !== lastMessage.id &&
      conversation?.id === conversation.id
    ) {
      lastProcessedMessageRef.current = lastMessage.id;

      if (aiAutoGetSuggestionTimeoutId) {
        clearTimeout(aiAutoGetSuggestionTimeoutId);
        setAiAutoGetSuggestionTimeoutId(null);
      }
      setAiAutoGetSuggestionTimeoutId(
        setTimeout(() => {
          setCurrentSuggestion('');
          setAiAutoGetSuggestionTimeoutId(null);
          aiReplySuggestionsMutation.mutate(conversation.id);
        }, 1500),
      );
    }
  }, [
    messages,
    conversation?.id,
    currentTextMessage,
    aiReplySuggestionsMutation.isLoading,
    showSuggestion,
  ]);

  function testTriggerAutoCompleteOnNewMessage() {
    if (!showSuggestion) {
      const msgTest = 'Olá! Esta é uma sugestão teste';
      setCurrentSuggestion(msgTest);
    }
  }

  async function sendShowSuggestionLog() {
    if (lastSentShowwSuggestionLogRef.current != currentSuggestion) {
      lastSentShowwSuggestionLogRef.current = currentSuggestion;

      LogsService.createInfoLog({
        type: 'generateAiAgentSuggestion',
        message: 'Sugestões de resposta geradas pelo agente de IA na conversa',
        source: 'internal',
        meta: {
          conversationId: conversation.id,
          userId: currentUserId,
          suggestions: currentSuggestion,
        },
      } as CreateInfoLogDto);
    }
  }

  return (
    <>
      {IS_LOCAL_DEVELOPMENT_MODE && (
        <Button
          position="absolute"
          top="-28px"
          right="0px"
          variant="ghost"
          color={colors.darkGrey}
          onClick={testTriggerAutoCompleteOnNewMessage}
          size="sm"
        >
          Testar Auto Complete
        </Button>
      )}
      {aiReplySuggestionsMutation.isLoading && (
        <Box position="absolute" top="-28px" zIndex={3} pointerEvents="none">
          <RiveLoadingAnimation
            isLoading={aiReplySuggestionsMutation.isLoading}
            hasResults={false}
            height="32px"
            width="32px"
          />
        </Box>
      )}
      <Box
        position="absolute"
        top="-28px"
        left="16px"
        zIndex={10}
        pointerEvents="none"
        sx={{
          animation:
            currentSuggestion && !showSuggestion
              ? 'appearFromBottom 0.3s ease-out forwards'
              : 'disappearToBottom 0.3s ease-in forwards',
          ...animationAppearDissapearKeyframes,
        }}
      >
        <Box
          position="relative"
          bg="white"
          px={2}
          py={1}
          borderRadius="md"
          overflow="hidden"
          sx={borderAnimationSx}
        >
          <Text
            fontSize="xs"
            color="gray.500"
            fontWeight="medium"
            position="relative"
            zIndex={1}
          >
            Nossa I.A. tem uma sugestão de resposta, basta pressionar Tab ⇥ para
            utilizar a sugestão
          </Text>
        </Box>
      </Box>
      {showSuggestion && (
        <>
          <Box
            position="absolute"
            top="-28px"
            left="16px"
            zIndex={10}
            sx={{
              animation: showSuggestion
                ? 'appearFromBottom 0.3s ease-out forwards'
                : 'disappearToBottom 0.3s ease-in forwards',
              ...animationAppearDissapearKeyframes,
            }}
            pointerEvents="none"
          >
            <Box
              position="relative"
              bg="white"
              px={2}
              py={1}
              borderRadius="md"
              overflow="hidden"
              sx={borderAnimationSx}
            >
              <Text
                fontSize="xs"
                color="gray.500"
                fontWeight="medium"
                position="relative"
                zIndex={1}
              >
                Pressione Tab ⇥ para utilizar a sugestão
              </Text>
            </Box>
          </Box>
          <Text
            position="absolute"
            top="8px"
            left="16px"
            fontSize="md"
            pointerEvents="none"
            zIndex={1}
            whiteSpace="pre-wrap"
            sx={{
              maxHeight: '80px',
              overflow: 'hidden',
              color: 'gray.400',
              animation: !isTypingAnimation
                ? 'blinkSuggestion 2s ease-in-out infinite'
                : 'none',
              '@keyframes blinkSuggestion': {
                '0%, 100%': {
                  color: 'gray.300',
                },
                '50%': {
                  color: 'gray.500',
                },
              },
            }}
          >
            {displayedSuggestion}
            {isTypingAnimation && (
              <Box
                as="span"
                sx={{
                  animation: 'typingCursor 1s ease-in-out infinite',
                  '@keyframes typingCursor': {
                    '0%, 50%': {
                      opacity: 1,
                    },
                    '51%, 100%': {
                      opacity: 0,
                    },
                  },
                }}
              >
                |
              </Box>
            )}
          </Text>
        </>
      )}
    </>
  );
}

export default AiSuggestionBox;
