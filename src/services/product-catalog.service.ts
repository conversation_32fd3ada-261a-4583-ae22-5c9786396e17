import { AxiosResponse } from 'axios';
import { apiRoutes } from '../constants/api-routes';
import { request } from '../constants/request';
import { MetaProductCatalog } from '../types/MetaProductCatalog';

const listCatalogByCompany = async (): Promise<
  AxiosResponse<MetaProductCatalog>
> => {
  return request.get(apiRoutes.listCatalogByCompany());
};

export const ProductCatalogService = {
  listCatalogByCompany,
};
