import {
  <PERSON>,
  But<PERSON>,
  <PERSON>ing,
  Table,
  TableContainer,
  Tag,
  Tbody,
  Td,
  Thead,
  Tr,
  Tooltip,
  Flex,
  Text,
  Icon,
} from '@chakra-ui/react';
import { InfoIcon } from '@chakra-ui/icons';
import { useQuery } from 'react-query';
import { useNavigate } from 'react-router-dom';
import { Th } from '../../../components/CustomTable';
import { apiRoutes } from '../../../constants/api-routes';
import { appPaths } from '../../../constants/app-paths';
import {
  IntegrationsService,
  IntegrationSummayOptions,
} from '../../../services/integrations.service';
import { useAppModuleAccessGuard } from '../../../hooks/useAppModuleAccessGuard';
import { colors } from '../../../constants/colors';

enum Integrations {
  ORDERS = 'Pedidos',
  ABANDONED_CARTS = 'Carrinho abandonado',
  TRACKING_CODE = 'Código de rastreio',
  WELCOME_REGISTRATION = 'Novo cadastro',
  NEW_ORDER = 'Novo pedido',
  ORDER_CONFIRMATION = 'Confirmação de pedido',
  ORDER_PAYMENT_CONFIRMATION = 'Confirmação de pagamento',
  ORDER_STATUS_UPDATE = 'Status de Pedido',
  PRODUCTS = 'Produtos',
}

const integrationColors = {
  [Integrations.ORDERS]: 'blue',
  [Integrations.ABANDONED_CARTS]: 'orange',
  [Integrations.TRACKING_CODE]: 'green',
  [Integrations.WELCOME_REGISTRATION]: 'teal',
  [Integrations.NEW_ORDER]: 'purple',
  [Integrations.ORDER_CONFIRMATION]: 'cyan',
  [Integrations.ORDER_PAYMENT_CONFIRMATION]: 'pink',
  [Integrations.ORDER_STATUS_UPDATE]: 'yellow',
  [Integrations.PRODUCTS]: 'blue',
};

const cartSyncTimes: Record<string, string> = {
  Shopify:
    'Tempo real via webhook e polling das 10h às 23h nos minutos 15 e 45 - UTC',
  Vtex: 'Tempo real via webhook - UTC',
  'Loja Integrada': 'Sincronização às 13h15 e 21h15 - UTC',
  CartPanda: 'Sincronização a cada 30 minutos das 11h às 23h30 - UTC',
  Omie: 'Sincronização a cada 2 horas das 10h às 22h - UTC',
  Tray: 'Sincronização às 13h25 e 21h25 - UTC',
  Unbox: 'Sincronização no minuto 15 de cada hora das 12h às 00h - UTC',
  'Visual E-Commerce': 'Sincronização às 13h30, 15h30, 18h30 e 21h30 - UTC',
  Magento: 'Sincronização a cada hora das 11h às 23h - UTC',
  NuvemShop:
    'Sincronização nos minutos 05 e 35 de cada hora das 09h às 18h - UTC',
  Shoppub: 'Sincronização a cada 30 minutos das 11h às 23h - UTC',
  Vnda: 'Sincronização a cada hora das 11h20 às 23h20 - UTC',
  'Google Tag Manager': 'Sincronização a cada hora das 11h10 às 23h10 - UTC',
  Yampi: 'Sincronização no minuto 40 de cada hora das 12h às 00h - UTC',
};

const integrationOptions: {
  name: string;
  integrations: Integrations[];
  path: string;
  statusKey: IntegrationSummayOptions;
}[] = [
  {
    name: 'Shopify',
    integrations: [
      Integrations.ORDERS,
      Integrations.ABANDONED_CARTS,
      Integrations.TRACKING_CODE,
      Integrations.ORDER_STATUS_UPDATE,
    ],
    path: appPaths.settings.integrationSettings.shopify(),
    statusKey: 'isShopifyActive',
  },
  {
    name: 'Vtex',
    integrations: [
      Integrations.ORDERS,
      Integrations.ABANDONED_CARTS,
      Integrations.TRACKING_CODE,
      Integrations.ORDER_STATUS_UPDATE,
    ],
    path: appPaths.settings.integrationSettings.vtex(),
    statusKey: 'isVtexActive',
  },
  {
    name: 'Bling',
    integrations: [Integrations.ORDERS, Integrations.TRACKING_CODE],
    path: appPaths.settings.integrationSettings.bling(),
    statusKey: 'isBlingActive',
  },
  {
    name: 'Loja Integrada',
    integrations: [
      Integrations.ORDERS,
      Integrations.ABANDONED_CARTS,
      Integrations.TRACKING_CODE,
      Integrations.ORDER_STATUS_UPDATE,
    ],
    path: appPaths.settings.integrationSettings.lojaIntegrada(),
    statusKey: 'isLojaIntegradaActive',
  },
  {
    name: 'CartPanda',
    integrations: [
      Integrations.ORDERS,
      Integrations.ABANDONED_CARTS,
      Integrations.TRACKING_CODE,
    ],
    path: appPaths.settings.integrationSettings.cartPanda(),
    statusKey: 'isCartPandaActive',
  },
  {
    name: 'Omny',
    integrations: [Integrations.ORDERS, Integrations.ORDER_STATUS_UPDATE],
    path: appPaths.settings.integrationSettings.omny(),
    statusKey: 'isOmnyActive',
  },
  {
    name: 'Omie',
    integrations: [
      Integrations.ORDERS,
      Integrations.TRACKING_CODE,
      Integrations.ORDER_STATUS_UPDATE,
    ],
    path: appPaths.settings.integrationSettings.omie(),
    statusKey: 'isOmieActive',
  },
  {
    name: 'Tray',
    integrations: [
      Integrations.ORDERS,
      Integrations.TRACKING_CODE,
      Integrations.ORDER_STATUS_UPDATE,
      Integrations.ABANDONED_CARTS,
    ],
    path: appPaths.settings.integrationSettings.tray(),
    statusKey: 'isTrayActive',
  },
  {
    name: 'Unbox',
    integrations: [Integrations.ABANDONED_CARTS],
    path: appPaths.settings.integrationSettings.unbox(),
    statusKey: 'isUnboxActive',
  },
  {
    name: 'Visual E-Commerce',
    integrations: [Integrations.ORDERS, Integrations.ABANDONED_CARTS],
    path: appPaths.settings.integrationSettings.visualECommerce(),
    statusKey: 'isVisualECommerceActive',
  },
  {
    name: 'Magazord',
    integrations: [Integrations.ORDERS],
    path: appPaths.settings.integrationSettings.magazord(),
    statusKey: 'isMagazordActive',
  },
  {
    name: 'Magento',
    integrations: [Integrations.ORDERS, Integrations.ABANDONED_CARTS],
    path: appPaths.settings.integrationSettings.magento(),
    statusKey: 'isMagentoActive',
  },
  {
    name: 'Linx Commerce',
    integrations: [Integrations.ORDERS, Integrations.ORDER_STATUS_UPDATE],
    path: appPaths.settings.integrationSettings.linxCommerce(),
    statusKey: 'isLinxCommerceActive',
  },
  {
    name: 'NuvemShop',
    integrations: [
      Integrations.ORDERS,
      Integrations.TRACKING_CODE,
      Integrations.ABANDONED_CARTS,
      Integrations.ORDER_STATUS_UPDATE,
    ],
    path: appPaths.settings.integrationSettings.nuvemShop(),
    statusKey: 'isNuvemShopActive',
  },
  {
    name: 'Shoppub',
    integrations: [
      Integrations.ORDERS,
      Integrations.ABANDONED_CARTS,
      Integrations.TRACKING_CODE,
      Integrations.ORDER_STATUS_UPDATE,
    ],
    path: appPaths.settings.integrationSettings.shoppub(),
    statusKey: 'isShoppubActive',
  },
  {
    name: 'Tiny',
    integrations: [
      Integrations.ORDERS,
      Integrations.TRACKING_CODE,
      Integrations.ORDER_STATUS_UPDATE,
    ],
    path: appPaths.settings.integrationSettings.tiny(),
    statusKey: 'isTinyActive',
  },
  {
    name: 'Vnda',
    integrations: [
      Integrations.ORDERS,
      Integrations.TRACKING_CODE,
      Integrations.ABANDONED_CARTS,
    ],
    path: appPaths.settings.integrationSettings.vnda(),
    statusKey: 'isVndaActive',
  },
  {
    name: 'Millennium',
    integrations: [Integrations.ORDERS, Integrations.ORDER_STATUS_UPDATE],
    path: appPaths.settings.integrationSettings.millennium(),
    statusKey: 'isMillenniumActive',
  },
  {
    name: 'Google Tag Manager',
    integrations: [Integrations.ABANDONED_CARTS],
    path: appPaths.settings.integrationSettings.googleTagManager(),
    statusKey: 'isGoogleTagManagerActive',
  },
  {
    name: 'Varejo Online',
    integrations: [Integrations.ORDERS],
    path: appPaths.settings.integrationSettings.varejoOnline(),
    statusKey: 'isVarejoOnlineActive',
  },
  {
    name: 'Venda Ai',
    integrations: [Integrations.ORDERS],
    path: appPaths.settings.integrationSettings.vendaAi(),
    statusKey: 'isVendaAiActive',
  },
  {
    name: 'Irroba',
    integrations: [Integrations.ORDERS],
    path: appPaths.settings.integrationSettings.irroba(),
    statusKey: 'isIrrobaActive',
  },
  {
    name: 'Wake Commerce',
    integrations: [Integrations.ORDERS, Integrations.TRACKING_CODE],
    path: appPaths.settings.integrationSettings.wakeCommerce(),
    statusKey: 'isWakeCommerceActive',
  },
  {
    name: 'Yampi',
    integrations: [Integrations.ABANDONED_CARTS],
    path: appPaths.settings.integrationSettings.yampi(),
    statusKey: 'isYampiActive',
  },
  {
    name: 'WooCommerce',
    integrations: [
      Integrations.ORDERS,
      Integrations.PRODUCTS,
      Integrations.ABANDONED_CARTS,
    ],
    path: appPaths.settings.integrationSettings.wooCommerce(),
    statusKey: 'isWooCommerceActive',
  },
  {
    name: 'Uappi',
    integrations: [Integrations.ORDERS, Integrations.ABANDONED_CARTS],
    path: appPaths.settings.integrationSettings.uappi(),
    statusKey: 'isUappiActive',
  },
];

const IntegrationSettingsPage = () => {
  const { checkUserHasPathAccess } = useAppModuleAccessGuard();
  const navigate = useNavigate();
  const { data: integrationStatus } = useQuery(
    apiRoutes.getIntegrationStatusSummary(),
    async () => {
      const { data } = await IntegrationsService.getIntegrationStatusSummary();
      return data;
    },
  );

  const renderIntegrationTags = (
    integrations: Integrations[],
    platformName: string,
  ) => {
    const MAX_VISIBLE_TAGS = 2;
    const visibleIntegrations = integrations.slice(0, MAX_VISIBLE_TAGS);
    const remainingCount = integrations.length - MAX_VISIBLE_TAGS;

    return (
      <Flex wrap="wrap" gap={2}>
        {visibleIntegrations.map((integration) => {
          const isAbandonedCart = integration === Integrations.ABANDONED_CARTS;
          const syncTime = cartSyncTimes[platformName];

          return (
            <Tooltip
              key={integration}
              label={
                isAbandonedCart ? (
                  <Box p={2} maxW="300px">
                    <Text fontWeight="bold" mb={1}>
                      Sincronização do Carrinho Abandonado
                    </Text>
                    <Text fontSize="sm">{syncTime}</Text>
                  </Box>
                ) : undefined
              }
              placement="top"
              hasArrow
              isDisabled={!isAbandonedCart}
            >
              <Tag
                colorScheme={integrationColors[integration] || 'purple'}
                mr={2}
                cursor={isAbandonedCart ? 'help' : 'default'}
                display="flex"
                alignItems="center"
                gap={1}
              >
                {integration}
                {isAbandonedCart && <Icon as={InfoIcon} boxSize={3} ml={1} />}
              </Tag>
            </Tooltip>
          );
        })}

        {remainingCount > 0 && (
          <Tooltip
            label={
              <Box p={1}>
                {integrations.slice(MAX_VISIBLE_TAGS).map((integration) => {
                  const isAbandonedCart =
                    integration === Integrations.ABANDONED_CARTS;
                  const syncTime = cartSyncTimes[platformName];

                  return (
                    <Tooltip
                      key={integration}
                      label={
                        isAbandonedCart ? (
                          <Box p={2} maxW="300px">
                            <Text fontWeight="bold" mb={1}>
                              Sincronização do Carrinho Abandonado
                            </Text>
                            <Text fontSize="sm">{syncTime}</Text>
                          </Box>
                        ) : undefined
                      }
                      placement="left"
                      hasArrow
                      isDisabled={!isAbandonedCart}
                    >
                      <Tag
                        colorScheme={integrationColors[integration] || 'purple'}
                        mr={1}
                        mb={1}
                        cursor={isAbandonedCart ? 'help' : 'default'}
                        display="flex"
                        alignItems="center"
                        gap={1}
                      >
                        {integration}
                        {isAbandonedCart && (
                          <Icon as={InfoIcon} boxSize={3} ml={1} />
                        )}
                      </Tag>
                    </Tooltip>
                  );
                })}
              </Box>
            }
            placement="top"
            hasArrow
          >
            <Tag colorScheme="gray">+{remainingCount}</Tag>
          </Tooltip>
        )}
      </Flex>
    );
  };

  return (
    <Box>
      <Heading mb={5}>Integrações</Heading>
      <Box mt={6}>
        <TableContainer
          borderRadius="md"
          border="1px solid"
          borderColor={colors.border}
        >
          <Table variant="simple">
            <Thead>
              <Tr>
                <Th>Plataforma</Th>
                <Th>Integrações</Th>
                <Th>Status</Th>
                <Th> </Th>
              </Tr>
            </Thead>
            <Tbody>
              {integrationOptions.map((integration) => {
                if (!checkUserHasPathAccess(integration.path)) return null;
                return (
                  <Tr key={integration.name} bg={colors.white}>
                    <Td>{integration.name}</Td>
                    <Td>
                      {renderIntegrationTags(
                        integration.integrations,
                        integration.name,
                      )}
                    </Td>
                    <Td>
                      {integrationStatus?.[integration.statusKey] ? (
                        <Tag colorScheme="green">Ativo</Tag>
                      ) : (
                        <Tag colorScheme="red">Inativo</Tag>
                      )}
                    </Td>
                    <Td>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => navigate(integration.path)}
                      >
                        Configurar
                      </Button>
                    </Td>
                  </Tr>
                );
              })}
            </Tbody>
          </Table>
        </TableContainer>
      </Box>
    </Box>
  );
};

export default IntegrationSettingsPage;
