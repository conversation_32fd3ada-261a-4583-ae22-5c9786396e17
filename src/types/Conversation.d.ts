import { ConversationTicket } from './ConversationTicket';
import { Customer } from './Customer';
import { Message } from './Message';
import Prisma from './Prisma';
import { MessageStatus } from './Message';

export interface Conversation extends Prisma.Conversation {
  createdAt: string;
  updatedAt: string;
}
interface ConversationWithIncludesCustomer extends Customer {
  customerTags: {
    tag: {
      id: string;
      name: string;
    };
  }[];
}

export interface ConversationWithIncludes extends Conversation {
  conversationTickets: ConversationTicket[];
  messages: Message[];
  company: Pick<Company, 'gupshupAppName' | 'isAutomaticSortingActive'>;
  customer: ConversationWithIncludesCustomer;
}

export interface ListConversationItem extends Conversation {
  conversationTickets: ConversationTicket[];
  messages: Message[];
  customer: Pick<Customer, 'isOptedOut'>;
}

export interface ListConversationDetailedItem {
  id: string;
  recipientName: string;
  lastMessage?: Pick<
    Message,
    'createdAt' | 'fromSystem' | 'text' | 'status' | 'id' | 'tempId'
  >;
  hasOpenTicket: boolean;
  categoryId: string | null;
  totalRows?: number;
  ticketAgentId: string | null;
  messageContexts?: { contextMessage: ListConversationDetailedItem }[];
  createdAt?: string;
  status?: MessageStatus;
  text?: string;
  fromSystem?: boolean;
  mediaType?: MediaType | null;
  mediaUrl?: string | null;
  fileKey?: string | null;
}

export interface ListConversationCustomerItem extends Conversation {
  _count: {
    conversationTickets: number;
  };
  conversationTickets: Pick<ConversationTicket, 'createdAt' | 'status'>[];
}

export interface SelectableListConversationDetailedItem
  extends ListConversationDetailedItem {
  isSelected?: boolean;
}
