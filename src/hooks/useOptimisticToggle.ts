import { useMutation, useQueryClient } from 'react-query';
import { useToast } from '@chakra-ui/react';
import { User } from '../types/Prisma';

interface UseOptimisticToggleProps {
  queryKey: string;
  mutationFn: (userId: string) => Promise<any>;
  field: keyof User;
  successMessage: {
    titleTrue: string;
    titleFalse: string;
    getDescription: (name: string, isActive: boolean) => string;
  };
  errorMessage: string;
}

export const useOptimisticToggle = ({
  queryKey,
  mutationFn,
  field,
  successMessage,
  errorMessage,
}: UseOptimisticToggleProps) => {
  const toast = useToast();
  const queryClient = useQueryClient();

  return useMutation(mutationFn, {
    onMutate: async (userId: string) => {
      await queryClient.cancelQueries(queryKey);

      const previousData = queryClient.getQueryData<User[]>(queryKey);

      queryClient.setQueryData<User[]>(queryKey, (old = []) =>
        old.map((item) =>
          item.id === userId ? { ...item, [field]: !item[field] } : item,
        ),
      );

      return { previousData };
    },

    onError: (err, userId, context) => {
      if (context?.previousData) {
        queryClient.setQueryData(queryKey, context.previousData);
      }
      toast({
        title: 'Erro',
        description: errorMessage,
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    },

    onSuccess: ({ data }) => {
      const isActive = data[field] as boolean;
      toast({
        title: isActive ? successMessage.titleTrue : successMessage.titleFalse,
        description: successMessage.getDescription(data.name, isActive),
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    },

    onSettled: () => {
      queryClient.invalidateQueries(queryKey);
    },
  });
};
