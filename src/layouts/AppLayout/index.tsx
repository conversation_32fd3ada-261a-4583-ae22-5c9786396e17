import { Grid, GridItem, Show } from '@chakra-ui/react';
import React, { ReactNode } from 'react';
import Sidebar from '../../components/Sidebar';
import { useSidebar } from '../../hooks/useSidebar';
import { usePasswordChangeRequired } from '../../hooks/usePasswordChangeRequired';
import LoadingScreen from '../../components/LoadingScreen';

interface AppLayoutProps {
  children: ReactNode;
  background?: string;
}

const AppLayout = ({ children, background }: AppLayoutProps) => {
  const { sidebarWidth, isMobile } = useSidebar();
  const { isPasswordChangeRequired, isLoadingRequestUpdatePasswordToken } =
    usePasswordChangeRequired();

  return (
    <LoadingScreen isLoading={isLoadingRequestUpdatePasswordToken}>
      <Grid
        height={'100vh'}
        templateColumns={
          isMobile || isPasswordChangeRequired ? 'auto' : `${sidebarWidth} auto`
        }
        templateAreas={
          isMobile || isPasswordChangeRequired ? "'page'" : "'sidebar page'"
        }
        bg={background || 'white'}
        transition="grid-template-columns 0.3s ease"
      >
        {!isPasswordChangeRequired && (
          <Show above="lg">
            <GridItem area="sidebar">
              <Sidebar />
            </GridItem>
          </Show>
        )}
        <GridItem area="page" maxH={'100vh'} height={'100vh'} overflow="auto">
          {children}
        </GridItem>
      </Grid>
    </LoadingScreen>
  );
};

export default AppLayout;
