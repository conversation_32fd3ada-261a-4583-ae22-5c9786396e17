export enum ComparisonType {
  EQUALS = 'equals',
  CONTAINS = 'contains',
  NOT_CONTAINS = 'not_contains',
  GREATER_THAN = 'greater_than',
  LESS_THAN = 'less_than',
  GREATER_THAN_OR_EQUAL = 'greater_than_or_equal',
  LESS_THAN_OR_EQUAL = 'less_than_or_equal',
}

export type DataType = 'string' | 'number' | 'date' | 'boolean';

export interface ComparisonOption {
  value: ComparisonType;
  label: string;
}

const COMPARISON_OPTIONS: ComparisonOption[] = [
  { value: ComparisonType.EQUALS, label: 'Igual a' },
  { value: ComparisonType.CONTAINS, label: 'Contém' },
  { value: ComparisonType.GREATER_THAN, label: 'Maior que' },
  { value: ComparisonType.LESS_THAN, label: 'Menor que' },
  { value: ComparisonType.GREATER_THAN_OR_EQUAL, label: '<PERSON><PERSON> ou igual a' },
  { value: ComparisonType.LESS_THAN_OR_EQUAL, label: '<PERSON>or ou igual a' },
];

const DATA_TYPE_TO_COMPARISON_MAP: Record<DataType, ComparisonType[]> = {
  string: [ComparisonType.EQUALS, ComparisonType.CONTAINS],
  number: [
    ComparisonType.EQUALS,
    ComparisonType.GREATER_THAN,
    ComparisonType.LESS_THAN,
    ComparisonType.GREATER_THAN_OR_EQUAL,
    ComparisonType.LESS_THAN_OR_EQUAL,
  ],
  date: [
    ComparisonType.EQUALS,
    ComparisonType.GREATER_THAN,
    ComparisonType.LESS_THAN,
    ComparisonType.GREATER_THAN_OR_EQUAL,
    ComparisonType.LESS_THAN_OR_EQUAL,
  ],
  boolean: [ComparisonType.EQUALS],
};

export function getComparisonOptionsForDataType(
  dataType: DataType,
): ComparisonOption[] {
  const validComparisons = DATA_TYPE_TO_COMPARISON_MAP[dataType] ?? [];
  return COMPARISON_OPTIONS.filter((option) =>
    validComparisons.includes(option.value),
  );
}

export const ComparisonTypesUtils = {
  getComparisonOptionsForDataType,
};
