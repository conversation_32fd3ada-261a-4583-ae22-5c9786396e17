function roundToNearestPowerOfTen(number: number) {
  const power = Math.floor(Math.log10(number));
  const multiplier = 10 ** power;
  return Math.ceil(number / multiplier) * multiplier;
}

function getPercentValue(value?: number, total?: number) {
  return `${(((value || 0) / (total || 1)) * 100).toFixed(2)}%`;
}

function isValidNumericFilter(value: any): value is number {
  if (value === undefined || value === null || value === '') {
    return false;
  }
  const num = Number(value);
  return !isNaN(num);
}

export const NumberUtils = {
  roundToNearestPowerOfTen,
  getPercentValue,
  isValidNumericFilter,
};
