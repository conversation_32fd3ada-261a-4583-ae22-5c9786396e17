import {
  useDisclosure,
  <PERSON>,
  Flex,
  Drawer,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>er<PERSON>ontent,
  <PERSON>er<PERSON><PERSON><PERSON>,
  Drawer<PERSON>loseButton,
  DrawerBody,
  Text,
} from '@chakra-ui/react';
import OrderItemDetail from './OrderItemDetail';
import { WhatsappOrder, WhatsappOrderItem } from '../../types/Message';
import ButtonWhatsapp from '../ButtonWhatsapp';
import { MoneyUtils } from '../../utils/money.utils';
interface CardOrderProps {
  order: WhatsappOrder;
}

const CardOrder = ({ order }: CardOrderProps) => {
  const { isOpen, onOpen, onClose } = useDisclosure();

  return (
    <>
      <Box
        bg="#dcf8c6"
        borderRadius="8px"
        p={3}
        maxWidth="280px"
        position="relative"
      >
        <Text fontSize="14px" fontWeight="500" color="#000" mb={2}>
          Pedido feito via catálogo
        </Text>

        <Flex alignItems="center" mb={3}>
          <Box
            bg="white"
            borderRadius="6px"
            p={2}
            mr={3}
            width="60px"
            height="60px"
            display="flex"
            alignItems="center"
            justifyContent="center"
          >
            <Text fontSize="24px">🛒</Text>
          </Box>

          <Box flex={1}>
            <Flex alignItems="center" mb={1}>
              <Text fontSize="14px" fontWeight="500" color="#000">
                {(order.whatsappOrderItems?.length || 0) === 1
                  ? 'Item'
                  : 'Itens'}
                : {order.whatsappOrderItems?.length || 0}
              </Text>
            </Flex>

            <Text fontSize="14px" color="#54656f">
              Subtotal: {MoneyUtils.formatCurrency(order.totalCents)}
            </Text>
          </Box>
        </Flex>

        <ButtonWhatsapp onClick={onOpen}>Mostrar detalhes</ButtonWhatsapp>
      </Box>

      <Drawer isOpen={isOpen} placement="right" onClose={onClose} size="sm">
        <DrawerOverlay />
        <DrawerContent>
          <DrawerCloseButton />
          <DrawerHeader>
            <Box>
              <Text fontSize="18px" fontWeight="600">
                Detalhes do Pedido
              </Text>
              <Text fontSize="12px" color="#54656f" fontWeight="400">
                ID: {order.id}
              </Text>
            </Box>
          </DrawerHeader>

          <DrawerBody p={0}>
            <Box p={4}>
              <Flex justifyContent="space-between" mb={2}>
                <Text fontSize="14px" color="#54656f">
                  Data do pedido:
                </Text>
                <Text fontSize="14px" fontWeight="500">
                  {new Date(order.createdAt).toLocaleDateString('pt-BR', {
                    day: '2-digit',
                    month: '2-digit',
                    year: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit',
                  })}
                </Text>
              </Flex>

              <Flex justifyContent="space-between">
                <Text fontSize="14px" color="#54656f">
                  Total de itens:
                </Text>
                <Text fontSize="14px" fontWeight="500">
                  {order.whatsappOrderItems?.length || 0}
                </Text>
              </Flex>
            </Box>

            <Box maxHeight="calc(100vh - 220px)" overflowY="auto">
              <Box p={3}>
                <Text fontSize="16px" fontWeight="500" mb={2}>
                  Itens do pedido
                </Text>
              </Box>

              {order.whatsappOrderItems &&
              order.whatsappOrderItems.length > 0 ? (
                order.whatsappOrderItems.map(
                  (item: WhatsappOrderItem, index: number) => (
                    <OrderItemDetail key={index} item={item} />
                  ),
                )
              ) : (
                <Box p={4} textAlign="center">
                  <Text fontSize="14px" color="#54656f">
                    Nenhum item encontrado neste pedido
                  </Text>
                </Box>
              )}
            </Box>

            <Box
              p={4}
              borderTop="1px solid #e0e0e0"
              bg="#f8f9fa"
              position="sticky"
              bottom={0}
              mt="auto"
            >
              <Flex justifyContent="space-between" alignItems="center">
                <Text fontSize="16px" fontWeight="600" color="#000">
                  Total do pedido:
                </Text>
                <Text fontSize="18px" fontWeight="700" color="#00a884">
                  {MoneyUtils.formatCurrency(order.totalCents || 0)}
                </Text>
              </Flex>
            </Box>
          </DrawerBody>
        </DrawerContent>
      </Drawer>
    </>
  );
};

export default CardOrder;
