import { BoxProps } from '@chakra-ui/react';
import NoImage from './NoImage';
import ImageWithTooltip from './ImageWithTooltip';

interface ProductImageProps extends BoxProps {
  src?: string;
  alt?: string;
  showTooltip?: boolean;
  tooltipSize?: string;
}

const ProductImage = ({
  src,
  alt = 'Imagem do produto',
  showTooltip = true,
  tooltipSize = '300px',
  ...boxProps
}: ProductImageProps) => {
  if (!src) {
    return <NoImage showTooltip={showTooltip} {...boxProps} />;
  }

  return (
    <ImageWithTooltip
      src={src}
      alt={alt}
      showTooltip={showTooltip}
      tooltipSize={tooltipSize}
      {...boxProps}
    />
  );
};

export default ProductImage;
