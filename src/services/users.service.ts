import { AxiosResponse } from 'axios';
import { request } from '../constants/request';
import { User } from '../types/Prisma';
import { apiRoutes } from '../constants/api-routes';

const listCompanyAgents = async (options?: {
  skipInactiveUsers?: boolean;
  skipAiAgents?: boolean;
}): Promise<AxiosResponse<User[]>> => {
  const { skipInactiveUsers = true, skipAiAgents = true } = options || {};
  return request.get(
    apiRoutes.listCompanyAgents({ skipInactiveUsers, skipAiAgents }),
  );
};

const listUsers = async (): Promise<AxiosResponse<User[]>> => {
  return request.get(apiRoutes.listUsers());
};

const toggleUserStatus = async (
  userId: string,
): Promise<AxiosResponse<User>> => {
  return request.put(apiRoutes.toggleUserStatus(userId));
};

const toggleUserCanViewAllConversations = async (
  userId: string,
): Promise<AxiosResponse<User>> => {
  return request.put(apiRoutes.toggleUserCanViewAllConversations(userId));
};

export const UsersService = {
  listCompanyAgents,
  listUsers,
  toggleUserStatus,
  toggleUserCanViewAllConversations,
};
