import {
  Box,
  Text,
  Table,
  TableContainer,
  Tbody,
  Td,
  Th,
  Thead,
  Tr,
  Checkbox,
  IconButton,
} from '@chakra-ui/react';
import { FaEye, FaEdit, FaTrash } from 'react-icons/fa';
import { Faq } from '../../../../../services/faq.service';
import { colors } from '../../../../../constants/colors';

interface FaqTableProps {
  faqs: Faq[];
  selectedFaqIds: string[];
  onToggleSelectAll: () => void;
  onToggleSelectOne: (id: string) => void;
  onViewFaq: (faq: Faq) => void;
  onEditFaq: (faq: Faq) => void;
  onDeleteFaq: (faqId: string) => void;
}

export const FaqTable = ({
  faqs,
  selectedFaqIds,
  onToggleSelectAll,
  onToggleSelectOne,
  onViewFaq,
  onEditFaq,
  onDeleteFaq,
}: FaqTableProps) => {
  const isChecked = selectedFaqIds.length > 0;
  const isIndeterminate =
    selectedFaqIds.length > 0 && selectedFaqIds.length < faqs.length;

  return (
    <TableContainer
      borderRadius="md"
      border="1px solid"
      borderColor={colors.border}
    >
      <Table variant="simple" style={{ tableLayout: 'fixed' }}>
        <Thead>
          <Tr display="flex" width="100%" bgColor={colors.slateSuperLight}>
            <Th width="5%">
              <Checkbox
                isChecked={isChecked}
                onChange={onToggleSelectAll}
                isIndeterminate={isIndeterminate}
              />
            </Th>
            <Th width="35%">Pergunta</Th>
            <Th width="45%">Resposta</Th>
            <Th width="15%" display="flex" justifyContent="center">
              Ações
            </Th>
          </Tr>
        </Thead>
        <Tbody>
          {faqs.map((faq) => (
            <Tr display="flex" width="100%" key={faq.id} bg={colors.white}>
              <Td width="5%">
                <Checkbox
                  isChecked={selectedFaqIds.includes(faq.id)}
                  onChange={() => onToggleSelectOne(faq.id)}
                />
              </Td>
              <Td width="35%">
                <Text noOfLines={2} title={faq.question}>
                  {faq.question}
                </Text>
              </Td>
              <Td width="45%">
                <Text noOfLines={3} title={faq.answer}>
                  {faq.answer}
                </Text>
              </Td>
              <Td
                width="15%"
                display="flex"
                align="center"
                justifyContent="center"
              >
                <IconButton
                  aria-label="Ver FAQ"
                  icon={<FaEye fontSize="20px" color={colors.darkGrey} />}
                  size="sm"
                  variant="ghost"
                  onClick={() => onViewFaq(faq)}
                />
                <IconButton
                  aria-label="Editar FAQ"
                  icon={<FaEdit fontSize="20px" color={colors.darkGrey} />}
                  size="sm"
                  variant="ghost"
                  onClick={() => onEditFaq(faq)}
                />
                <IconButton
                  aria-label="Excluir FAQ"
                  icon={<FaTrash fontSize="20px" color={colors.darkGrey} />}
                  size="sm"
                  variant="ghost"
                  onClick={() => onDeleteFaq(faq.id)}
                />
              </Td>
            </Tr>
          ))}
        </Tbody>
      </Table>
    </TableContainer>
  );
};
