import {
  Box,
  Button,
  Checkbox,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  Input,
  Stack,
  Text,
  useToast,
} from '@chakra-ui/react';
import { yupResolver } from '@hookform/resolvers/yup';
import { useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { FaShopify } from 'react-icons/fa';
import { useMutation, useQuery } from 'react-query';
import { ShopifyService } from '../../../../services/shopify.service';
import * as yup from 'yup';
import { colors } from '../../../../constants/colors';
import { apiRoutes } from '../../../../constants/api-routes';
import {
  IntegrationsService,
  SaveShopifyCredentialsDto,
  SaveIntegrationsConfigDto,
  IntegrationsConfigDto,
} from '../../../../services/integrations.service';
import { SourceIntegration } from '../../../../types/SourceIntegration';
import IntegrationsTable, {
  IntegrationsTableColumn,
} from '../../../../components/IntegrationsTable';
import { get } from 'http';

const createSchema = (isEditing: boolean) =>
  yup.object({
    description: yup.string().default('').required('A descrição é obrigatória'),
    shopifyShopName: yup
      .string()
      .default('')
      .required("'Shop Name' é obrigatório"),
    shopifyAdminAccessToken: isEditing
      ? yup.string().default('')
      : yup.string().default('').required("'Admin Access Token' é obrigatório"),
    shopifyApiKey: isEditing
      ? yup.string().default('')
      : yup.string().default('').required("'API Key' é obrigatória"),
    shopifyApiSecretKey: isEditing
      ? yup.string().default('')
      : yup.string().default('').required("'API Secret' é obrigatória"),
    shopifyStorefrontAccessToken: yup.string().default('').optional(),
    isOrderActive: yup.boolean().default(false),
    isAbandonedCartActive: yup.boolean().default(false),
    isProductActive: yup.boolean().default(false),
    isActive: yup.boolean().default(true),
  });

const ShopifyIntegrationPage = () => {
  const toast = useToast();
  const [showForm, setShowForm] = useState(false);
  const [editingIntegrationId, setEditingIntegrationId] = useState<
    string | null
  >(null);

  // Configuração das colunas da tabela - para Shopify, mostramos pedidos e carrinhos abandonados
  const tableColumns: IntegrationsTableColumn[] = [
    { key: 'isOrderActive', label: 'Sinc. de Pedidos', show: true },
    { key: 'isProductActive', label: 'Sinc. de Produtos', show: true },
    {
      key: 'isAbandonedCartActive',
      label: 'Sinc. de Carrinhos Abandonados',
      show: true,
    },
  ];

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    getValues,
    setValue,
    reset,
  } = useForm({
    resolver: yupResolver(createSchema(!!editingIntegrationId)),
  });

  const { data: integrationsConfig, refetch: refetchIntegrationsConfig } =
    useQuery(
      apiRoutes.getIntegrationsConfig(SourceIntegration.shopify_ecommerce),
      async () => {
        const { data } = await IntegrationsService.getIntegrationsConfig(
          SourceIntegration.shopify_ecommerce,
        );
        return data;
      },
    );

  const saveShopifyCredentials = useMutation(
    (data: SaveIntegrationsConfigDto<SaveShopifyCredentialsDto>) =>
      IntegrationsService.saveIntegrationConfig(data),
    {
      onSuccess: () => {
        refetchIntegrationsConfig();
        toast({
          title: 'Configurações salvas com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      },
    },
  );

  const syncShopifyOrdersByIntegration = useMutation(
    (integrationId: string) =>
      ShopifyService.syncShopifyOrdersByIntegration(integrationId),
    {
      onSuccess: () => {
        toast({
          title: 'Sincronização com Shopify realizada com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      },
    },
  );

  async function handleSaveData() {
    const config: SaveShopifyCredentialsDto = {
      shopifyShopName: getValues('shopifyShopName') || '',
      shopifyAdminAccessToken: getValues('shopifyAdminAccessToken') || '',
      shopifyApiKey: getValues('shopifyApiKey') || '',
      shopifyApiSecretKey: getValues('shopifyApiSecretKey') || '',
      shopifyStorefrontAccessToken:
        getValues('shopifyStorefrontAccessToken') || '',
    };

    const data = {
      id: editingIntegrationId || undefined,
      source: SourceIntegration.shopify_ecommerce,
      description: getValues('description'),
      config,
      isActive: !!getValues('isActive'),
      isAbandonedCartActive: !!getValues('isAbandonedCartActive'),
      isProductActive: !!getValues('isProductActive'),
      isOrderActive: !!getValues('isOrderActive'),
      companyId: '',
    };

    await saveShopifyCredentials.mutateAsync(data);

    reset();
    setShowForm(false);
    setEditingIntegrationId(null);
  }

  async function onSubmit() {
    handleSaveData();
  }

  const handleEditIntegration = (integration: IntegrationsConfigDto) => {
    setShowForm(true);
    setValue('description', integration.description);
    setValue('isOrderActive', integration.isOrderActive);
    setValue('isAbandonedCartActive', integration.isAbandonedCartActive);
    setValue('isProductActive', integration.isProductActive);
    setValue('isActive', integration.isActive);

    const config = integration.config as SaveShopifyCredentialsDto;
    if (config) {
      setValue('shopifyShopName', config.shopifyShopName || '');
      setValue('shopifyAdminAccessToken', config.shopifyAdminAccessToken || '');
      setValue('shopifyApiKey', config.shopifyApiKey || '');
      setValue('shopifyApiSecretKey', ''); // Não carregar a senha por segurança
      setValue('shopifyStorefrontAccessToken', ''); // Não carregar por segurança
    }
    setEditingIntegrationId(integration.id);
  };

  return (
    <Box>
      <Heading size="md" mb={4}>
        Integração Shopify
      </Heading>
      {!showForm ? (
        <Flex justifyContent="flex-end" mb={4}>
          <Button
            colorScheme="green"
            onClick={() => setShowForm(true)}
            isLoading={saveShopifyCredentials.isLoading}
          >
            Adicionar Nova Integração
          </Button>
        </Flex>
      ) : (
        <Box mb={6}>
          <Heading size="sm" mb={4}>
            {editingIntegrationId
              ? 'Editar Integração'
              : 'Cadastrar Nova Integração'}
          </Heading>
          <form onSubmit={handleSubmit(onSubmit)}>
            <Stack spacing={4} mt={4}>
              <FormControl isRequired>
                <FormLabel>Descrição</FormLabel>
                <Input
                  placeholder="Descrição da integração"
                  {...register('description')}
                  isInvalid={!!errors.description?.message}
                />
                <Text color={colors.danger} fontSize="xs">
                  {errors.description?.message}
                </Text>
              </FormControl>

              <FormControl isRequired>
                <FormLabel>Shop Name</FormLabel>
                <Input
                  placeholder="Ex: minha-loja-shopify"
                  {...register('shopifyShopName')}
                  isInvalid={!!errors.shopifyShopName?.message}
                />
                <Text color={colors.danger} fontSize="xs">
                  {errors.shopifyShopName?.message}
                </Text>
              </FormControl>

              <FormControl isRequired={!editingIntegrationId}>
                <FormLabel>Admin Access Token</FormLabel>
                <Input
                  type="password"
                  placeholder={'************'}
                  sx={{
                    '&::placeholder': {
                      color: 'black',
                    },
                  }}
                  {...register('shopifyAdminAccessToken')}
                  isInvalid={!!errors.shopifyAdminAccessToken?.message}
                />
                <Text color={colors.danger} fontSize="xs">
                  {errors.shopifyAdminAccessToken?.message}
                </Text>
              </FormControl>

              <FormControl isRequired={!editingIntegrationId}>
                <FormLabel>API Key</FormLabel>
                <Input
                  type="password"
                  placeholder={'************'}
                  sx={{
                    '&::placeholder': {
                      color: 'black',
                    },
                  }}
                  {...register('shopifyApiKey')}
                  isInvalid={!!errors.shopifyApiKey?.message}
                />
                <Text color={colors.danger} fontSize="xs">
                  {errors.shopifyApiKey?.message}
                </Text>
              </FormControl>

              <FormControl isRequired={!editingIntegrationId}>
                <FormLabel>API Secret</FormLabel>
                <Input
                  type="password"
                  placeholder={'************'}
                  sx={{
                    '&::placeholder': {
                      color: 'black',
                    },
                  }}
                  {...register('shopifyApiSecretKey')}
                  isInvalid={!!errors.shopifyApiSecretKey?.message}
                />
                <Text color={colors.danger} fontSize="xs">
                  {errors.shopifyApiSecretKey?.message}
                </Text>
                {editingIntegrationId && (
                  <Text fontSize="xs" color="gray.500">
                    Deixe em branco para manter a chave atual
                  </Text>
                )}
              </FormControl>

              <FormControl>
                <FormLabel>Storefront Access Token (Opcional)</FormLabel>
                <Input
                  type="password"
                  placeholder={'************'}
                  sx={{
                    '&::placeholder': {
                      color: 'black',
                    },
                  }}
                  {...register('shopifyStorefrontAccessToken')}
                  isInvalid={!!errors.shopifyStorefrontAccessToken?.message}
                />
                <Text color={colors.danger} fontSize="xs">
                  {errors.shopifyStorefrontAccessToken?.message}
                </Text>
                <Text fontSize="xs" color="gray.500">
                  Token opcional para acessar a Storefront API do Shopify
                </Text>
                {editingIntegrationId && (
                  <Text fontSize="xs" color="gray.500">
                    Deixe em branco para manter o token atual
                  </Text>
                )}
              </FormControl>

              <FormControl>
                <Controller
                  name="isOrderActive"
                  control={control}
                  render={({ field }) => (
                    <Checkbox
                      isChecked={field.value}
                      onChange={(e) => field.onChange(e.target.checked)}
                    >
                      Sincronização de Pedidos Ativa
                    </Checkbox>
                  )}
                />
              </FormControl>

              <FormControl>
                <Controller
                  name="isAbandonedCartActive"
                  control={control}
                  render={({ field }) => (
                    <Checkbox
                      isChecked={field.value}
                      onChange={(e) => field.onChange(e.target.checked)}
                    >
                      Sincronização de Carrinhos Abandonados Ativa
                    </Checkbox>
                  )}
                />
              </FormControl>
              <FormControl>
                <Controller
                  name="isProductActive"
                  control={control}
                  render={({ field }) => (
                    <Checkbox
                      isChecked={field.value}
                      onChange={(e) => field.onChange(e.target.checked)}
                    >
                      Sincronização de Produtos Ativa
                    </Checkbox>
                  )}
                />
              </FormControl>

              <FormControl>
                <Controller
                  name="isActive"
                  control={control}
                  render={({ field }) => (
                    <Checkbox
                      isChecked={field.value}
                      onChange={(e) => field.onChange(e.target.checked)}
                    >
                      Integração Ativa
                    </Checkbox>
                  )}
                />
              </FormControl>

              <Flex justify="flex-end" gap={2}>
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowForm(false);
                    setEditingIntegrationId(null);
                    reset();
                  }}
                >
                  Cancelar
                </Button>
                <Button
                  type="submit"
                  colorScheme="blue"
                  isLoading={saveShopifyCredentials.isLoading}
                >
                  Salvar
                </Button>
              </Flex>
            </Stack>
          </form>
        </Box>
      )}

      <IntegrationsTable
        integrationsConfig={integrationsConfig}
        sourceIntegration={SourceIntegration.shopify_ecommerce}
        columns={tableColumns}
        onEdit={handleEditIntegration}
        isLoading={saveShopifyCredentials.isLoading}
        renderAdditionalActions={(integration) => (
          <Button
            size="sm"
            colorScheme="green"
            leftIcon={<FaShopify />}
            onClick={() =>
              syncShopifyOrdersByIntegration.mutateAsync(integration.id)
            }
            isLoading={syncShopifyOrdersByIntegration.isLoading}
            isDisabled={!integration.isActive || !integration.isOrderActive}
          >
            Sincronizar Pedidos
          </Button>
        )}
      />
    </Box>
  );
};

export default ShopifyIntegrationPage;
