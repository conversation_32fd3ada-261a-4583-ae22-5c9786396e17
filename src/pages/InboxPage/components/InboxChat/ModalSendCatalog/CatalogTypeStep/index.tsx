import React from 'react';
import {
  Center,
  VStack,
  Text,
  RadioGroup,
  Card,
  CardBody,
  HStack,
  Radio,
  Badge,
} from '@chakra-ui/react';
import { Controller, Control } from 'react-hook-form';
import {
  CATALOG_TYPES,
  CatalogFormData,
} from '../../../../../../types/Catalog';

interface CatalogTypeStepProps {
  control: Control<CatalogFormData>;
}

export const CatalogTypeStep = ({ control }: CatalogTypeStepProps) => {
  return (
    <Center flex="1">
      <VStack spacing={6} maxW="2xl" w="full">
        <VStack spacing={2} textAlign="center">
          <Text fontSize="lg" color="gray.700">
            Escolha o tipo de catálogo que melhor se adequa à sua estratégia
          </Text>
          <Text fontSize="sm" color="gray.500">
            Cada tipo tem características específicas para diferentes objetivos
          </Text>
        </VStack>

        <Controller
          name="type"
          control={control}
          render={({ field }) => (
            <RadioGroup value={field.value} onChange={field.onChange}>
              <VStack spacing={4} w="full">
                {Object.entries(CATALOG_TYPES).map(([key, config]) => (
                  <Card
                    key={key}
                    w="full"
                    variant={field.value === key ? 'filled' : 'outline'}
                    bg={field.value === key ? 'blue.50' : 'white'}
                    borderColor={field.value === key ? 'blue.200' : 'gray.200'}
                    cursor="pointer"
                    _hover={{
                      borderColor: 'blue.300',
                      transform: 'translateY(-2px)',
                    }}
                    transition="all 0.2s"
                    onClick={() => field.onChange(key)}
                  >
                    <CardBody p={6}>
                      <HStack spacing={4} align="start">
                        <Radio
                          value={key}
                          colorScheme="blue"
                          size="lg"
                          mt={1}
                        />
                        <VStack align="start" spacing={2} flex={1}>
                          <Text
                            fontWeight="bold"
                            fontSize="lg"
                            color="gray.800"
                          >
                            {config.label}
                          </Text>
                          <Text color="gray.600" fontSize="sm">
                            {config.description}
                          </Text>
                          <HStack spacing={4} mt={2}>
                            <Badge colorScheme="green" variant="subtle">
                              {config.maxProducts === 1
                                ? '1 produto'
                                : config.maxProducts === Infinity
                                  ? 'Ilimitado'
                                  : `Até ${config.maxProducts} produtos`}
                            </Badge>
                            {config.needsHeader && (
                              <Badge colorScheme="blue" variant="subtle">
                                Header obrigatório
                              </Badge>
                            )}
                          </HStack>
                        </VStack>
                      </HStack>
                    </CardBody>
                  </Card>
                ))}
              </VStack>
            </RadioGroup>
          )}
        />
      </VStack>
    </Center>
  );
};
