import React, { useEffect } from 'react';
import {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalBody,
  Center,
  Spinner,
  VStack,
  Text,
  Icon,
} from '@chakra-ui/react';
import { CatalogTypeStep } from './CatalogTypeStep';
import { CatalogContentStep } from './CatalogContentStep';
import { ProductsStep } from './ProductsStep';
import { ReviewStep } from './ReviewStep';
import { scrollbarStyles } from '../../../../../styles/scrollbar.styles';
import { useSendCatalogModal } from '../../../../../hooks/useSendCatalogModal';
import { ModalSendCatalogHeader } from './ModalSendCatalogHeader';
import { ModalSendCatalogFooter } from './ModalSendCatalogFooter';
import { FiAlertTriangle, FiTriangle } from 'react-icons/fi';

interface SendCatalogModalProps {
  isOpen: boolean;
  onClose: () => void;
  conversationId: string;
}

export const SendCatalogModal = ({
  isOpen,
  onClose,
  conversationId,
}: SendCatalogModalProps) => {
  const {
    step,
    searchInput,
    products,
    currentPage,
    totalPages,
    totalItems,
    watchedValues,
    selectedType,
    isLoadingProducts,
    integrationsLoading,
    catalogLoading,
    activeIntegrations,
    selectedIntegration,
    hasValidCatalog,
    form,
    sendCatalogMutation,
    setSearchInput,
    handleSearch,
    handleLoadMore,
    handleProductToggle,
    handleNext,
    handleBack,
    handleReset,
    handleIntegrationChange,
    onSubmit,
    canProceed,
    getSelectedProductsData,
  } = useSendCatalogModal({ conversationId, onClose });

  useEffect(() => {
    if (!isOpen) {
      handleReset();
    }
  }, [isOpen, handleReset]);

  useEffect(() => {
    form.setValue('selectedProducts', []);
  }, [watchedValues.type, form]);

  if (catalogLoading) {
    return (
      <Modal isOpen={isOpen} onClose={onClose} size="md" isCentered>
        <ModalOverlay bg="blackAlpha.600" backdropFilter="blur(4px)" />
        <ModalContent bg="white" borderRadius="xl" boxShadow="2xl" py={8}>
          <Center>
            <VStack spacing={4}>
              <Spinner size="xl" color="blue.500" thickness="4px" />
              <Text color="gray.600">
                Verificando configurações do catálogo...
              </Text>
            </VStack>
          </Center>
        </ModalContent>
      </Modal>
    );
  }

  if (!hasValidCatalog) {
    return (
      <Modal isOpen={isOpen} onClose={onClose} size="md" isCentered>
        <ModalOverlay bg="blackAlpha.600" backdropFilter="blur(4px)" />
        <ModalContent bg="white" borderRadius="xl" boxShadow="2xl" py={8}>
          <Center>
            <VStack spacing={4}>
              <Icon as={FiAlertTriangle} boxSize={12} color="red.500" />
              <Text fontSize="lg" color="gray.700" fontWeight="semibold">
                Catálogo não configurado
              </Text>
              <Text
                fontSize="sm"
                color="gray.500"
                textAlign="center"
                maxW="320px"
              >
                Essa empresa não tem catálogo com a Meta configurado.
                <br />
                Entre em contato com o suporte para configurar.
              </Text>
            </VStack>
          </Center>
        </ModalContent>
      </Modal>
    );
  }

  if (integrationsLoading) {
    return (
      <Modal isOpen={isOpen} onClose={onClose} size="md" isCentered>
        <ModalOverlay bg="blackAlpha.600" backdropFilter="blur(4px)" />
        <ModalContent bg="white" borderRadius="xl" boxShadow="2xl" py={8}>
          <Center>
            <VStack spacing={4}>
              <Spinner size="xl" color="blue.500" thickness="4px" />
              <Text color="gray.600">Carregando integrações...</Text>
            </VStack>
          </Center>
        </ModalContent>
      </Modal>
    );
  }

  if (activeIntegrations.length === 0) {
    return (
      <Modal isOpen={isOpen} onClose={onClose} size="md" isCentered>
        <ModalOverlay bg="blackAlpha.600" backdropFilter="blur(4px)" />
        <ModalContent bg="white" borderRadius="xl" boxShadow="2xl" py={8}>
          <Center>
            <VStack spacing={4}>
              <Icon as={FiTriangle} boxSize={12} color="orange.500" />
              <Text fontSize="lg" color="gray.700" fontWeight="semibold">
                Nenhuma integração ativa
              </Text>
              <Text
                fontSize="sm"
                color="gray.500"
                textAlign="center"
                maxW="320px"
              >
                Configure ao menos uma integração para enviar catálogos de
                produtos.
              </Text>
            </VStack>
          </Center>
        </ModalContent>
      </Modal>
    );
  }

  const renderStep = () => {
    switch (step) {
      case 'type':
        return <CatalogTypeStep control={form.control} />;

      case 'content':
        return (
          <CatalogContentStep
            control={form.control}
            errors={form.formState.errors}
            selectedType={selectedType}
          />
        );

      case 'products':
        return (
          <ProductsStep
            selectedType={selectedType}
            selectedProducts={watchedValues.selectedProducts || []}
            products={products}
            searchInput={searchInput}
            activeIntegrations={activeIntegrations}
            selectedIntegration={selectedIntegration}
            onSearchInputChange={setSearchInput}
            onSearch={handleSearch}
            onProductToggle={handleProductToggle}
            onLoadMore={handleLoadMore}
            onIntegrationChange={handleIntegrationChange}
            isLoading={isLoadingProducts}
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={totalItems}
          />
        );

      case 'review':
        return (
          <ReviewStep
            formData={watchedValues}
            selectedType={selectedType}
            selectedProductsData={getSelectedProductsData()}
          />
        );

      default:
        return null;
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="6xl" isCentered>
      <ModalOverlay bg="blackAlpha.600" backdropFilter="blur(4px)" />
      <ModalContent
        maxH="80vh"
        minH="80vh"
        maxW="70vw"
        minW="70vw"
        bg="white"
        borderRadius="xl"
        boxShadow="2xl"
        overflow="hidden"
      >
        <ModalSendCatalogHeader step={step} />

        <ModalBody
          py={6}
          px={6}
          overflow="auto"
          flex="1"
          display="flex"
          flexDirection="column"
          css={scrollbarStyles({ width: '5px' })}
        >
          {renderStep()}
        </ModalBody>

        <ModalSendCatalogFooter
          step={step}
          canProceed={!!canProceed()}
          selectedProductsLength={watchedValues.selectedProducts?.length || 0}
          maxProducts={selectedType.maxProducts}
          isLoading={sendCatalogMutation.isLoading}
          onClose={onClose}
          onBack={handleBack}
          onNext={handleNext}
          onSubmit={form.handleSubmit(onSubmit)}
        />
      </ModalContent>
    </Modal>
  );
};
