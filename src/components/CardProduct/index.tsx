import { Box, useDisclosure } from '@chakra-ui/react';
import { MessageProductCard } from '../../types/Message';
import DrawerProduct from './DrawerProduct';
import MultiProductCard from './MultiProductCard';
import SingleProductCard from './SingleProductCard';

interface ProductCardProps {
  card: MessageProductCard;
}

const ProductCard = ({ card }: ProductCardProps) => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const isMPM = card.type === 'multi_product';

  return (
    <>
      <Box
        bg="white"
        borderRadius="8px"
        overflow="hidden"
        maxWidth="100%"
        boxShadow="0 1px 3px rgba(0,0,0,0.12)"
        cursor="pointer"
        _hover={{ boxShadow: '0 2px 8px rgba(0,0,0,0.15)' }}
        transition="all 0.2s"
        p={3}
        onClick={onOpen}
      >
        {isMPM ? (
          <MultiProductCard card={card} onOpen={onOpen} />
        ) : (
          <SingleProductCard card={card} onOpen={onOpen} />
        )}
      </Box>

      <DrawerProduct isOpen={isOpen} onClose={onClose} card={card} />
    </>
  );
};

export default ProductCard;
