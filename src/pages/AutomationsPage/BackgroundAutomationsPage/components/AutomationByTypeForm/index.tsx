import {
  Box,
  Button,
  Container,
  Divider,
  Flex,
  FormControl,
  FormLabel,
  Grid,
  GridItem,
  Heading,
  HStack,
  Input,
  Radio,
  RadioGroup,
  Select,
  Stack,
  Text,
  Tabs,
  TabList,
  TabPanels,
  TabPanel,
  Tab,
  Tooltip,
} from '@chakra-ui/react';
import { yupResolver } from '@hookform/resolvers/yup';
import { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';
import { colors } from '../../../../../constants/colors';
import { MessageTemplateUtils } from '../../../../../utils/message-templates.utils';
import InputSwitchControlled from '../../../../../components/InputSwitchControlled';
import WhatsappTemplatePreview from '../../../../../components/WhatsappTemplatePreview';
import EmailTemplatePreview from '../../../../../components/EmailTemplatePreview';
import {
  AutomationType,
  EmailTemplate,
  EmailTemplateType,
  MessageTemplateType,
} from '../../../../../types/Prisma';
import { AutomationByTypeFormValues } from '../../EditBackgroundAutomationByTypePage';
import { useQuery } from 'react-query';
import { apiRoutes } from '../../../../../constants/api-routes';
import {
  FlowsService,
  ListFlowItem,
} from '../../../../../services/flows.service';
import { OrdersService } from '../../../../../services/orders.service';
import { AutomationActionEnum } from '../../../../../types/AutomationActionEnum';
import InputSelect from '../../../../../components/InputSelect';
import useSelectOptionsQuery from '../../../../../hooks/useSelectOptionsQuery';
import { AutomationTypeSlug } from '../../../../../types/AutomationTypes';
import { EmailTemplateUtils } from '../../../../../utils/email-template.utils';
import { EmailTemplatesService } from '../../../../../services/email-templates.service';
import { MessageTemplatesService } from '../../../../../services/message-templates.service';

const schema = yup
  .object({
    name: yup.string().required(),
    isActive: yup.boolean().required(),
    messageTemplateId: yup.string().nullable(),
    emailTemplateId: yup.string().nullable(),
    dailyMessageLimitOnWhatsapp: yup.number().nullable().positive().integer(),
    dailyEmailLimit: yup.number().nullable().positive().integer(),
    flowId: yup.string().nullable(),
    action: yup.string().required(),
    data: yup
      .object()
      .shape({
        orderStatuses: yup.array().of(yup.string()).nullable(),
        paymentMethods: yup.array().of(yup.string()).nullable(),
        cancelReasons: yup.array().of(yup.string()).nullable(),
      })
      .nullable(),
  })
  .required();

type FormValues = {
  name: string;
  isActive: boolean;
  messageTemplateId: string | null;
  emailTemplateId: string | null;
  dailyMessageLimitOnWhatsapp: number;
  dailyEmailLimit: number;
  flowId: string | null;
  action: string;
  data: {
    orderStatuses?: string[] | null;
    paymentMethods?: string[] | null;
    cancelReasons?: string[] | null;
  } | null;
};

interface AutomationByTypeFormProps {
  onSubmit: (data: FormValues) => void;
  whatsappTemplateArgs: { [key: string]: string | undefined };
  setWhatsappTemplateArgs: (args: {
    [key: string]: string | undefined;
  }) => void;
  emailTemplateArgs: { [key: string]: string | undefined };
  setEmailTemplateArgs: (args: { [key: string]: string | undefined }) => void;
  automation?: AutomationByTypeFormValues;
  automationType?: AutomationType;
}

const AutomationByTypeForm = ({
  onSubmit,
  whatsappTemplateArgs,
  setWhatsappTemplateArgs,
  emailTemplateArgs,
  setEmailTemplateArgs,
  automation,
  automationType,
}: AutomationByTypeFormProps) => {
  const [tabIndex, setTabIndex] = useState(0);

  const shouldShowCancelReasons =
    automationType?.slug === AutomationTypeSlug.order_status_update &&
    (automation?.name?.toLowerCase().includes('cancelado') ||
      automationType?.name?.toLowerCase().includes('cancelado'));

  const defaultValues: FormValues = {
    name: automation?.name || automationType?.name || '',
    isActive: automation?.isActive ?? true,
    messageTemplateId: automation?.messageTemplateId || null,
    emailTemplateId: automation?.emailTemplateId || null,
    dailyMessageLimitOnWhatsapp: automation?.dailyMessageLimitOnWhatsapp || 100,
    dailyEmailLimit: automation?.dailyEmailLimit || 1000,
    flowId: automation?.flowId || null,
    action: automation?.action || 'send_message_template',
    data: {
      ...automation?.data,
      ...(automationType?.slug === AutomationTypeSlug.order_status_update && {
        orderStatuses: automation?.data?.orderStatuses || [],
        paymentMethods: automation?.data?.paymentMethods || [],
        ...(shouldShowCancelReasons && {
          cancelReasons: automation?.data?.cancelReasons || [],
        }),
      }),
    },
  };

  const {
    data: whatsappTemplates = [],
    isFetching: isFetchingWhatsappTemplates,
  } = useQuery(
    apiRoutes.listMessageTemplates({
      status: 'approved',
      type: automationType?.slug.toUpperCase() as MessageTemplateType,
    }),
    async () => {
      const { data } = await MessageTemplatesService.listMessageTemplates({
        status: 'approved',
        type: automationType?.slug.toUpperCase() as MessageTemplateType,
      });
      return data;
    },
    {
      enabled: !!automationType?.slug,
    },
  );

  const { data: emailTemplates = [], isFetching: isFetchingEmailTemplates } =
    useQuery(
      apiRoutes.listEmailTemplates({
        type: automationType?.slug.toUpperCase() as EmailTemplateType,
      }),
      async () => {
        const { data } = await EmailTemplatesService.listEmailTemplates({
          type: automationType?.slug.toUpperCase() as EmailTemplateType,
        });
        return data;
      },
      {
        enabled: !!automationType?.slug,
      },
    );

  const automationAlreadyHasWhatsappTemplate = !!automation?.messageTemplateId;
  const automationAlreadyHasEmailTemplate = !!automation?.emailTemplateId;

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    control,
  } = useForm<FormValues>({
    resolver: yupResolver(schema),
    defaultValues: defaultValues,
  });

  const watchMessageTemplateId = watch('messageTemplateId');
  const watchEmailTemplateId = watch('emailTemplateId');

  const { data: flows } = useQuery(apiRoutes.listFlows(), async () => {
    const { data } = await FlowsService.listFlows();
    if (data.length >= 1) {
      return data.filter((flow: ListFlowItem) => {
        return flow.type === 'abandoned_cart';
      });
    }
  });

  function handleChangeTemplateParameter(parameter: string, value: string) {
    setWhatsappTemplateArgs({
      ...whatsappTemplateArgs,
      [parameter]: value,
    });
  }

  function handleChangeEmailTemplateParameter(
    parameter: string,
    value: string,
  ) {
    setEmailTemplateArgs({
      ...emailTemplateArgs,
      [parameter]: value,
    });
  }

  const selectedWhatsappTemplate = watchMessageTemplateId
    ? whatsappTemplates?.find(
        (template) => template.id === watchMessageTemplateId,
      )
    : null;

  const selectedEmailTemplate = watchEmailTemplateId
    ? emailTemplates?.find((template) => template.id === watchEmailTemplateId)
    : null;

  const whatsappTemplateCustomParameters =
    MessageTemplateUtils.getCustomParametersInText(
      selectedWhatsappTemplate?.templateText || '',
    );

  const emailTemplateCustomParameters = selectedEmailTemplate?.html
    ? EmailTemplateUtils.getCustomParametersInText(selectedEmailTemplate.html)
    : [];

  useEffect(() => {
    if (automation) {
      setValue('name', automation.name);
      setValue('isActive', automation.isActive);
      setValue('messageTemplateId', automation.messageTemplateId);
      setValue('emailTemplateId', automation.emailTemplateId);
      setValue(
        'dailyMessageLimitOnWhatsapp',
        automation.dailyMessageLimitOnWhatsapp,
      );
      setValue('dailyEmailLimit', automation.dailyEmailLimit);
      setValue('flowId', automation.flowId);
      setValue('action', automation.action);
      setValue('data', automation.data || {});
    }
  }, [automation, setValue]);

  const handleChangeAction = (value: AutomationActionEnum) => {
    setValue('action', value);

    if (value === AutomationActionEnum.SEND_MESSAGE_TEMPLATE) {
      setValue('flowId', null);
    } else {
      setValue('messageTemplateId', null);
    }
  };

  const ordersStatusOptions =
    automationType?.slug === 'order_status_update'
      ? useSelectOptionsQuery(
          apiRoutes.listOrderFieldValues(
            'status',
            automationType.sourceIntegration,
          ),
          () =>
            OrdersService.listOrderFieldValues(
              'status',
              automationType.sourceIntegration,
            ),
          'status',
        )
      : [];

  const paymentMethodsOptions =
    automationType?.slug === 'order_status_update'
      ? useSelectOptionsQuery(
          apiRoutes.listOrderFieldValues(
            'paymentMethods',
            automationType.sourceIntegration,
          ),
          () =>
            OrdersService.listOrderFieldValues(
              'paymentMethods',
              automationType.sourceIntegration,
            ),
          'paymentMethods',
        )
      : [];

  const cancelReasonsOptions =
    automationType?.slug === 'order_status_update'
      ? useSelectOptionsQuery(
          apiRoutes.listOrderFieldValues(
            'cancelReason',
            automationType.sourceIntegration,
          ),
          () =>
            OrdersService.listOrderFieldValues(
              'cancelReason',
              automationType.sourceIntegration,
            ),
          'cancelReason',
        )
      : [];

  async function handleCreateOrderStatus(newStatusValue: string) {
    const currentDataOrderStatuses = watch('data.orderStatuses') || [];

    const updatedStatuses = Array.isArray(currentDataOrderStatuses)
      ? [...currentDataOrderStatuses, newStatusValue]
      : [newStatusValue];

    setValue('data.orderStatuses', updatedStatuses, {
      shouldDirty: true,
      shouldValidate: true,
    });
  }

  async function handleCreatePaymentMethods(newValue: string) {
    const current = watch('data.paymentMethods') || [];
    const updated = [...current, newValue];
    setValue('data.paymentMethods', updated, {
      shouldDirty: true,
      shouldValidate: true,
    });
  }

  const handleWhatsappTemplateChange = (
    e: React.ChangeEvent<HTMLSelectElement>,
  ) => {
    register('messageTemplateId').onChange(e);
    if (e.target.value) setTabIndex(0);
  };

  const handleEmailTemplateChange = (
    e: React.ChangeEvent<HTMLSelectElement>,
  ) => {
    register('emailTemplateId').onChange(e);
    if (e.target.value) setTabIndex(1);
  };

  return (
    <Grid
      height="auto"
      templateColumns="2fr 450px"
      paddingTop="50px"
      paddingBottom="50px"
      paddingX="100px"
      alignItems="start"
      gap={4}
    >
      <GridItem>
        <Heading size="md" mb={5}>
          {automation ? 'Editar automação' : 'Nova automação'}
        </Heading>
        <form onSubmit={handleSubmit(onSubmit)}>
          <Stack spacing={5}>
            <FormControl isRequired>
              <FormLabel>Nome da automação</FormLabel>
              <Input placeholder="Nome" {...register('name')} disabled={true} />
              <Text color={colors.danger} fontSize="xs">
                {errors.name?.message}
              </Text>
            </FormControl>

            {automationType?.slug === 'abandoned_cart' && (
              <>
                <Divider />
                <FormControl>
                  <FormLabel>Ação da automação</FormLabel>
                  <Controller
                    name="action"
                    control={control}
                    defaultValue={AutomationActionEnum.SEND_MESSAGE_TEMPLATE}
                    render={({ field }) => (
                      <RadioGroup
                        onChange={handleChangeAction}
                        defaultValue={
                          AutomationActionEnum.SEND_MESSAGE_TEMPLATE
                        }
                        value={field.value}
                      >
                        <HStack gap="6">
                          <Radio
                            value={AutomationActionEnum.SEND_MESSAGE_TEMPLATE}
                          >
                            Enviar template de WhatsApp ou Email
                          </Radio>
                          <Radio value={AutomationActionEnum.TRIGGER_FLOW}>
                            Acionar fluxo
                          </Radio>
                        </HStack>
                      </RadioGroup>
                    )}
                  />
                </FormControl>
              </>
            )}

            {automationType?.slug ===
              AutomationTypeSlug.order_status_update && (
              <>
                <FormControl>
                  <FormLabel>Status do Pedido</FormLabel>
                  <Controller
                    name="data.orderStatuses"
                    control={control}
                    defaultValue={automation?.data?.orderStatuses || []}
                    render={({ field }) => {
                      return (
                        <InputSelect
                          options={ordersStatusOptions || []}
                          onCreateOption={handleCreateOrderStatus}
                          isMulti
                          value={
                            field.value?.map((value) => ({
                              value,
                              label: value,
                            })) || []
                          }
                          onChange={(
                            selectedOptions: Array<{ value: string }>,
                          ) => {
                            field.onChange(
                              selectedOptions.map((option) => option.value),
                            );
                          }}
                          placeholder="Selecione ou adicione um novo status"
                        />
                      );
                    }}
                  />
                </FormControl>

                <FormControl>
                  <FormLabel>Forma de Pagamento</FormLabel>
                  <Controller
                    name="data.paymentMethods"
                    control={control}
                    defaultValue={automation?.data?.paymentMethods || []}
                    render={({ field }) => {
                      return (
                        <InputSelect
                          options={paymentMethodsOptions || []}
                          onCreateOption={handleCreatePaymentMethods}
                          isMulti
                          value={
                            field.value?.map((value) => ({
                              value,
                              label: value,
                            })) || []
                          }
                          onChange={(
                            selectedOptions: Array<{ value: string }>,
                          ) => {
                            field.onChange(
                              selectedOptions.map((opt) => opt.value),
                            );
                          }}
                          placeholder="Selecione ou adicione novas formas de pagamento"
                        />
                      );
                    }}
                  />
                </FormControl>

                {shouldShowCancelReasons && (
                  <FormControl>
                    <FormLabel>Razão de Cancelamento</FormLabel>
                    <Controller
                      name="data.cancelReasons"
                      control={control}
                      defaultValue={automation?.data?.cancelReasons || []}
                      render={({ field }) => {
                        return (
                          <InputSelect
                            options={cancelReasonsOptions || []}
                            isMulti
                            value={
                              field.value?.map((value) => ({
                                value,
                                label: value,
                              })) || []
                            }
                            onChange={(
                              selectedOptions: Array<{ value: string }>,
                            ) => {
                              field.onChange(
                                selectedOptions.map((opt) => opt.value),
                              );
                            }}
                            placeholder="Selecione as razões de cancelamento"
                          />
                        );
                      }}
                    />
                  </FormControl>
                )}
                <Divider />
              </>
            )}

            {watch('action') === AutomationActionEnum.SEND_MESSAGE_TEMPLATE && (
              <>
                <FormControl
                  isRequired={automationAlreadyHasWhatsappTemplate}
                  isDisabled={isFetchingWhatsappTemplates}
                >
                  <FormLabel>Template WhatsApp para envio</FormLabel>
                  <Select
                    placeholder="Selecione um template"
                    {...register('messageTemplateId')}
                    value={watchMessageTemplateId || ''}
                    onChange={handleWhatsappTemplateChange}
                  >
                    {whatsappTemplates?.map((template) => (
                      <option
                        key={template.id}
                        value={template.id}
                        title={template.templateText}
                      >
                        {template.name}
                      </option>
                    ))}
                  </Select>
                  <Text color={colors.danger} fontSize="xs">
                    {errors.messageTemplateId?.message}
                  </Text>
                </FormControl>

                {selectedWhatsappTemplate &&
                  !!whatsappTemplateCustomParameters?.length && (
                    <Container paddingX={'50px'}>
                      <Text mb={4} fontWeight="bold">
                        Parâmetros WhatsApp
                      </Text>
                      {whatsappTemplateCustomParameters.map((param) => (
                        <FormControl isRequired key={param}>
                          <FormLabel fontSize="sm">
                            {param.replaceAll('[', '').replaceAll(']', '')}
                          </FormLabel>
                          <Input
                            size="sm"
                            onChange={(e) =>
                              handleChangeTemplateParameter(
                                param,
                                e.target.value,
                              )
                            }
                            value={whatsappTemplateArgs[param] || ''}
                            required
                          />
                        </FormControl>
                      ))}
                    </Container>
                  )}

                <Divider />

                <FormControl
                  isRequired={automationAlreadyHasEmailTemplate}
                  isDisabled={isFetchingEmailTemplates}
                >
                  <FormLabel>Template Email para envio</FormLabel>
                  <Select
                    placeholder="Selecione um template"
                    {...register('emailTemplateId')}
                    onChange={handleEmailTemplateChange}
                    value={watchEmailTemplateId || ''}
                  >
                    {emailTemplates?.map((template) => (
                      <option
                        key={template.id}
                        value={template.id}
                        title={template.html}
                      >
                        {template.name}
                      </option>
                    ))}
                  </Select>
                  <Text color={colors.danger} fontSize="xs">
                    {errors.emailTemplateId?.message}
                  </Text>

                  <Text fontSize={12} mt={2}>
                    Limite de envio de emails fixado em 1000 emails por dia
                  </Text>
                </FormControl>

                {selectedEmailTemplate &&
                  !!emailTemplateCustomParameters?.length && (
                    <Container paddingX={'50px'}>
                      <Text mb={4} fontWeight="bold">
                        Parâmetros Email
                      </Text>
                      {emailTemplateCustomParameters.map((param) => (
                        <FormControl isRequired key={param}>
                          <FormLabel fontSize="sm">
                            {param.replaceAll('[', '').replaceAll(']', '')}
                          </FormLabel>
                          <Input
                            size="sm"
                            onChange={(e) =>
                              handleChangeEmailTemplateParameter(
                                param,
                                e.target.value,
                              )
                            }
                            value={emailTemplateArgs[param] || ''}
                            required
                          />
                        </FormControl>
                      ))}
                    </Container>
                  )}
              </>
            )}

            {watch('action') === AutomationActionEnum.TRIGGER_FLOW && (
              <FormControl isRequired>
                <FormLabel>Fluxo a ser ativado</FormLabel>
                <Select
                  placeholder="Selecione um fluxo"
                  {...register('flowId')}
                >
                  {flows?.map((flow) => (
                    <option key={flow.id} value={flow.id} title={flow.title}>
                      {flow.title}
                    </option>
                  ))}
                </Select>
                <Text color={colors.danger} fontSize="xs">
                  {errors.flowId?.message}
                </Text>
              </FormControl>
            )}

            <Divider />

            <FormControl isRequired>
              <FormLabel>
                Limite diário de envio de mensagens Whatsapp
              </FormLabel>
              <Input
                type="number"
                placeholder="Limite diário de envio de mensagens Whatsapp"
                {...register('dailyMessageLimitOnWhatsapp')}
              />
              <Text color={colors.danger} fontSize="xs">
                {errors.dailyMessageLimitOnWhatsapp?.message}
              </Text>
            </FormControl>

            <FormControl isRequired isDisabled>
              <FormLabel>Limite diário de envio de Emails</FormLabel>
              <Input
                type="number"
                placeholder="Limite diário de envio de emails"
                {...register('dailyEmailLimit')}
              />
              <Text color={colors.danger} fontSize="xs">
                {errors.dailyEmailLimit?.message}
              </Text>
            </FormControl>

            <FormControl>
              <FormLabel>Ativo?</FormLabel>
              <InputSwitchControlled name="isActive" control={control} />
              <Text color={colors.danger} fontSize="xs">
                {errors.isActive?.message}
              </Text>
            </FormControl>
            <Divider />
            <Flex justify={'flex-end'}>
              <Button
                width="30%"
                isLoading={false}
                color={colors.white}
                bgColor={colors.primary}
                type="submit"
              >
                Salvar
              </Button>
            </Flex>
          </Stack>
        </form>
      </GridItem>
      <GridItem position={'sticky'} top={0} alignSelf="start" w="full">
        <Box
          display="flex"
          flexDirection="column"
          justifyContent="center"
          alignItems="center"
        >
          <Tabs
            width="100%"
            isFitted
            variant="enclosed"
            index={tabIndex}
            onChange={(index) => setTabIndex(index)}
          >
            <TabList mb="1em">
              <Tab isDisabled={!selectedWhatsappTemplate}>WhatsApp</Tab>
              <Tab isDisabled={!selectedEmailTemplate}>Email</Tab>
            </TabList>
            <TabPanels width={'450px'}>
              <TabPanel>
                {selectedWhatsappTemplate && (
                  <WhatsappTemplatePreview
                    message={selectedWhatsappTemplate.templateText}
                    footer={selectedWhatsappTemplate.footerText}
                    buttons={selectedWhatsappTemplate.messageTemplateButtons}
                    fileUrl={selectedWhatsappTemplate.mediaUrl}
                  />
                )}
              </TabPanel>
              <TabPanel>
                {selectedEmailTemplate && (
                  <EmailTemplatePreview
                    emailTemplate={selectedEmailTemplate}
                    selectedEmailTemplate={selectedEmailTemplate}
                  />
                )}
              </TabPanel>
            </TabPanels>
          </Tabs>
        </Box>
      </GridItem>
    </Grid>
  );
};

export default AutomationByTypeForm;
