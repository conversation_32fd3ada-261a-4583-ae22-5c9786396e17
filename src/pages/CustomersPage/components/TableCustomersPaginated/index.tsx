import { Box, Flex, Link, Switch, Tag, useToast, Text } from '@chakra-ui/react';
import { ColumnDef } from '@tanstack/react-table';
import { format } from 'date-fns';
import { useCallback } from 'react';
import { useMutation } from 'react-query';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import DataTableServerPaginated from '../../../../components/DataTableServerPaginated';
import { appPaths } from '../../../../constants/app-paths';
import { useCustomerSearchParams } from '../../../../hooks/useCustomerSearchParams';
import {
  ConversationsService,
  CreateConversationDto,
} from '../../../../services/conversations.service';
import { CustomersService } from '../../../../services/customers.service';
import { setSelectedCustomerRows } from '../../../../state/campaignCreationSlice';
import { RootState } from '../../../../state/store';
import { ConversationWithIncludes } from '../../../../types/Conversation';
import { CustomerSql } from '../../../../types/CustomerSql';
import { TableHeaders } from '../../../../types/TableHeaders';
import { MoneyUtils } from '../../../../utils/money.utils';
import { useAppModuleAccessGuard } from '../../../../hooks/useAppModuleAccessGuard';
import { ColorUtils } from '../../../../utils/color.utils';
import { NumberUtils } from './../../../../utils/number.utils';

const TableCustomersPaginated = () => {
  const { checkUserHasPathAccess } = useAppModuleAccessGuard();
  const navigate = useNavigate();
  const { showSelectCustomerRows, selectedCustomerRows, refetchKey } =
    useSelector((state: RootState) => state.campaignCreation);
  const dispatch = useDispatch();
  const toast = useToast();
  const {
    selectedEngagementTemplateIds,
    selectedEngagementEmailTemplateIds,
    searchQuery,
    minTotalPurchases,
    maxTotalPurchases,
    minAverageOrderValue,
    maxAverageOrderValue,
    minTotalOrders,
    maxTotalOrders,
    selectedEngagementActionTypes,
    selectedEmailEngagementActionTypes,
    startOrdersCreatedAt,
    endOrdersCreatedAt,
    minDaysSinceLastCampaign,
    minDaysSinceLastEmailCampaign,
    maxDaysSinceLastEngagement,
    sortBy,
    minAverageItemValue,
    maxAverageItemValue,
    selectedTags,
    minDaysSinceLastPurchase,
    maxDaysSinceLastPurchase,
    exactDaysSinceLastPurchase,
    isOrderSubscription,
    excludedTags,
    selectedDefaultAgentIds,
    excludedTemplateIds,
    excludedEmailTemplateIds,
    selectedProductIds,
    excludedProductIds,
    selectedProductComparator,
    excludedProductComparator,
    minProductQuantity,
    maxProductQuantity,
    minDaysSinceLastProductPurchase,
    maxDaysSinceLastProductPurchase,
    productNameContains,
    isLastProductPurchased,
    customFieldId1,
    customFieldValue1,
    customFieldComparisonType1,
    customFieldId2,
    customFieldValue2,
    customFieldComparisonType2,
    customFieldId3,
    customFieldValue3,
    customFieldComparisonType3,
    customFieldId4,
    customFieldValue4,
    customFieldComparisonType4,
    isScheduledCampaignsVisible,
    isScheduledEmailCampaignsVisible,
    platformOrderSource,
    selectedStates,
    selectedCoupons,
    selectedOrdersStatuses,
    selectedOrdersSalesReps,
    selectedCities,
    hasEmail,
    hasPhoneNumberId,
    selectedCampaignChannel,
    daysUntilBirthday,
    selectedOrdersSources,
    selectedOrdersStoreNames,
    selectedOrdersSalesChannels,
  } = useCustomerSearchParams();

  const columnWidths = {
    select: '50px',
    name: '200px',
    phone_number_id: '200px',
    email: '200px',
    customer_tags: '220px',
    createdAt: '200px',
    lastPurchaseAt: '200px',
    default_agent_name: '200px',
    state: '100px',
    city: '100px',
    total_orders: '200px',
    total_purchases: '200px',
    average_order_value: '200px',
    is_opted_out: '200px',
  };

  const toggleOptOutCustomer = useMutation(
    (customerId: string) => CustomersService.toggleOptOutCustomer(customerId),
    {
      onSuccess: ({ data }) => {
        toast({
          title: 'Cliente Bloqueado',
          description: `O cliente ${data.name} foi bloqueado com sucesso.`,
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      },
      onError: () => {
        toast({
          title: 'Erro ao Bloquear Cliente(s)',
          status: 'error',
          description: 'Não foi possível realizar a operação de bloqueio.',
          duration: 3000,
          isClosable: true,
        });
      },
    },
  );
  const createConversation = useMutation(
    (createConversationDto: CreateConversationDto) =>
      ConversationsService.createConverstation(createConversationDto),
    {
      onSuccess: (res) => {
        const createdConversation: ConversationWithIncludes = res.data;
        navigate({
          pathname: appPaths.conversations(),
          search: `conversationId=${createdConversation.id}`,
        });
      },
    },
  );

  const handleClickRow = useCallback(
    async (row: CustomerSql) => {
      await createConversation.mutateAsync({
        recipientPhoneNumberId: row.phoneNumberId,
        recipientName: row.name,
      });
    },
    [createConversation],
  );

  function transformHeader(header: TableHeaders) {
    const column: ColumnDef<any> = {
      header: () => header.header,
      accessorKey: header.accessorKey,
      id: header.id,
      cell: (info: any) => info.getValue(),
    };
    if (header.accessorKey === 'name') {
      column.cell = (info: any) => {
        return checkUserHasPathAccess(appPaths.conversations()) &&
          !!info.row.original.phoneNumberId ? (
          <Link
            onClick={() => handleClickRow(info.row.original as CustomerSql)}
            noOfLines={1}
            isTruncated
            title={info.getValue()}
            color="black"
            fontWeight="semibold"
          >
            {info.getValue()}
          </Link>
        ) : (
          <Box noOfLines={1} isTruncated title={info.getValue()}>
            {info.getValue()}
          </Box>
        );
      };
    }
    if (header.accessorKey === 'customerTags') {
      column.cell = (info: any) => {
        const customerTags: CustomerSql['customerTags'] = info.getValue();
        const maxTags = 2;

        const visibleTags = customerTags?.slice(0, maxTags) || [];
        const hiddenCount = (customerTags?.length || 0) - visibleTags.length;

        return (
          <Flex
            gap={2}
            overflow="hidden"
            flexWrap="nowrap"
            justifyContent="start"
            width="100%"
            alignItems="start"
          >
            {visibleTags.map((tag) => (
              <Tag
                key={tag}
                size="sm"
                backgroundColor={ColorUtils.lightenHexColor(
                  ColorUtils.stringToHexColor(tag),
                  0.9,
                )}
                color={ColorUtils.stringToHexColor(tag)}
                borderColor={ColorUtils.stringToHexColor(tag)}
                borderWidth="1px"
                fontWeight="500"
                borderRadius="full"
                px={3}
                py={1}
                whiteSpace="nowrap"
                textOverflow="ellipsis"
                maxWidth="120px"
                overflow="hidden"
                title={tag}
                display="flex"
                alignItems="start"
                justifyContent="start"
              >
                <Text as="span" overflow="hidden" textOverflow="ellipsis">
                  {tag}
                </Text>
              </Tag>
            ))}
            {hiddenCount > 0 && (
              <Tag
                size="sm"
                background="gray.200"
                color="gray.700"
                px={3}
                py={1}
                borderRadius="full"
                whiteSpace="nowrap"
                display="flex"
                alignItems="center"
                justifyContent="center"
              >
                +{hiddenCount}
              </Tag>
            )}
          </Flex>
        );
      };
    }

    if (header.accessorKey === 'defaultAgentName') {
      column.cell = (info: any) => {
        const value = info.getValue();
        return <span>{value || 'Não Atribuído'}</span>;
      };
    }
    if (
      ['createdAt', 'lastPurchaseAt', 'lastCampaignAt', 'birthDate'].includes(
        header.accessorKey,
      )
    ) {
      column.cell = (info: any) => {
        const value = info.getValue();
        if (!value) return null;

        const date = new Date(value);
        const utcDate = new Date(
          date.getUTCFullYear(),
          date.getUTCMonth(),
          date.getUTCDate(),
        );

        const displayValue = format(utcDate, 'dd/MM/yyyy');
        return <span>{displayValue}</span>;
      };
    }
    if (header.accessorKey === 'isOptedOut') {
      column.cell = (info: any) => {
        return (
          <Switch
            size="md"
            defaultChecked={info.row.original.isOptedOut}
            colorScheme="black"
            onChange={async (event) => {
              await toggleOptOutCustomer.mutateAsync(info.row.original.id);
            }}
          />
        );
      };
    }
    if (
      ['averageOrderValue', 'averageItemValue', 'totalPurchases'].includes(
        header.accessorKey,
      )
    ) {
      column.cell = (info: any) => {
        const value = info.getValue() as number;
        const displayValue = value ? MoneyUtils.formatCurrency(value) : null;
        return <span>{displayValue}</span>;
      };
    }
    return column;
  }

  function handleChangeSelectedRows(selectedRows: Record<string, boolean>) {
    dispatch(setSelectedCustomerRows(selectedRows));
  }

  return (
    <DataTableServerPaginated
      showRowSelection={showSelectCustomerRows}
      dataUrl="/customers"
      headersUrl="/customers/table-headers"
      queryParameters={{
        selectedTemplateIds: selectedEngagementTemplateIds,
        selectedEmailTemplateIds: selectedEngagementEmailTemplateIds,
        selectedEngagementActionTypes,
        selectedEmailEngagementActionTypes,
        searchQuery,
        minTotalPurchases: NumberUtils.isValidNumericFilter(minTotalPurchases)
          ? String(Number(minTotalPurchases) * 100)
          : '',
        maxTotalPurchases: NumberUtils.isValidNumericFilter(maxTotalPurchases)
          ? String(Number(maxTotalPurchases) * 100)
          : '',
        minAverageOrderValue: NumberUtils.isValidNumericFilter(
          minAverageOrderValue,
        )
          ? String(Number(minAverageOrderValue) * 100)
          : '',
        maxAverageOrderValue: NumberUtils.isValidNumericFilter(
          maxAverageOrderValue,
        )
          ? String(Number(maxAverageOrderValue) * 100)
          : '',
        minAverageItemValue: NumberUtils.isValidNumericFilter(
          minAverageItemValue,
        )
          ? String(Number(minAverageItemValue) * 100)
          : '',
        maxAverageItemValue: NumberUtils.isValidNumericFilter(
          maxAverageItemValue,
        )
          ? String(Number(maxAverageItemValue) * 100)
          : '',
        minTotalOrders,
        maxTotalOrders,
        startOrdersCreatedAt,
        endOrdersCreatedAt,
        minDaysSinceLastCampaign,
        minDaysSinceLastEmailCampaign,
        maxDaysSinceLastEngagement,
        sortBy,
        selectedTags,
        minDaysSinceLastPurchase: NumberUtils.isValidNumericFilter(
          minDaysSinceLastPurchase,
        )
          ? String(Number(minDaysSinceLastPurchase))
          : '',
        maxDaysSinceLastPurchase: NumberUtils.isValidNumericFilter(
          maxDaysSinceLastPurchase,
        )
          ? String(Number(maxDaysSinceLastPurchase))
          : '',
        exactDaysSinceLastPurchase,
        isOrderSubscription,
        excludedTags,
        selectedDefaultAgentIds,
        excludedTemplateIds,
        excludedEmailTemplateIds,
        selectedProductIds,
        excludedProductIds,
        minProductQuantity,
        maxProductQuantity,
        minDaysSinceLastProductPurchase: NumberUtils.isValidNumericFilter(
          minDaysSinceLastProductPurchase,
        )
          ? String(Number(minDaysSinceLastProductPurchase))
          : '',
        maxDaysSinceLastProductPurchase: NumberUtils.isValidNumericFilter(
          maxDaysSinceLastProductPurchase,
        )
          ? String(Number(maxDaysSinceLastProductPurchase))
          : '',
        productNameContains,
        isLastProductPurchased,
        customFieldId1,
        customFieldValue1,
        customFieldComparisonType1,
        customFieldId2,
        customFieldValue2,
        customFieldComparisonType2,
        customFieldId3,
        customFieldValue3,
        customFieldComparisonType3,
        customFieldId4,
        customFieldValue4,
        customFieldComparisonType4,
        isScheduledCampaignsVisible,
        isScheduledEmailCampaignsVisible,
        isCreatingCampaign: String(showSelectCustomerRows),
        platformOrderSource,
        selectedStates,
        selectedCoupons,
        selectedOrdersStatuses,
        selectedOrdersSalesReps,
        selectedCities,
        hasEmail,
        hasPhoneNumberId,
        selectedCampaignChannel,
        selectedProductComparator,
        excludedProductComparator,
        daysUntilBirthday,
        selectedOrdersSources,
        selectedOrdersStoreNames,
        selectedOrdersSalesChannels,
      }}
      transformHeader={transformHeader}
      rowSelection={selectedCustomerRows}
      onRowSelectionChange={handleChangeSelectedRows}
      refetchKey={refetchKey}
      columnWidths={columnWidths}
    />
  );
};

export default TableCustomersPaginated;
