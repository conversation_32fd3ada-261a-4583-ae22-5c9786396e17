import React from 'react';
import {
  Card,
  CardBody,
  VStack,
  HStack,
  Checkbox,
  Image,
  Text,
  Badge,
} from '@chakra-ui/react';
import { MoneyUtils } from '../../../../../../utils/money.utils';
import { ProductVariant } from '../../../../../../types/Product';

interface ProductCardProps {
  product: ProductVariant;
  isSelected: boolean;
  onToggle: (productId: string) => void;
}

export const ProductCard = ({
  product,
  isSelected,
  onToggle,
}: ProductCardProps) => {
  const hasStock = (product.stockQuantity ?? 0) > 0;

  return (
    <Card
      variant={isSelected ? 'filled' : 'outline'}
      bg={isSelected ? 'blue.50' : 'white'}
      borderColor={isSelected ? 'blue.200' : 'gray.200'}
      cursor="pointer"
      _hover={{
        transform: 'translateY(-2px)',
        boxShadow: 'lg',
        borderColor: 'blue.300',
      }}
      transition="all 0.2s"
      onClick={() => onToggle(product.id)}
      opacity={!hasStock ? 0.7 : 1}
    >
      <CardBody p={4}>
        <VStack spacing={3} align="stretch">
          <HStack spacing={3} align="start">
            <Checkbox
              isChecked={isSelected}
              onChange={() => onToggle(product.id)}
              colorScheme="blue"
              size="lg"
            />
            {product.imageUrl && (
              <Image
                src={product.imageUrl}
                alt={product.name}
                boxSize="80px"
                objectFit="cover"
                borderRadius="lg"
                bg="gray.100"
              />
            )}
            <VStack align="start" flex={1} spacing={2}>
              <Text fontWeight="bold" fontSize="sm" noOfLines={2}>
                {product.name}
              </Text>
              {product.description && (
                <Text fontSize="xs" color="gray.600" noOfLines={2}>
                  {product.description}
                </Text>
              )}
              <HStack spacing={2}>
                <Text fontSize="lg" fontWeight="bold" color="green.600">
                  {MoneyUtils.formatCurrency(product.price ?? 0)}
                </Text>
                {!hasStock && (
                  <Badge colorScheme="red" variant="solid" fontSize="xs">
                    Sem estoque
                  </Badge>
                )}
              </HStack>
              {product.sku && (
                <Text fontSize="xs" color="gray.500">
                  SKU: {product.sku}
                </Text>
              )}
            </VStack>
          </HStack>
        </VStack>
      </CardBody>
    </Card>
  );
};
