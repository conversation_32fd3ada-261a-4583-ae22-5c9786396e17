import { AxiosResponse } from 'axios';
import { apiRoutes } from '../constants/api-routes';
import { request } from '../constants/request';
import {
  ConversationSector,
  ConversationSectorWithIncludes,
} from '../types/ConversationSector';

const listConversationSectors = async (): Promise<
  AxiosResponse<ConversationSectorWithIncludes[]>
> => {
  return request.get(apiRoutes.listConversationSectors());
};

const listMyConversationSectors = async (): Promise<
  AxiosResponse<ConversationSector[]>
> => {
  return request.get(apiRoutes.listMyConversationSectors());
};

export interface CreateConversationSectorDto {
  name: string;
}

const createConversationSector = async (
  createConversationSectorDto: CreateConversationSectorDto,
): Promise<AxiosResponse<ConversationSector>> => {
  return request.post(
    apiRoutes.createConversationSector(),
    createConversationSectorDto,
  );
};

const deleteConversationSector = async (
  conversationSectorId: string,
): Promise<AxiosResponse<ConversationSector>> => {
  return request.delete(
    apiRoutes.deleteConversationSector(conversationSectorId),
  );
};

const hasSomeConversationSectorAssociatedToUser = async (
  userId: string,
): Promise<AxiosResponse<boolean>> => {
  return request.get(
    apiRoutes.hasSomeConversationSectorAssociatedToUser(userId),
  );
};

const getConversationSector = async (
  conversationSectorId: string,
): Promise<AxiosResponse<ConversationSectorWithIncludes>> => {
  return request.get(apiRoutes.getConversationSector(conversationSectorId));
};

const getUserSectorsAndCategories = async (
  userId: string,
): Promise<AxiosResponse<ConversationSectorWithIncludes[]>> => {
  return request.get(apiRoutes.getUserSectorsAndCategories(userId));
};

const updateConversationSector = async (
  conversationSectorId: string,
  data: Partial<ConversationSector> & { userIds?: string[] },
): Promise<AxiosResponse<ConversationSectorWithIncludes>> => {
  return request.put(
    apiRoutes.updateConversationSector(conversationSectorId),
    data,
  );
};

export const ConversationSectorsService = {
  listConversationSectors,
  listMyConversationSectors,
  createConversationSector,
  getConversationSector,
  getUserSectorsAndCategories,
  updateConversationSector,
  deleteConversationSector,
  hasSomeConversationSectorAssociatedToUser,
};
