import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';
import { AxiosError } from 'axios';
import { AuthService } from '../services/auth.service';
import { MixpanelService } from '../services/mixpanel.service';
import { JwtPayload, parseJwt } from '../utils/parse-jwt.utils';
import { RootState } from './store';
import { DateUtils } from '../utils/date.utils';

interface AuthState {
  currentUser: JwtPayload | null;
  userActions: {
    isPasswordChangeRequired: boolean;
  };
}

export const login = createAsyncThunk(
  'auth/login',
  async (
    { email, password }: { email: string; password: string },
    { rejectWithValue },
  ) => {
    try {
      const response = await AuthService.login({ email, password });
      return response.data;
    } catch (err) {
      const error = err as AxiosError;
      return rejectWithValue(error.response?.data);
    }
  },
);

const defaultUserAcions: AuthState['userActions'] = {
  isPasswordChangeRequired: false,
};
// Define the initial state using that type
const initialState: AuthState = {
  currentUser: null,
  userActions: defaultUserAcions,
};

export const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    checkAuthentication: (state) => {
      const accessToken = AuthService.getAccessToken();
      if (accessToken) {
        localStorage.removeItem('user.actions');
        // const userActionsLocalStorage = localStorage.getItem('user.actions');
        // if (userActionsLocalStorage) {
        //   const userActions: AuthState['userActions'] = JSON.parse(
        //     userActionsLocalStorage,
        //   );
        //   state.userActions = userActions;
        // }
        state.currentUser = parseJwt(accessToken);
      } else {
        state.currentUser = null;
      }
    },
    updateUserActions: (
      state,
      action: PayloadAction<Partial<AuthState['userActions']>>,
    ) => {
      state.userActions = { ...state.userActions, ...action.payload };
      localStorage.setItem('user.actions', JSON.stringify(state.userActions));
    },
    logout: (state) => {
      AuthService.logout();
      state.currentUser = null;
      state.userActions = defaultUserAcions;
    },
  },
  extraReducers: (builder) => {
    // Add reducers for additional action types here, and handle loading state as needed
    builder.addCase(login.fulfilled, (state, action) => {
      const { access_token: accessToken, actions } = action.payload;

      // const userActions = {
      //   isPasswordChangeRequired: DateUtils.isSameOfBeforeDate(
      //     actions.next_password_change_date,
      //   ),
      // };
      // localStorage.setItem('user.actions', JSON.stringify(userActions));

      localStorage.setItem('access_token', accessToken);
      localStorage.removeItem('user.actions');

      const currentUser = parseJwt(accessToken);
      state.currentUser = currentUser;
      // state.userActions = userActions;
      MixpanelService.identify(currentUser!.sub);
      MixpanelService.people.set({
        $email: currentUser!.email,
        companyId: currentUser!.companyId,
        companyName: currentUser!.company.name,
      });
      MixpanelService.track('login');
    });
  },
});

export const { checkAuthentication, updateUserActions, logout } =
  authSlice.actions;

export const isAuthenticated = (state: RootState) =>
  state.auth.currentUser !== null;

export default authSlice.reducer;
