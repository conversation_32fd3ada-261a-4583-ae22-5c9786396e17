import { Flex } from '@chakra-ui/react';

interface ButtonWhatsappProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary';
}

const ButtonWhatsapp = ({
  children,
  onClick,
  variant = 'primary',
}: ButtonWhatsappProps) => (
  <Flex
    bg="#FFFFFF"
    color="#009de2"
    border="1px solid #e0e0e0"
    borderRadius="5px"
    padding={variant === 'primary' ? 2 : 1.5}
    justifyContent="center"
    alignItems="center"
    fontSize={variant === 'primary' ? '14px' : '12px'}
    fontWeight="500"
    cursor="pointer"
    onClick={onClick}
    _hover={{ bg: '#f5f5f5' }}
    _active={{ bg: '#e5e5e5' }}
  >
    {children}
  </Flex>
);

export default ButtonWhatsapp;
