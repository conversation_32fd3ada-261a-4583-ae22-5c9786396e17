import { Flex, Box, Text, Image } from '@chakra-ui/react';
import { MessageProductCardItems } from '../../../types/Message';
import { MoneyUtils } from '../../../utils/money.utils';

interface ListProductItemProps {
  product: MessageProductCardItems;
}

const ListProductItem = ({ product }: ListProductItemProps) => {
  const handleProductClick = () => {
    if (product.productVariant.url) {
      window.open(product.productVariant.url, '_blank');
    }
  };

  return (
    <Flex
      p={3}
      borderBottom="1px solid #e0e0e0"
      cursor="pointer"
      onClick={handleProductClick}
      _hover={{ bg: '#f5f5f5' }}
      alignItems="center"
      gap={3}
    >
      {product.productVariant.imageUrl && (
        <Image
          src={product.productVariant.imageUrl}
          alt={product.name}
          width="60px"
          height="60px"
          objectFit="cover"
          borderRadius="8px"
          flexShrink={0}
        />
      )}

      <Box flex={1}>
        <Text fontSize="14px" fontWeight="500" color="#000" mb={1}>
          {product.name}
        </Text>

        <Text fontSize="14px" fontWeight="600" color="#00a884" mb={1}>
          {MoneyUtils.formatCurrency(product.priceCents)}
        </Text>

        {product.productVariant.description && (
          <Text
            fontSize="12px"
            color="#54656f"
            noOfLines={2}
            dangerouslySetInnerHTML={{
              __html: product.productVariant.description.replace(
                /<[^>]*>/g,
                '',
              ),
            }}
          />
        )}

        <Flex justifyContent="space-between" alignItems="center" mt={2}>
          {product.productVariant.stockQuantity > 0 ? (
            <Text fontSize="12px" color="#00a884">
              Em estoque
            </Text>
          ) : (
            <Text fontSize="12px" color="#e53e3e">
              Esgotado
            </Text>
          )}

          {product.productVariant.sku && (
            <Text fontSize="10px" color="#54656f">
              SKU: {product.productVariant.sku}
            </Text>
          )}
        </Flex>
      </Box>
    </Flex>
  );
};

export default ListProductItem;
