import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Text,
  VStack,
  HStack,
  useToast,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure,
} from '@chakra-ui/react';
import { FiDownload } from 'react-icons/fi';
import { IOSPushNotificationsUtils } from '../../utils/ios-push-notifications.utils';

interface PWAInstallPromptProps {
  showOnIOS?: boolean;
  showOnOtherDevices?: boolean;
}

export const PWAInstallPrompt: React.FC<PWAInstallPromptProps> = ({
  showOnIOS = true,
  showOnOtherDevices = false,
}) => {
  const [isPWAInstallable, setIsPWAInstallable] = useState(false);
  const [isIOS, setIsIOS] = useState(false);
  const [isPWAInstalled, setIsPWAInstalled] = useState(false);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const toast = useToast();

  useEffect(() => {
    const checkPWAStatus = () => {
      const iosInfo = IOSPushNotificationsUtils.getIOSInfo();
      setIsIOS(iosInfo.isIOS);
      setIsPWAInstalled(iosInfo.isPWAInstalled);

      const isStandalone = (window.navigator as any).standalone === true;
      const isPWAInstalled = window.matchMedia(
        '(display-mode: standalone)',
      ).matches;

      const isInstallable =
        !isStandalone &&
        !isPWAInstalled &&
        'serviceWorker' in navigator &&
        window.matchMedia('(display-mode: browser)').matches;

      setIsPWAInstallable(isInstallable);
    };

    checkPWAStatus();

    const mediaQuery = window.matchMedia('(display-mode: standalone)');
    const handleChange = () => {
      checkPWAStatus();
    };

    mediaQuery.addEventListener('change', handleChange);

    return () => {
      mediaQuery.removeEventListener('change', handleChange);
    };
  }, []);

  const shouldShow = () => {
    if (isPWAInstalled) return false;
    if (isIOS && showOnIOS) return true;
    if (!isIOS && showOnOtherDevices) return true;
    return false;
  };

  const handleInstallPWA = () => {
    if (isIOS) {
      onOpen();
    } else {
      toast({
        title: 'Instalação PWA',
        description: 'Use o menu do navegador para instalar o app.',
        status: 'info',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  if (!shouldShow()) {
    return null;
  }

  return (
    <>
      <Alert status="info" borderRadius="md">
        <AlertIcon />
        <Box flex="1">
          <AlertTitle>Instale como App</AlertTitle>
          <AlertDescription>
            Para uma melhor experiência e notificações push, instale o Revi como
            app.
          </AlertDescription>
        </Box>
        <Button
          leftIcon={<FiDownload />}
          size="sm"
          colorScheme="blue"
          onClick={handleInstallPWA}
        >
          Instalar
        </Button>
      </Alert>

      <Modal isOpen={isOpen} onClose={onClose}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Instalar Revi no iOS</ModalHeader>
          <ModalBody>
            <VStack spacing={4} align="stretch">
              <Text>Para instalar o Revi como app no seu iPhone ou iPad:</Text>

              <VStack spacing={3} align="stretch">
                <HStack>
                  <Text fontWeight="bold">1.</Text>
                  <Text>
                    Toque no botão de compartilhar (ícone quadrado com seta para
                    cima)
                  </Text>
                </HStack>

                <HStack>
                  <Text fontWeight="bold">2.</Text>
                  <Text>
                    Role para baixo e toque em "Adicionar à Tela Inicial"
                  </Text>
                </HStack>

                <HStack>
                  <Text fontWeight="bold">3.</Text>
                  <Text>Toque em "Adicionar"</Text>
                </HStack>

                <HStack>
                  <Text fontWeight="bold">4.</Text>
                  <Text>Abra o app da tela inicial</Text>
                </HStack>
              </VStack>

              <Alert status="info">
                <AlertIcon />
                <AlertDescription>
                  Após instalar, você poderá ativar notificações push para
                  receber alertas mesmo quando o app estiver fechado.
                </AlertDescription>
              </Alert>
            </VStack>
          </ModalBody>
          <ModalFooter>
            <Button onClick={onClose}>Entendi</Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  );
};
