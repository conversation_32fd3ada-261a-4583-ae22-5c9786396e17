import {
  Box,
  Button,
  Divider,
  Flex,
  FormControl,
  Heading,
  Input,
  Text,
  useToast,
  VStack,
} from '@chakra-ui/react';
import { useMutation } from 'react-query';
import FormLabel from '../../../../components/FormLabel';
import {
  AuthService,
  UpdatePasswordDto,
} from '../../../../services/auth.service';
import * as yup from 'yup';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { colors } from '../../../../constants/colors';

const schema = yup
  .object({
    currentPassword: yup.string().required(),
    newPassword: yup.string().required().min(8, 'Mínimo de 8 caracteres'),
    confirmNewPassword: yup
      .string()
      .oneOf([yup.ref('newPassword')], 'As senhas devem ser iguais'),
  })
  .required();

export default function AccountSettingsPage() {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });
  const toast = useToast();
  const updatePassword = useMutation(
    (updatePasswordDto: UpdatePasswordDto) =>
      AuthService.updatePassword(updatePasswordDto),
    {
      onSuccess: (res) => {
        toast({
          title: 'Senha atualizada com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      },
    },
  );

  async function onSubmit(data: any) {
    await updatePassword.mutateAsync(data);
  }

  return (
    <Box
      bg="white"
      borderRadius="md"
      border="1px solid"
      borderColor="gray.200"
      p={8}
    >
      <Heading size="lg" mb={1}>
        Segurança
      </Heading>
      <Text color="gray.500" mb={8}>
        Altere sua senha de acesso
      </Text>

      <form onSubmit={handleSubmit(onSubmit)}>
        <VStack spacing={6}>
          <FormControl>
            <FormLabel>Senha atual</FormLabel>
            <Input
              type="password"
              {...register('currentPassword')}
              placeholder="Digite sua senha atual"
            />
            <Text color={colors.danger} fontSize="xs">
              {errors.currentPassword?.message}
            </Text>
          </FormControl>
          <FormControl>
            <FormLabel>Nova senha</FormLabel>
            <Input
              type="password"
              {...register('newPassword')}
              placeholder="Digite sua nova senha"
            />
            <Text color={colors.danger} fontSize="xs">
              {errors.newPassword?.message}
            </Text>
          </FormControl>
          <FormControl>
            <FormLabel>Confirmar senha nova</FormLabel>
            <Input
              type="password"
              {...register('confirmNewPassword')}
              placeholder="Confirme sua nova senha"
            />
            <Text color={colors.danger} fontSize="xs">
              {errors.confirmNewPassword?.message}
            </Text>
          </FormControl>

          <Divider />

          <Flex justify="flex-end" width="100%">
            <Button
              width="30%"
              isLoading={false}
              color={colors.white}
              bgColor={colors.primary}
              type="submit"
            >
              Atualizar senha
            </Button>
          </Flex>
        </VStack>
      </form>
    </Box>
  );
}
