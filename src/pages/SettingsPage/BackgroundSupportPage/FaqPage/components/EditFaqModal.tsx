import React, { useEffect } from 'react';
import {
  <PERSON>dal,
  ModalOverlay,
  Modal<PERSON>ontent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  Button,
  VStack,
  FormControl,
  FormLabel,
  FormErrorMessage,
  Input,
  Textarea,
} from '@chakra-ui/react';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { Faq, BulkUpdateFaqsDto } from '../../../../../services/faq.service';

const updateFaqSchema = yup.object({
  id: yup.string().required(),
  question: yup.string().required('A pergunta é obrigatória'),
  answer: yup.string().required('A resposta é obrigatória'),
});

interface UpdateFaqFormData {
  id: string;
  question: string;
  answer: string;
}

interface EditFaqModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: BulkUpdateFaqsDto) => void;
  isLoading: boolean;
  faq: Faq | null;
}

export const EditFaqModal = ({
  isOpen,
  onClose,
  onSubmit,
  isLoading,
  faq,
}: EditFaqModalProps) => {
  const form = useForm<UpdateFaqFormData>({
    resolver: yupResolver(updateFaqSchema),
  });

  const handleSubmit = (data: UpdateFaqFormData) => {
    const updateData = {
      id: data.id,
      question: data.question,
      answer: data.answer,
    };
    onSubmit(updateData);
  };

  const handleClose = () => {
    form.reset();
    onClose();
  };

  useEffect(() => {
    if (faq) {
      form.setValue('id', faq.id);
      form.setValue('question', faq.question);
      form.setValue('answer', faq.answer);
    }
  }, [faq, form]);

  return (
    <Modal isOpen={isOpen} onClose={handleClose}>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>Editar Pergunta</ModalHeader>
        <form onSubmit={form.handleSubmit(handleSubmit)}>
          <ModalBody>
            <VStack spacing={4}>
              <FormControl isInvalid={!!form.formState.errors.question}>
                <FormLabel>Pergunta</FormLabel>
                <Input
                  {...form.register('question')}
                  placeholder="Digite a pergunta"
                />
                <FormErrorMessage>
                  {form.formState.errors.question?.message}
                </FormErrorMessage>
              </FormControl>

              <FormControl isInvalid={!!form.formState.errors.answer}>
                <FormLabel>Resposta</FormLabel>
                <Textarea
                  {...form.register('answer')}
                  placeholder="Digite a resposta"
                  rows={4}
                />
                <FormErrorMessage>
                  {form.formState.errors.answer?.message}
                </FormErrorMessage>
              </FormControl>
            </VStack>
          </ModalBody>

          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={handleClose}>
              Cancelar
            </Button>
            <Button colorScheme="green" type="submit" isLoading={isLoading}>
              Atualizar
            </Button>
          </ModalFooter>
        </form>
      </ModalContent>
    </Modal>
  );
};
