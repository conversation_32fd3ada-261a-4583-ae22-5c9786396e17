import { FormDataUtils } from './../utils/form-data.utils';
import { AxiosResponse } from 'axios';
import { Edge, Node } from 'reactflow';
import { apiRoutes } from '../constants/api-routes';
import { request } from '../constants/request';
import { Flow } from '../types/Flow';
import { FlowNode } from '../types/FlowNode';
import { FlowTrigger } from '../types/FlowTrigger';
import { FlowType } from '../types/Prisma';

export interface CreateFlowDto extends Record<any, any> {}

export interface UpdateFlowMetadataDto {
  title?: string;
  repeatOnInvalidInput?: boolean;
  isActive?: boolean;
}

type CreateFlowResponse = Flow;
const createFlow = async (
  createFlowDto: CreateFlowDto,
): Promise<AxiosResponse<CreateFlowResponse>> => {
  return request.post(apiRoutes.createFlow(), createFlowDto);
};

const copyFlow = async (
  flowId: string,
): Promise<AxiosResponse<ShowFlowResponse>> => {
  return request.get(apiRoutes.copyFlow(flowId));
};

export interface UpdateFlowDto {
  title: string;
  type: string;
  isActive: boolean;
  repeatOnInvalidInput: boolean;
  nodes: Node[];
  edges: Edge[];
  flowTriggers: any[];
}

type UpdateFlowResponse = ShowFlowResponse;
const updateFlow = async (
  flowId: string,
  updateFlowDto: UpdateFlowDto,
): Promise<AxiosResponse<UpdateFlowResponse>> => {
  const data = FormDataUtils.convertJsonToFormData(updateFlowDto);
  return request.put(apiRoutes.updateFlow(flowId), data, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    timeout: 240000,
  });
};

const updateFlowMetadata = async (
  flowId: string,
  updateFlowMetadataDto: UpdateFlowMetadataDto,
): Promise<AxiosResponse<UpdateFlowResponse>> => {
  return request.patch(
    apiRoutes.updateFlowMetadata(flowId),
    updateFlowMetadataDto,
  );
};

export interface ListFlowItem extends Flow {
  flowNodes: FlowNode[];
  flowTriggers: FlowTrigger[];
}

const countFlows = async (
  type?: FlowType,
  isActive?: boolean,
): Promise<AxiosResponse<number>> => {
  return request.get(apiRoutes.countFlows(type, isActive));
};

const hasActiveFlow = async (
  type?: FlowType,
): Promise<AxiosResponse<boolean>> => {
  return request.get(apiRoutes.hasActiveFlow(type));
};

const listFlows = async (): Promise<AxiosResponse<ListFlowItem[]>> => {
  return request.get(apiRoutes.listFlows());
};

export interface ShowFlowResponse extends Flow {
  flowNodes: FlowNode[];
  flowTriggers: FlowTrigger[];
}
const showFlow = async (
  flowId: string,
): Promise<AxiosResponse<ShowFlowResponse>> => {
  return request.get(apiRoutes.showFlow(flowId));
};

const deleteFlow = async (flowId: string): Promise<AxiosResponse> => {
  return request.delete(apiRoutes.deleteFlow(flowId));
};

export const FlowsService = {
  createFlow,
  copyFlow,
  countFlows,
  hasActiveFlow,
  updateFlow,
  updateFlowMetadata,
  listFlows,
  showFlow,
  deleteFlow,
};
