import React from 'react';
import {
  Center,
  VStack,
  Text,
  Card,
  CardBody,
  SimpleGrid,
  Box,
  Divider,
  HStack,
  Image,
} from '@chakra-ui/react';
import { CatalogFormData, CatalogType } from '../../../../../../types/Catalog';
import { ProductVariant } from '../../../../../../types/Product';
import { MoneyUtils } from '../../../../../../utils/money.utils';

interface ReviewStepProps {
  formData: CatalogFormData;
  selectedType: CatalogType;
  selectedProductsData: ProductVariant[];
}

export const ReviewStep = ({
  formData,
  selectedType,
  selectedProductsData,
}: ReviewStepProps) => {
  return (
    <Center flex="1">
      <VStack spacing={6} maxW="3xl" w="full">
        <VStack spacing={2} textAlign="center">
          <Text fontSize="lg" color="gray.700">
            Revisar informações antes do envio
          </Text>
          <Text fontSize="sm" color="gray.500">
            Confira todos os detalhes da sua mensagem de catálogo
          </Text>
        </VStack>

        <Card w="full" variant="outline">
          <CardBody p={6}>
            <VStack spacing={6} align="stretch">
              <VStack spacing={4} align="stretch">
                <Text fontWeight="bold" fontSize="md" color="gray.800">
                  Configurações da Mensagem
                </Text>

                <SimpleGrid columns={2} spacing={4}>
                  <Box>
                    <Text fontSize="sm" color="gray.600" fontWeight="semibold">
                      Tipo:
                    </Text>
                    <Text fontSize="sm">{selectedType.label}</Text>
                  </Box>
                  <Box>
                    <Text fontSize="sm" color="gray.600" fontWeight="semibold">
                      Produtos:
                    </Text>
                    <Text fontSize="sm">
                      {formData.selectedProducts.length} selecionado(s)
                    </Text>
                  </Box>
                </SimpleGrid>

                {formData.header && (
                  <Box>
                    <Text fontSize="sm" color="gray.600" fontWeight="semibold">
                      Título:
                    </Text>
                    <Text
                      fontSize="sm"
                      mt={1}
                      p={3}
                      bg="gray.50"
                      borderRadius="md"
                    >
                      {formData.header}
                    </Text>
                  </Box>
                )}

                <Box>
                  <Text fontSize="sm" color="gray.600" fontWeight="semibold">
                    Conteúdo:
                  </Text>
                  <Text
                    fontSize="sm"
                    mt={1}
                    p={3}
                    bg="gray.50"
                    borderRadius="md"
                    whiteSpace="pre-wrap"
                  >
                    {formData.body}
                  </Text>
                </Box>

                {formData.footer && (
                  <Box>
                    <Text fontSize="sm" color="gray.600" fontWeight="semibold">
                      Rodapé:
                    </Text>
                    <Text
                      fontSize="sm"
                      mt={1}
                      p={3}
                      bg="gray.50"
                      borderRadius="md"
                    >
                      {formData.footer}
                    </Text>
                  </Box>
                )}
              </VStack>

              <Divider />

              <VStack spacing={4} align="stretch">
                <Text fontWeight="bold" fontSize="md" color="gray.800">
                  Produtos Selecionados
                </Text>

                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={3}>
                  {selectedProductsData.map((product) => (
                    <Card key={product.id} size="sm" variant="outline">
                      <CardBody p={3}>
                        <HStack spacing={3}>
                          {product.imageUrl && (
                            <Image
                              src={product.imageUrl}
                              alt={product.name}
                              boxSize="50px"
                              objectFit="cover"
                              borderRadius="md"
                              bg="gray.100"
                            />
                          )}
                          <VStack align="start" flex={1} spacing={1}>
                            <Text fontWeight="bold" fontSize="sm" noOfLines={1}>
                              {product.name}
                            </Text>
                            <Text
                              fontSize="sm"
                              fontWeight="bold"
                              color="green.600"
                            >
                              {MoneyUtils.formatCurrency(product.price)}
                            </Text>
                            {product.sku && (
                              <Text fontSize="xs" color="gray.500">
                                SKU: {product.sku}
                              </Text>
                            )}
                          </VStack>
                        </HStack>
                      </CardBody>
                    </Card>
                  ))}
                </SimpleGrid>
              </VStack>
            </VStack>
          </CardBody>
        </Card>
      </VStack>
    </Center>
  );
};
