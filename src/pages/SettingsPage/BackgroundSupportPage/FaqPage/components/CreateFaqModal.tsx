import { useEffect } from 'react';
import {
  Modal,
  Modal<PERSON>verlay,
  Modal<PERSON>ontent,
  ModalHeader,
  ModalBody,
  Modal<PERSON>ooter,
  Button,
  VStack,
  Box,
  Flex,
  Text,
  Badge,
  IconButton,
  Divider,
  FormControl,
  FormLabel,
  FormErrorMessage,
  Input,
  Textarea,
} from '@chakra-ui/react';
import { AddIcon } from '@chakra-ui/icons';
import { FaTrash } from 'react-icons/fa';
import { useForm, useFieldArray } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { BulkCreateFaqsDto } from '../../../../../services/faq.service';

const createFaqSchema = yup.object({
  faqs: yup
    .array()
    .of(
      yup.object({
        question: yup.string().required('A pergunta é obrigatória'),
        answer: yup.string().required('A resposta é obrigatória'),
      }),
    )
    .min(1, 'Adicione pelo menos uma pergunta')
    .required(),
});

interface CreateFaqFormData {
  faqs: Array<{
    question: string;
    answer: string;
  }>;
}

interface CreateFaqModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: BulkCreateFaqsDto) => void;
  isLoading: boolean;
}

export const CreateFaqModal = ({
  isOpen,
  onClose,
  onSubmit,
  isLoading,
}: CreateFaqModalProps) => {
  const form = useForm<CreateFaqFormData>({
    resolver: yupResolver(createFaqSchema),
    defaultValues: {
      faqs: [{ question: '', answer: '' }],
    },
  });

  const { fields, append, remove, replace } = useFieldArray({
    control: form.control,
    name: 'faqs',
  });

  const handleSubmit = (data: CreateFaqFormData) => {
    const manyFaqs: BulkCreateFaqsDto = { faqs: data.faqs };
    onSubmit(manyFaqs);
  };

  const handleClose = () => {
    form.reset({
      faqs: [{ question: '', answer: '' }],
    });
    onClose();
  };

  useEffect(() => {
    if (!isOpen) {
      form.reset({
        faqs: [{ question: '', answer: '' }],
      });
      replace([{ question: '', answer: '' }]);
    }
  }, [isOpen, form, replace]);

  const addNewFaqField = () => {
    append({ question: '', answer: '' });
  };

  const removeFaqField = (index: number) => {
    if (fields.length > 1) {
      remove(index);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="4xl">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>Adicionar Múltiplas Perguntas</ModalHeader>
        <form onSubmit={form.handleSubmit(handleSubmit)}>
          <ModalBody>
            <VStack spacing={6} align="stretch">
              <Box>
                <Flex justifyContent="space-between" alignItems="center" mb={4}>
                  <Text fontWeight="bold" fontSize="lg">
                    FAQs a serem criadas
                  </Text>
                  <Badge colorScheme="blue" fontSize="sm">
                    {fields.length}{' '}
                    {fields.length === 1 ? 'pergunta' : 'perguntas'}
                  </Badge>
                </Flex>

                {fields.map((field, index) => (
                  <Box
                    key={field.id}
                    p={4}
                    border="1px solid"
                    borderColor="gray.200"
                    borderRadius="md"
                    mb={4}
                  >
                    <Flex
                      justifyContent="space-between"
                      alignItems="center"
                      mb={3}
                    >
                      <Text fontWeight="medium" color="gray.700">
                        Pergunta {index + 1}
                      </Text>
                      {fields.length > 1 && (
                        <IconButton
                          aria-label="Remover pergunta"
                          icon={<FaTrash />}
                          size="sm"
                          variant="ghost"
                          colorScheme="red"
                          onClick={() => removeFaqField(index)}
                        />
                      )}
                    </Flex>

                    <VStack spacing={3} align="stretch">
                      <FormControl
                        isInvalid={
                          !!form.formState.errors.faqs?.[index]?.question
                        }
                      >
                        <FormLabel fontSize="sm">Pergunta</FormLabel>
                        <Input
                          {...form.register(`faqs.${index}.question` as const)}
                          placeholder="Digite a pergunta"
                          size="sm"
                        />
                        <FormErrorMessage>
                          {
                            form.formState.errors.faqs?.[index]?.question
                              ?.message
                          }
                        </FormErrorMessage>
                      </FormControl>

                      <FormControl
                        isInvalid={
                          !!form.formState.errors.faqs?.[index]?.answer
                        }
                      >
                        <FormLabel fontSize="sm">Resposta</FormLabel>
                        <Textarea
                          {...form.register(`faqs.${index}.answer` as const)}
                          placeholder="Digite a resposta"
                          rows={3}
                          size="sm"
                        />
                        <FormErrorMessage>
                          {form.formState.errors.faqs?.[index]?.answer?.message}
                        </FormErrorMessage>
                      </FormControl>
                    </VStack>

                    {index < fields.length - 1 && <Divider mt={4} />}
                  </Box>
                ))}
              </Box>

              <Button
                leftIcon={<AddIcon />}
                variant="outline"
                onClick={addNewFaqField}
                size="sm"
              >
                Adicionar outra pergunta
              </Button>
            </VStack>
          </ModalBody>

          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={handleClose}>
              Cancelar
            </Button>
            <Button colorScheme="green" type="submit" isLoading={isLoading}>
              Criar {fields.length}{' '}
              {fields.length === 1 ? 'Pergunta' : 'Perguntas'}
            </Button>
          </ModalFooter>
        </form>
      </ModalContent>
    </Modal>
  );
};
