import { Box, Skeleton, Image, Tooltip, BoxProps } from '@chakra-ui/react';
import { useState } from 'react';
import NoImage from '../NoImage';

interface ImageWithTooltipProps extends BoxProps {
  src: string;
  alt: string;
  showTooltip?: boolean;
  tooltipSize?: string;
}

const ImageWithTooltip = ({
  src,
  alt,
  showTooltip = true,
  tooltipSize = '300px',
  ...boxProps
}: ImageWithTooltipProps) => {
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const handleImageError = () => {
    setImageError(true);
    setIsLoading(false);
  };

  const handleImageLoad = () => {
    setIsLoading(false);
  };

  if (imageError) {
    return <NoImage showTooltip={showTooltip} {...boxProps} />;
  }

  const imageContent = (
    <Box
      position="relative"
      border="1px solid"
      borderColor="gray.200"
      borderRadius="md"
      overflow="hidden"
      cursor={showTooltip ? 'pointer' : 'default'}
      {...boxProps}
    >
      {isLoading && (
        <Skeleton
          width="100%"
          height="100%"
          position="absolute"
          top={0}
          left={0}
          zIndex={1}
        />
      )}
      <Image
        src={src}
        alt={alt}
        objectFit="cover"
        width="100%"
        height="100%"
        onError={handleImageError}
        onLoad={handleImageLoad}
        transition="opacity 0.3s"
        opacity={isLoading ? 0 : 1}
        zIndex={2}
      />
    </Box>
  );

  if (!showTooltip) {
    return imageContent;
  }

  return (
    <Tooltip
      label={
        <Box
          maxWidth={tooltipSize}
          maxHeight={tooltipSize}
          overflow="hidden"
          borderRadius="md"
        >
          <Image
            src={src}
            alt={alt}
            objectFit="contain"
            width="100%"
            height="100%"
            maxWidth={tooltipSize}
            maxHeight={tooltipSize}
          />
        </Box>
      }
      placement="right"
      hasArrow
      bg="white"
      border="1px solid"
      borderColor="gray.200"
      borderRadius="md"
      p={2}
      boxShadow="lg"
      openDelay={300}
      closeDelay={100}
    >
      {imageContent}
    </Tooltip>
  );
};

export default ImageWithTooltip;
