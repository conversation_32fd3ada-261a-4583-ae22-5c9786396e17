import { queryStringDelimiter } from '../constants/query-string-delimiter';
import {
  selectEmailEngagementActionOptions,
  selectWhatsappEngamentActionOptions,
} from '../pages/CustomersPage/components/FilterSidebar/selectEngamentActionOptions';
import { MessageTemplatesService } from '../services/message-templates.service';
import { TagsService } from '../services/tags.service';
import { MoneyUtils } from './money.utils';
import { UrlUtils } from './url.utils';
import { format, parse } from 'date-fns';

function getFormattedValue(value: any, type: string) {
  const formatters: Record<string, (value: any) => any> = {
    money: (value) => MoneyUtils.formatCurrency(value * 100),
    number: (value) => Number(value),
    date: (value) =>
      format(parse(value, 'yyyy-MM-dd', new Date()), 'dd/MM/yyyy'),
    bool: (value) => (value ? 'Sim' : 'Não'),
    id: () => 1,
    ids: (value) => value.split(queryStringDelimiter).length,
    sortBy: (value) => {
      const sortByTranslation: Record<string, string> = {
        createdAtDesc: 'Criado em (desc)',
        nameAsc: 'Nome (asc)',
        totalPurchasesDesc: 'Total em Compras (desc)',
        totalOrdersDesc: 'Total de Pedidos (desc)',
        lastPurchaseAtDesc: 'Última Compra (desc)',
        random: 'Aleatório',
      };
      return sortByTranslation[value] || value;
    },
    text: (value) => {
      return value.split(queryStringDelimiter).join(', ').replaceAll('+', ' ');
    },
  };

  return formatters[type] ? formatters[type](value) : value;
}

async function parseFilterCriteriaAsync(filterCriteria: string) {
  const translatedKeys: Record<
    string,
    {
      title: string;
      type:
        | 'string'
        | 'number'
        | 'id'
        | 'date'
        | 'bool'
        | 'money'
        | 'ids'
        | 'sortBy'
        | 'text';
      section:
        | 'general'
        | 'clientes'
        | 'tags'
        | 'campanhas'
        | 'compras'
        | 'produtos'
        | 'emails';
    }
  > = {
    searchQuery: {
      title: 'Campo de Pesquisa',
      type: 'string',
      section: 'general',
    },
    sortBy: { title: 'Ordenado Por', type: 'sortBy', section: 'general' },
    selectedTags: {
      title: 'Tags Selecionadas',
      type: 'text',
      section: 'tags',
    },
    excludedTags: { title: 'Tags Excluídas', type: 'text', section: 'tags' },
    selectedEngagementActionTypes: {
      title: 'Tipos de Ações de Engajamento Selecionados',
      type: 'text',
      section: 'campanhas',
    },
    selectedEmailEngagementActionTypes: {
      title: 'Tipos de Ações de Engajamento de Email Selecionados',
      type: 'text',
      section: 'emails',
    },
    selectedEngagementTemplateIds: {
      title: 'Templates selecionados',
      type: 'text',
      section: 'campanhas',
    },
    selectedEngagementEmailTemplateIds: {
      title: 'Templates de Email Selecionados',
      type: 'text',
      section: 'emails',
    },
    excludedTemplateIds: {
      title: 'Templates excluídos',
      type: 'text',
      section: 'campanhas',
    },
    excludedEmailTemplateIds: {
      title: 'Templates de Email Excluídos',
      type: 'text',
      section: 'emails',
    },
    minDaysSinceLastCampaign: {
      title: 'Mínimo de Dias desde a Última Campanha',
      type: 'number',
      section: 'campanhas',
    },
    maxDaysSinceLastEngagement: {
      title: 'Máximo de Dias desde o Último Engajamento',
      type: 'number',
      section: 'campanhas',
    },
    minDaysSinceLastEmailCampaign: {
      title: 'Mínimo de Dias desde a Última Campanha de Email',
      type: 'number',
      section: 'emails',
    },
    startOrdersCreatedAt: {
      title: 'Pedidos Criados a partir de',
      type: 'date',
      section: 'compras',
    },
    endOrdersCreatedAt: {
      title: 'Pedidos Criados até',
      type: 'date',
      section: 'compras',
    },
    minTotalPurchases: {
      title: 'Mínimo em Compras',
      type: 'money',
      section: 'compras',
    },
    maxTotalPurchases: {
      title: 'Máximo em Compras',
      type: 'money',
      section: 'compras',
    },
    minTotalOrders: {
      title: 'Mínimo de Pedidos',
      type: 'number',
      section: 'compras',
    },
    maxTotalOrders: {
      title: 'Máximo de Pedidos',
      type: 'number',
      section: 'compras',
    },
    minAverageOrderValue: {
      title: 'Valor Médio Mínimo do Pedido',
      type: 'money',
      section: 'compras',
    },
    maxAverageOrderValue: {
      title: 'Valor Médio Máximo do Pedido',
      type: 'money',
      section: 'compras',
    },
    minAverageItemValue: {
      title: 'Valor Médio Mínimo do Item',
      type: 'money',
      section: 'compras',
    },
    maxAverageItemValue: {
      title: 'Valor Médio Máximo do Item',
      type: 'money',
      section: 'compras',
    },
    minDaysSinceLastPurchase: {
      title: 'Mínimo de Dias desde a Última Compra',
      type: 'number',
      section: 'compras',
    },
    maxDaysSinceLastPurchase: {
      title: 'Máximo de Dias desde a Última Compra',
      type: 'number',
      section: 'compras',
    },
    exactDaysSinceLastPurchase: {
      title: 'Dias exatos desde a última compra',
      type: 'number',
      section: 'compras',
    },
    isOrderSubscription: {
      title: 'Pedido é inscrição ou regular',
      type: 'bool',
      section: 'compras',
    },
    selectedProductIds: {
      title: 'Produtos Selecionados',
      type: 'ids',
      section: 'produtos',
    },
    excludedProductIds: {
      title: 'Produtos Excluídos',
      type: 'ids',
      section: 'produtos',
    },
    minProductQuantity: {
      title: 'Mínimo de pedidos por produto',
      type: 'number',
      section: 'produtos',
    },
    maxProductQuantity: {
      title: 'Máximo de pedidos por produto',
      type: 'number',
      section: 'produtos',
    },
    isLastProductPurchased: {
      title: 'É o último produto comprado',
      type: 'bool',
      section: 'produtos',
    },
    minDaysSinceLastProductPurchase: {
      title: 'Mínimo de dias desde a última compra do produto',
      type: 'number',
      section: 'produtos',
    },
    maxDaysSinceLastProductPurchase: {
      title: 'Máximo de dias desde a última compra do produto',
      type: 'number',
      section: 'produtos',
    },
    productNameContains: {
      title: 'Nome do produto contém',
      type: 'string',
      section: 'produtos',
    },
    isScheduledCampaignsVisible: {
      title: 'Campanha Agendada',
      type: 'bool',
      section: 'campanhas',
    },
    isScheduledEmailCampaignsVisible: {
      title: 'Campanha de Email Agendada',
      type: 'bool',
      section: 'emails',
    },
    selectedStates: {
      title: 'Estados Selecionados',
      type: 'text',
      section: 'clientes',
    },
    selectedCities: {
      title: 'Cidades Selecionadas',
      type: 'text',
      section: 'clientes',
    },
    hasEmail: {
      title: 'Apenas clientes com email',
      type: 'bool',
      section: 'clientes',
    },
    hasPhoneNumberId: {
      title: 'Apenas clientes com telefone',
      type: 'bool',
      section: 'clientes',
    },
    selectedCampaignChannel: {
      title: 'Canal da Campanha',
      type: 'string',
      section: 'campanhas',
    },
  };

  const filterCriteriaObj = UrlUtils.convertQueryStringToObject(filterCriteria);

  const engagementActionTranslations: Record<string, string> =
    filterCriteriaObj.selectedCampaignChannel === 'whatsapp'
      ? Object.fromEntries(
          selectWhatsappEngamentActionOptions.map((option) => [
            option.value,
            option.label,
          ]),
        )
      : Object.fromEntries(
          selectEmailEngagementActionOptions.map((option) => [
            option.value,
            option.label,
          ]),
        );

  if (filterCriteriaObj.selectedEngagementActionTypes) {
    const translatedEngagementActionTypes =
      filterCriteriaObj.selectedEngagementActionTypes
        .split(queryStringDelimiter)
        .map((action: string) => engagementActionTranslations[action] || action)
        .join(queryStringDelimiter);

    filterCriteriaObj.selectedEngagementActionTypes =
      translatedEngagementActionTypes;
  }

  const replaceCriteriaIdsWithNames = async (
    field: string,
    criteriaType: 'template' | 'tag',
  ) => {
    if (!filterCriteriaObj[field]) return;

    const ids = filterCriteriaObj[field].replace('||', ',');

    if (criteriaType === 'template') {
      const { data } = await MessageTemplatesService.listMessageTemplates({
        ids,
      });
      const templateNames = data.map(({ name }: { name: string }) => name);
      return (filterCriteriaObj[field] = templateNames.join('||'));
    } else if (criteriaType === 'tag') {
      const { data } = await TagsService.listTags(ids);
      const tagNames = data.map(({ name }: { name: string }) => name);
      filterCriteriaObj[field] = tagNames.join('||');
    }
  };

  await Promise.all(
    [
      'selectedEngagementTemplateIds',
      'selectedEngagementEmailTemplateIds',
      'excludedTemplateIds',
      'excludedEmailTemplateIds',
    ].map((field) => replaceCriteriaIdsWithNames(field, 'template')),
  );

  await Promise.all(
    ['selectedTags', 'excludedTags'].map((field) =>
      replaceCriteriaIdsWithNames(field, 'tag'),
    ),
  );

  return Object.keys(translatedKeys)
    .filter((key) => key in filterCriteriaObj)
    .map((key) => ({
      title: translatedKeys[key].title,
      value: getFormattedValue(
        filterCriteriaObj[key],
        translatedKeys[key].type,
      ),
      section: translatedKeys[key].section,
    }));
}

export const CustomerFilterUtils = {
  parseFilterCriteriaAsync,
};
