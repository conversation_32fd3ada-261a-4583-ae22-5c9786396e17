import { Box } from '@chakra-ui/react';
import { SettingsIcon } from '@chakra-ui/icons';
import { SourceIntegration } from '../../../../../types/Prisma';
import { IntegrationDisplayNames } from '../../constants/integration-display-names';

interface ManualConfigurationScreenProps {
  sourceIntegration: SourceIntegration;
}

const ManualConfigurationScreen: React.FC<ManualConfigurationScreenProps> = ({
  sourceIntegration,
}) => {
  const integrationName = IntegrationDisplayNames[sourceIntegration];

  return (
    <Box
      textAlign="center"
      py={10}
      px={6}
      display="flex"
      flexDirection="column"
      justifyContent="center"
      alignItems="center"
      height="100vh"
    >
      <SettingsIcon boxSize={50} color="blue.500" />
      <Box mt={6} fontWeight="bold" fontSize="xl">
        Configuração Necessária
      </Box>
      <Box mt={4} color="gray.500" textAlign="center">
        Para configurar o {integrationName}, entre em contato com o time de
        suporte.
      </Box>
    </Box>
  );
};

export default ManualConfigurationScreen;
