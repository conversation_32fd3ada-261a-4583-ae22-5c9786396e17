import { Flex, FormControl, Select, Text } from '@chakra-ui/react';
import { addDays, format, parse } from 'date-fns';
import { useEffect } from 'react';
import ReactDatePicker from 'react-datepicker';
import { Controller, UseFormReturn } from 'react-hook-form';
import { Tb<PERSON><PERSON><PERSON>oney } from 'react-icons/tb';
import { useQuery } from 'react-query';
import { useSelector } from 'react-redux';
import FormLabel from '../../../../../components/FormLabel';
import InputNumber from '../../../../../components/InputNumber';
import InputRangeNumber from '../../../../../components/InputRangeNumber';
import InputSelect from '../../../../../components/InputSelect';
import { apiRoutes } from '../../../../../constants/api-routes';
import { useCustomerSearchParams } from '../../../../../hooks/useCustomerSearchParams';
import { OrdersService } from '../../../../../services/orders.service';
import { StatisticsService } from '../../../../../services/statistics.service';
import { RootState } from '../../../../../state/store';
import { CustomerFiltersEnum } from '../../../../../types/CustomerFiltersEnum';
import { MoneyUtils } from '../../../../../utils/money.utils';
import AccordionItemLayout from '../AccordionItemLayout';
import useSelectOptionsQuery from '../../../../../hooks/useSelectOptionsQuery';
import useUpdateSelectEffect from '../../../../../hooks/useUpdateSelectEffect';
import { StringUtils } from '../../../../../utils/string.utils';
import SelectField from '../../../../../components/SelectField/SelectField';

interface SectionPurchasesProps {
  useFormReturn: UseFormReturn<any>;
  updateSelectedValues: (args: any) => void;
}

const SectionPurchases = ({
  useFormReturn,
  updateSelectedValues,
}: SectionPurchasesProps) => {
  const VITE_ALLOWED_COMPANIES_TO_SEE_ORDER_INTEGRATION_FILTERS =
    import.meta.env.VITE_ALLOWED_COMPANIES_TO_SEE_ORDER_INTEGRATION_FILTERS ||
    '';

  const { currentUser } = useSelector((state: RootState) => state.auth);

  const { refetchKey } = useSelector(
    (state: RootState) => state.campaignCreation,
  );
  const {
    startOrdersCreatedAt,
    selectedCoupons,
    selectedOrdersStatuses,
    selectedOrdersSalesReps,
    selectedOrdersSources,
    selectedOrdersStoreNames,
    selectedOrdersSalesChannels,
  } = useCustomerSearchParams();

  const { control, watch, setValue } = useFormReturn;
  const watchMinDaysSinceLastPurchase = watch(
    CustomerFiltersEnum.MIN_DAYS_SINCE_LAST_PURCHASE,
  );
  const watchMaxDaysSinceLastPurchase = watch(
    CustomerFiltersEnum.MAX_DAYS_SINCE_LAST_PURCHASE,
  );
  const watchExactDaysSinceLastPurchase = watch(
    CustomerFiltersEnum.EXACT_DAYS_SINCE_LAST_PURCHASE,
  );

  const { data: orderStatistics, refetch: refetchOrderStatistics } = useQuery(
    apiRoutes.getOrderAggByCustomerStatistics(startOrdersCreatedAt),
    async () => {
      const { data } = await StatisticsService.getOrderAggByCustomerStatistics(
        startOrdersCreatedAt,
        format(addDays(new Date(), 1), 'yyyy-MM-dd'),
      );

      return data;
    },
  );

  const cleanRelatedFilters = () => {
    setValue('endOrdersCreatedAt', '');
    setValue('exactDaysSinceLastPurchase', '');
  };

  useEffect(() => {
    refetchOrderStatistics();
  }, [refetchKey, refetchOrderStatistics]);

  const couponsOptions = useSelectOptionsQuery(
    apiRoutes.listOrderFieldValues('coupon'),
    () => OrdersService.listOrderFieldValues('coupon'),
    'coupon',
  );

  const ordersStatusOptions = useSelectOptionsQuery(
    apiRoutes.listOrderFieldValues('status'),
    () => OrdersService.listOrderFieldValues('status'),
    'status',
  );

  const ordersSalesRepOptions = useSelectOptionsQuery(
    apiRoutes.listOrderFieldValues('salesRep'),
    () => OrdersService.listOrderFieldValues('salesRep'),
    'salesRep',
  );

  const ordersSourceOptions = useSelectOptionsQuery(
    apiRoutes.listOrderFieldValues('source'),
    () => OrdersService.listOrderFieldValues('source'),
    'source',
  );

  const ordersStoreNameOptions = useSelectOptionsQuery(
    apiRoutes.listOrderFieldValues('storeName'),
    () => OrdersService.listOrderFieldValues('storeName'),
    'storeName',
  );

  const ordersSalesChannelOptions = useSelectOptionsQuery(
    apiRoutes.listOrderFieldValues('salesChannel'),
    () => OrdersService.listOrderFieldValues('salesChannel'),
    'salesChannel',
  );

  useUpdateSelectEffect(
    couponsOptions,
    selectedCoupons,
    CustomerFiltersEnum.SELECTED_COUPONS,
    updateSelectedValues,
  );
  useUpdateSelectEffect(
    ordersStatusOptions,
    selectedOrdersStatuses,
    CustomerFiltersEnum.SELECTED_ORDERS_STATUSES,
    updateSelectedValues,
  );
  useUpdateSelectEffect(
    ordersSalesRepOptions,
    selectedOrdersSalesReps,
    CustomerFiltersEnum.SELECTED_ORDERS_SALES_REPS,
    updateSelectedValues,
  );
  useUpdateSelectEffect(
    ordersSourceOptions,
    selectedOrdersSources,
    CustomerFiltersEnum.SELECTED_ORDERS_SOURCES,
    updateSelectedValues,
  );
  useUpdateSelectEffect(
    ordersStoreNameOptions,
    selectedOrdersStoreNames,
    CustomerFiltersEnum.SELECTED_ORDERS_STORE_NAMES,
    updateSelectedValues,
  );
  useUpdateSelectEffect(
    ordersSalesChannelOptions,
    selectedOrdersSalesChannels,
    CustomerFiltersEnum.SELECTED_ORDERS_SALES_CHANNELS,
    updateSelectedValues,
  );

  const canSeeOrderIntegrationFilters = currentUser ? true : false;

  const orderTypeOptions = [
    { value: 'true', label: 'Inscrição' },
    { value: 'false', label: 'Regular' },
  ];

  return (
    <AccordionItemLayout title="Compras" icon={<TbZoomMoney size="18px" />}>
      <FormControl>
        <FormLabel size="sm" tooltip="Pedidos criados no período">
          Período de vendas
        </FormLabel>
        <FormLabel size="xs" marginBottom="0xp">
          De
        </FormLabel>
        <Controller
          name="startOrdersCreatedAt"
          control={control}
          render={({ field }) => (
            <ReactDatePicker
              isClearable
              selected={
                field.value
                  ? parse(field.value, 'yyyy-MM-dd', new Date())
                  : null
              }
              onSelect={(date: Date) => {
                if (!date) {
                  return field.onChange(null);
                }
                const formattedDate = format(date, 'yyyy-MM-dd');
                field.onChange(formattedDate); // Save the formatted date as a string
              }}
              onChange={(date: Date) => {
                if (!date) {
                  return field.onChange(null);
                }
                const formattedDate = format(date, 'yyyy-MM-dd');
                field.onChange(formattedDate); // Save the formatted date as a string
              }}
              dateFormat="dd/MM/yyyy"
            />
          )}
        />
        <FormLabel size="xs" marginBottom="0xp">
          Até
        </FormLabel>
        {!!watchMinDaysSinceLastPurchase ||
        !!watchMaxDaysSinceLastPurchase ||
        !!watchExactDaysSinceLastPurchase ? (
          <Text fontSize={10}>
            Desmarque a opção "Exibir/Ocultar clientes que compraram nos
            últimos" para poder alterar
          </Text>
        ) : (
          <Controller
            name="endOrdersCreatedAt"
            control={control}
            render={({ field }) => (
              <ReactDatePicker
                isClearable
                selected={
                  field.value
                    ? parse(field.value, 'yyyy-MM-dd', new Date())
                    : null
                }
                onSelect={(date: Date) => {
                  if (!date) {
                    return field.onChange(null);
                  }
                  const formattedDate = format(date, 'yyyy-MM-dd');
                  field.onChange(formattedDate); // Save the formatted date as a string
                }}
                onChange={(date: Date) => {
                  if (!date) {
                    return field.onChange(null);
                  }
                  const formattedDate = format(date, 'yyyy-MM-dd');
                  field.onChange(formattedDate); // Save the formatted date as a string
                }}
                dateFormat="dd/MM/yyyy"
              />
            )}
          />
        )}
      </FormControl>

      <FormControl>
        <FormLabel size="sm" tooltip="Total de pedidos do cliente no período">
          Total de Pedidos no Período
        </FormLabel>
        <Controller
          name="totalOrders"
          control={control}
          defaultValue={{ minValue: 0, maxValue: 0 }}
          render={({ field }) => (
            <InputRangeNumber
              size={'sm'}
              rightAddon="UN"
              onChangeMinValue={(value) =>
                field.onChange({ ...field.value, minValue: value })
              }
              onChangeMaxValue={(value) =>
                field.onChange({ ...field.value, maxValue: value })
              }
              minValue={field.value.minValue}
              maxValue={field.value.maxValue}
            />
          )}
        />
        <Flex justifyContent={'flex-end'}>
          <Text fontSize="xs">
            média no período:{' '}
            {(orderStatistics?.averageTotalOrders || 0).toFixed(0)}
          </Text>
        </Flex>
      </FormControl>
      <FormControl>
        <FormLabel size="sm" tooltip="Total em compras do cliente no período">
          Total em Compras no Período
        </FormLabel>
        <Controller
          name="totalPurchases"
          control={control}
          defaultValue={{ minValue: 0, maxValue: 0 }}
          render={({ field }) => (
            <InputRangeNumber
              leftAddon="R$"
              size={'sm'}
              onChangeMinValue={(value) =>
                field.onChange({ ...field.value, minValue: value })
              }
              onChangeMaxValue={(value) =>
                field.onChange({ ...field.value, maxValue: value })
              }
              minValue={field.value.minValue}
              maxValue={field.value.maxValue}
            />
          )}
        />
        <Flex justifyContent={'flex-end'}>
          <Text fontSize="xs">
            média no período:{' '}
            {MoneyUtils.formatCurrency(
              orderStatistics?.averageTotalPurchases || 0,
            )}
          </Text>
        </Flex>
      </FormControl>
      <FormControl>
        <FormLabel size="sm" tooltip="Valor médio dos pedidos no período">
          Ticket Médio no Período
        </FormLabel>
        <Controller
          name="averageOrderValue"
          control={control}
          defaultValue={{ minValue: 0, maxValue: 0 }}
          render={({ field }) => (
            <InputRangeNumber
              leftAddon="R$"
              size={'sm'}
              onChangeMinValue={(value) =>
                field.onChange({ ...field.value, minValue: value })
              }
              onChangeMaxValue={(value) =>
                field.onChange({ ...field.value, maxValue: value })
              }
              minValue={field.value.minValue}
              maxValue={field.value.maxValue}
            />
          )}
        />
        <Flex justifyContent={'flex-end'}>
          <Text fontSize="xs">
            média no período:{' '}
            {MoneyUtils.formatCurrency(orderStatistics?.averageOrderValue || 0)}
          </Text>
        </Flex>
      </FormControl>
      <FormControl>
        <FormLabel size="sm" tooltip="Valor dos itens comprados no período">
          Preço Médio dos Items
        </FormLabel>
        <Controller
          name="averageItemValue"
          control={control}
          defaultValue={{ minValue: 0, maxValue: 0 }}
          render={({ field }) => (
            <InputRangeNumber
              size={'sm'}
              leftAddon="R$"
              onChangeMinValue={(value) =>
                field.onChange({ ...field.value, minValue: value })
              }
              onChangeMaxValue={(value) =>
                field.onChange({ ...field.value, maxValue: value })
              }
              minValue={field.value.minValue}
              maxValue={field.value.maxValue}
            />
          )}
        />
        <Flex justifyContent={'flex-end'}>
          <Text fontSize="xs">
            média no período:{' '}
            {MoneyUtils.formatCurrency(orderStatistics?.averageItemValue || 0)}
          </Text>
        </Flex>
      </FormControl>
      <FormControl>
        <FormLabel
          size="sm"
          tooltip="Mostrar clientes que usaram um dos cupons"
        >
          Usou um dos cupons
        </FormLabel>
        <Controller
          name="selectedCoupons"
          control={control}
          render={({ field }) => {
            return (
              <InputSelect
                options={couponsOptions || []}
                isMulti
                value={field.value}
                onChange={(value) => {
                  field.onChange(value);
                }}
              />
            );
          }}
        />
      </FormControl>
      <FormControl>
        <FormLabel
          size="sm"
          tooltip="Mostrar pedidos com pelo menos um dos status selecionados"
        >
          Status do Pedido
        </FormLabel>
        <Controller
          name="selectedOrdersStatuses"
          control={control}
          render={({ field }) => {
            return (
              <InputSelect
                options={ordersStatusOptions || []}
                isMulti
                value={field.value}
                onChange={(value) => {
                  field.onChange(value);
                }}
              />
            );
          }}
        />
      </FormControl>
      <FormControl>
        <FormLabel
          size="sm"
          tooltip="Exibir clientes que compraram nos últimos X dias"
        >
          Comprou nos últimos
        </FormLabel>
        <Controller
          name="maxDaysSinceLastPurchase"
          control={control}
          render={({ field }) => {
            return (
              <InputNumber
                value={field.value}
                onChange={(value) => {
                  field.onChange(value);
                  cleanRelatedFilters();
                }}
                size="sm"
                rightAddon="dias"
              />
            );
          }}
        />
      </FormControl>
      <FormControl>
        <FormLabel size="sm" tooltip="Realizou compra com determinado vendedor">
          Comprou com o vendedor
        </FormLabel>
        <Controller
          name="selectedOrdersSalesReps"
          control={control}
          render={({ field }) => {
            return (
              <InputSelect
                options={ordersSalesRepOptions || []}
                isMulti
                value={field.value}
                onChange={(value) => {
                  field.onChange(value);
                }}
              />
            );
          }}
        />
      </FormControl>
      <FormControl>
        <FormLabel
          size="sm"
          tooltip="Ocultar clientes que compraram nos últimos X dias"
        >
          Não comprou nos últimos
        </FormLabel>
        <Controller
          name="minDaysSinceLastPurchase"
          control={control}
          render={({ field }) => {
            return (
              <InputNumber
                value={field.value}
                onChange={(value) => {
                  field.onChange(value);
                  cleanRelatedFilters();
                }}
                size="sm"
                rightAddon="dias"
              />
            );
          }}
        />
      </FormControl>
      <FormControl>
        <FormLabel
          size="sm"
          tooltip="Exibir clientes que compraram exatamente há X dias"
        >
          Comprou exatamente há
        </FormLabel>
        {(!!watchMinDaysSinceLastPurchase ||
          !!watchMaxDaysSinceLastPurchase) && (
          <Text fontSize={10}>
            Limpe os filtros "Exibir/Ocultar clientes que compraram nos últimos"
            para poder alterar
          </Text>
        )}
        <Controller
          name="exactDaysSinceLastPurchase"
          control={control}
          render={({ field }) => {
            return (
              <InputNumber
                value={field.value}
                onChange={(value) => {
                  field.onChange(value);
                  setValue('endOrdersCreatedAt', '');
                }}
                size="sm"
                rightAddon="dias"
                isDisabled={
                  !!watchMinDaysSinceLastPurchase ||
                  !!watchMaxDaysSinceLastPurchase
                }
              />
            );
          }}
        />
      </FormControl>
      <FormControl>
        <FormLabel
          size="sm"
          tooltip="Filtrar por tipo de pedido: inscrição ou regular"
        >
          Tipo de Pedido
        </FormLabel>
        <Controller
          name="isOrderSubscription"
          control={control}
          render={({ field }) => {
            return (
              <Select
                size="md"
                bg="white"
                placeholder="Selecione o tipo de pedido"
                value={field.value === undefined ? '' : String(field.value)}
                onChange={(e) => {
                  const value = e.target.value;
                  if (value === '') {
                    field.onChange(undefined);
                  } else {
                    field.onChange(value === 'true');
                  }
                }}
              >
                <option value="true">Inscrição</option>
                <option value="false">Regular</option>
              </Select>
            );
          }}
        />
      </FormControl>
      {canSeeOrderIntegrationFilters &&
        VITE_ALLOWED_COMPANIES_TO_SEE_ORDER_INTEGRATION_FILTERS.split(
          ',',
        ).includes(currentUser?.companyId!) && (
          <>
            <FormControl>
              <FormLabel
                size="sm"
                tooltip="Filtra compras realizadas através de uma integração específica."
              >
                Nome da Integração
              </FormLabel>
              <Controller
                name="selectedOrdersSources"
                control={control}
                render={({ field }) => {
                  return (
                    <InputSelect
                      options={
                        ordersSourceOptions.map((option) => ({
                          label: StringUtils.snakeToTitleCase(option.label),
                          value: option.value,
                        })) || []
                      }
                      isMulti
                      value={field.value}
                      onChange={(value) => {
                        field.onChange(value);
                      }}
                    />
                  );
                }}
              />
            </FormControl>
            <FormControl>
              <FormLabel
                size="sm"
                tooltip="Exibe clientes que fizeram compras em uma loja específica."
              >
                Nome da Loja
              </FormLabel>
              <Controller
                name="selectedOrdersStoreNames"
                control={control}
                render={({ field }) => {
                  return (
                    <InputSelect
                      options={ordersStoreNameOptions || []}
                      isMulti
                      value={field.value}
                      onChange={(value) => {
                        field.onChange(value);
                      }}
                    />
                  );
                }}
              />
            </FormControl>
            <FormControl>
              <FormLabel
                size="sm"
                tooltip="Filtra clientes com compras feitas por um canal de vendas específico."
              >
                Canal de vendas
              </FormLabel>
              <Controller
                name="selectedOrdersSalesChannels"
                control={control}
                render={({ field }) => {
                  return (
                    <InputSelect
                      options={ordersSalesChannelOptions || []}
                      isMulti
                      value={field.value}
                      onChange={(value) => {
                        field.onChange(value);
                      }}
                    />
                  );
                }}
              />
            </FormControl>
          </>
        )}
    </AccordionItemLayout>
  );
};

export default SectionPurchases;
