import {
  Button,
  FormControl,
  FormLabel,
  Modal,
  Modal<PERSON>ody,
  ModalClose<PERSON>utton,
  Modal<PERSON>ontent,
  <PERSON>dal<PERSON>ooter,
  ModalHeader,
  ModalOverlay,
  Select,
  Text,
  Alert,
  AlertIcon,
  AlertDescription,
  Spinner,
  Box,
  Link,
  Flex,
} from '@chakra-ui/react';
import { useEffect, useState } from 'react';
import { useQuery } from 'react-query';
import { User } from '../../../../../types/Prisma';
import { ConversationSectorsService } from '../../../../../services/conversation-sectors.service';
import { ConversationSectorWithIncludes } from '../../../../../types/ConversationSector';
import { dataTestIds } from '../../../../../constants/data-testids';
import { appPaths } from '../../../../../constants/app-paths';
import { useNavigate } from 'react-router-dom';

interface AgentAssignmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (agentId: string, categoryId?: string) => void;
  companyAgents: User[];
  preSelectedAgentId?: string;
  isLoading?: boolean;
}

const ModalAgentAssignment = ({
  isOpen,
  onClose,
  onConfirm,
  companyAgents,
  preSelectedAgentId,
  isLoading = false,
}: AgentAssignmentModalProps) => {
  const [selectedAgentId, setSelectedAgentId] = useState<string>('');
  const [selectedCategoryId, setSelectedCategoryId] = useState<string>('');
  const navigate = useNavigate();
  const selectedAgent = companyAgents.find(
    (agent) => agent.id === selectedAgentId,
  );

  const { data: agentSectors = [], isLoading: isLoadingSectors } = useQuery(
    ['agent-sectors', selectedAgentId],
    async (): Promise<ConversationSectorWithIncludes[]> => {
      if (!selectedAgentId) return [];
      const { data } =
        await ConversationSectorsService.getUserSectorsAndCategories(
          selectedAgentId,
        );
      return data;
    },
    {
      enabled: !!selectedAgentId && isOpen,
    },
  );

  const availableCategories = agentSectors.flatMap((sector) =>
    sector.categories.filter((cat) => !cat.isDeleted),
  );

  const hasAgentCategories = availableCategories.length > 0;
  const isCategoryRequired = selectedAgent && hasAgentCategories;
  const canAssignWithoutCategory =
    selectedAgent &&
    !hasAgentCategories &&
    selectedAgent.canViewAllConversations;
  const cannotAssign =
    selectedAgent &&
    !hasAgentCategories &&
    !selectedAgent.canViewAllConversations;

  useEffect(() => {
    if (isOpen && preSelectedAgentId) {
      setSelectedAgentId(preSelectedAgentId);
      setSelectedCategoryId('');
    }
  }, [isOpen, preSelectedAgentId]);

  useEffect(() => {
    setSelectedCategoryId('');
  }, [selectedAgentId]);

  const handleAgentChange = (agentId: string) => {
    setSelectedAgentId(agentId);
  };

  const handleConfirm = () => {
    if (!selectedAgentId || cannotAssign) return;
    if (isCategoryRequired && !selectedCategoryId) return;
    onConfirm(selectedAgentId, selectedCategoryId || undefined);
  };

  const handleClose = () => {
    setSelectedAgentId('');
    setSelectedCategoryId('');
    onClose();
  };

  const isConfirmDisabled =
    !selectedAgentId ||
    (isCategoryRequired && !selectedCategoryId) ||
    cannotAssign;

  const getAgentDisplayText = (agent: User) => {
    return agent.canViewAllConversations
      ? `${agent.name} (Acesso total)`
      : `${agent.name} (Acesso restrito)`;
  };

  const getCategoryDisplayText = (category: any, sectorName: string) => {
    return `${category.name} (${sectorName})`;
  };

  const renderCategorySelection = () => {
    if (!selectedAgentId) {
      return (
        <FormControl>
          <FormLabel>Categoria</FormLabel>
          <Select
            isDisabled
            placeholder="Selecione um responsável primeiro"
            data-testid={
              dataTestIds.inboxPage.chat.modalAgentAssignment.categorySelect
            }
          />
        </FormControl>
      );
    }

    if (isLoadingSectors) {
      return (
        <FormControl>
          <FormLabel>Categoria</FormLabel>
          <Box
            display="flex"
            alignItems="center"
            gap={2}
            p={3}
            border="1px solid"
            borderColor="gray.200"
            borderRadius="md"
            data-testid={
              dataTestIds.inboxPage.chat.modalAgentAssignment
                .categoryLoadingState
            }
          >
            <Spinner size="sm" />
            <Text>Buscando categorias do agente...</Text>
          </Box>
        </FormControl>
      );
    }

    if (!hasAgentCategories) {
      return (
        <FormControl>
          <FormLabel>Categoria</FormLabel>
          <Select
            isDisabled
            placeholder="Este agente não possui categorias atribuídas"
            data-testid={
              dataTestIds.inboxPage.chat.modalAgentAssignment.categorySelect
            }
          />
        </FormControl>
      );
    }

    return (
      <FormControl>
        <FormLabel>Categoria *</FormLabel>
        <Select
          value={selectedCategoryId}
          onChange={(e) => setSelectedCategoryId(e.target.value)}
          placeholder="Selecione uma categoria"
          data-testid={
            dataTestIds.inboxPage.chat.modalAgentAssignment.categorySelect
          }
        >
          {agentSectors.map((sector) =>
            sector.categories
              .filter((cat) => !cat.isDeleted)
              .map((category) => (
                <option key={category.id} value={category.id}>
                  {getCategoryDisplayText(category, sector.name)}
                </option>
              )),
          )}
        </Select>
      </FormControl>
    );
  };

  const renderStatusAlert = () => {
    if (!selectedAgent || isLoadingSectors) return null;

    if (hasAgentCategories) {
      return (
        <Alert
          status="info"
          data-testid={
            dataTestIds.inboxPage.chat.modalAgentAssignment
              .categoryRequiredAlert
          }
        >
          <AlertIcon />
          <AlertDescription>
            <strong>Categorias encontradas:</strong> Este agente possui
            categorias atribuídas. É obrigatório selecionar uma categoria para
            continuar.
          </AlertDescription>
        </Alert>
      );
    }

    if (canAssignWithoutCategory) {
      return (
        <Alert
          status="success"
          data-testid={
            dataTestIds.inboxPage.chat.modalAgentAssignment.canAssignAlert
          }
        >
          <AlertIcon />
          <AlertDescription>
            <strong>Pode ser atribuído:</strong> Este agente não possui
            categorias específicas, mas tem permissão para ver todas as
            conversas.
          </AlertDescription>
        </Alert>
      );
    }

    if (cannotAssign) {
      return (
        <Alert
          status="error"
          data-testid={
            dataTestIds.inboxPage.chat.modalAgentAssignment.cannotAssignAlert
          }
        >
          <AlertIcon />
          <AlertDescription>
            <strong>Não pode ser atribuído:</strong> Este agente não possui
            categorias atribuídas e não tem permissão para ver todas as
            conversas.
            <br />
            <Link
              textDecoration="underline"
              onClick={() => navigate(appPaths.settings.access())}
              data-testid={
                dataTestIds.inboxPage.chat.modalAgentAssignment.createSectorLink
              }
            >
              Adicionar um setor ao usuário
            </Link>
          </AlertDescription>
        </Alert>
      );
    }

    return null;
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      isCentered
      size="lg"
      data-testid={dataTestIds.inboxPage.chat.modalAgentAssignment.modal}
    >
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>Atribuir Responsável e Categoria</ModalHeader>
        <ModalCloseButton
          data-testid={
            dataTestIds.inboxPage.chat.modalAgentAssignment.closeButton
          }
        />
        <ModalBody>
          <FormControl mb={4}>
            <FormLabel>Responsável *</FormLabel>
            <Select
              value={selectedAgentId}
              onChange={(e) => handleAgentChange(e.target.value)}
              placeholder="Selecione um responsável"
              data-testid={
                dataTestIds.inboxPage.chat.modalAgentAssignment.agentSelect
              }
            >
              {companyAgents.map((agent) => (
                <option key={agent.id} value={agent.id}>
                  {getAgentDisplayText(agent)}
                </option>
              ))}
            </Select>
          </FormControl>

          <Box mb={4}>{renderCategorySelection()}</Box>

          {renderStatusAlert()}

          {selectedAgent && !isLoadingSectors && (
            <Box
              mt={4}
              p={3}
              bg="gray.50"
              borderRadius="md"
              data-testid={
                dataTestIds.inboxPage.chat.modalAgentAssignment.helpText
              }
            >
              <Text fontSize="sm" fontWeight="bold" mb={2}>
                Como funciona a atribuição:
              </Text>
              <Text fontSize="xs" color="gray.600">
                • <strong>Com categorias:</strong> Obrigatório selecionar uma
                categoria
                <br />• <strong>Sem categorias + Acesso total:</strong> Pode ser
                atribuído sem categoria
                <br />• <strong>Sem categorias + Sem acesso:</strong> Não pode
                ser atribuído
              </Text>
            </Box>
          )}
        </ModalBody>

        <ModalFooter>
          <Flex width="100%" justify="space-between" gap={4}>
            <Button
              variant="outline"
              onClick={handleClose}
              data-testid={
                dataTestIds.inboxPage.chat.modalAgentAssignment.cancelButton
              }
            >
              Cancelar
            </Button>
            <Button
              onClick={handleConfirm}
              variant="primary"
              isLoading={isLoading}
              isDisabled={isConfirmDisabled}
              data-testid={
                dataTestIds.inboxPage.chat.modalAgentAssignment.confirmButton
              }
            >
              {cannotAssign
                ? 'Não é possível atribuir'
                : 'Confirmar Atribuição'}
            </Button>
          </Flex>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default ModalAgentAssignment;
