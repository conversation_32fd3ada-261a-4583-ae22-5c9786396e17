import React from 'react';
import {
  Box,
  Button,
  Text,
  VStack,
  HStack,
  Icon,
  useToast,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Badge,
} from '@chakra-ui/react';
import { FiBell, FiBellOff, FiSend, FiDownload } from 'react-icons/fi';
import { usePushNotifications } from '../../../../../../hooks/usePushNotifications';

export const PushNotificationSettings: React.FC = () => {
  const {
    isSupported,
    isEnabled,
    permission,
    isLoading,
    isIOS,
    isPWAInstalled,
    enablePushNotifications,
    disablePushNotifications,
    showPWAInstallInstructions,
    sendTestPushNotificationMutation,
  } = usePushNotifications();

  const toast = useToast();

  const handleEnableNotifications = async (): Promise<void> => {
    try {
      await enablePushNotifications();
      toast({
        title: 'Notificações ativadas',
        description:
          'Você receberá notificações push quando houver novas mensagens.',
        status: 'success',
        duration: 5000,
        isClosable: true,
      });
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Tente novamente mais tarde.';

      if (
        errorMessage.includes('PWA') ||
        errorMessage.includes('instale o app')
      ) {
        toast({
          title: 'Instale como PWA',
          description: errorMessage,
          status: 'warning',
          duration: 8000,
          isClosable: true,
        });
      } else if (errorMessage.includes('iOS')) {
        toast({
          title: 'Limitação do dispositivo',
          description: errorMessage,
          status: 'warning',
          duration: 8000,
          isClosable: true,
        });
      } else {
        toast({
          title: 'Erro ao ativar notificações',
          description: errorMessage,
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      }
    }
  };

  const handleDisableNotifications = async (): Promise<void> => {
    try {
      await disablePushNotifications();
      toast({
        title: 'Notificações desativadas',
        description: 'Você não receberá mais notificações push.',
        status: 'info',
        duration: 3000,
        isClosable: true,
      });
    } catch (err) {
      toast({
        title: 'Erro ao desativar notificações',
        description:
          err instanceof Error ? err.message : 'Tente novamente mais tarde.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  const handleSendTestNotification = async (): Promise<void> => {
    try {
      await sendTestPushNotificationMutation.mutateAsync();
      toast({
        title: 'Notificação de teste enviada',
        description: 'Verifique se você recebeu a notificação.',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (err) {
      toast({
        title: 'Erro ao enviar notificação de teste',
        description:
          err instanceof Error ? err.message : 'Tente novamente mais tarde.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  const getDeviceStatusBadge = () => {
    if (isIOS && !isPWAInstalled) {
      return <Badge colorScheme="orange">iOS - Instale como PWA</Badge>;
    }
    if (isIOS && isPWAInstalled) {
      return <Badge colorScheme="green">iOS - PWA Instalado</Badge>;
    }
    if (isIOS) {
      return <Badge colorScheme="yellow">iOS</Badge>;
    }
    return <Badge colorScheme="blue">Outro dispositivo</Badge>;
  };

  return (
    <VStack spacing={6} align="stretch">
      <Box>
        <Text fontSize="lg" fontWeight="bold" mb={2}>
          Configurações de Notificações Push
        </Text>
        <Text fontSize="sm" color="gray.600" mb={4}>
          Receba notificações mesmo quando o navegador estiver fechado.
        </Text>

        <HStack spacing={2} mb={4}>
          <Text fontSize="sm">Dispositivo:</Text>
          {getDeviceStatusBadge()}
        </HStack>
      </Box>

      <VStack spacing={3} align="stretch">
        {isEnabled ? (
          <Button
            leftIcon={<FiBellOff />}
            colorScheme="red"
            variant="outline"
            onClick={handleDisableNotifications}
            isLoading={isLoading}
            loadingText="Desativando..."
          >
            Desativar notificações neste navegador
          </Button>
        ) : (
          <Button
            leftIcon={<FiBell />}
            colorScheme="blue"
            onClick={handleEnableNotifications}
            isLoading={isLoading}
            loadingText="Ativando..."
            isDisabled={permission === 'denied' || !isSupported}
          >
            Ativar notificações neste navegador
          </Button>
        )}

        {isEnabled && (
          <Button
            leftIcon={<Icon as={FiSend} />}
            colorScheme="blue"
            onClick={handleSendTestNotification}
            isLoading={isLoading}
            isDisabled={!isEnabled}
          >
            Enviar Notificação de Teste
          </Button>
        )}

        {isIOS && !isPWAInstalled && (
          <Button
            leftIcon={<FiDownload />}
            colorScheme="purple"
            variant="outline"
            onClick={showPWAInstallInstructions}
          >
            Como instalar como PWA
          </Button>
        )}
      </VStack>

      {permission === 'denied' && (
        <Alert status="warning">
          <AlertIcon />
          <Box>
            <AlertTitle>Permissão negada</AlertTitle>
            <AlertDescription>
              Para receber notificações, você precisa permitir notificações nas
              configurações do seu navegador.
            </AlertDescription>
          </Box>
        </Alert>
      )}

      {isIOS && !isPWAInstalled && (
        <Alert status="warning">
          <AlertIcon />
          <Box>
            <AlertTitle>Instale como PWA no iOS</AlertTitle>
            <AlertDescription>
              Para receber notificações push no iOS, você precisa instalar o app
              como PWA:
              <br />
              1. Toque no botão de compartilhar (ícone quadrado com seta)
              <br />
              2. Role para baixo e toque em "Adicionar à Tela Inicial"
              <br />
              3. Toque em "Adicionar"
              <br />
              4. Abra o app da tela inicial e tente ativar as notificações
              novamente
            </AlertDescription>
          </Box>
        </Alert>
      )}

      {isIOS && isPWAInstalled && (
        <Alert status="success">
          <AlertIcon />
          <Box>
            <AlertTitle>PWA instalado</AlertTitle>
            <AlertDescription>
              Ótimo! O app está instalado como PWA. Agora você pode ativar as
              notificações push.
            </AlertDescription>
          </Box>
        </Alert>
      )}

      {!isSupported && (
        <Alert status="error">
          <AlertIcon />
          <Box>
            <AlertTitle>Navegador não suportado</AlertTitle>
            <AlertDescription>
              Seu navegador não suporta notificações push. Tente usar um
              navegador mais recente como Chrome, Firefox ou Edge.
            </AlertDescription>
          </Box>
        </Alert>
      )}
    </VStack>
  );
};
