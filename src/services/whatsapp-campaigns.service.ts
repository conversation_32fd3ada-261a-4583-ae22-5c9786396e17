import { PaginatedResponse } from './../types/PaginatedResponse.d';
import { AxiosResponse } from 'axios';
import { apiRoutes } from '../constants/api-routes';
import { request } from '../constants/request';
import { WhatsappCampaignStatsData } from '../types/WhatsappCampaignsData';

const listWhatsappCampaigns = async (
  page: number,
  perPage: number,
  templateIds?: string,
): Promise<AxiosResponse<PaginatedResponse<WhatsappCampaignStatsData>>> => {
  return request.get(
    apiRoutes.listWhatsappCampaigns(page, perPage, templateIds),
  );
};

const getWhatsappCampaignDetails = async (
  campaignId: string,
): Promise<AxiosResponse<WhatsappCampaignStatsData>> => {
  return request.get(apiRoutes.getWhatsappCampaignDetails(campaignId));
};

export interface SendOrScheduleWhatsappCampaignDto {
  templateId: string;
  customerIds: string[];
  templateArgs?: {
    [key: string]: string | undefined;
  };
  filterCriteria?: string;
  scheduledExecutionTime?: string | null;
}

const buildFormDataForWhatsappCampaign = (
  SendOrScheduleWhatsappCampaignDto: SendOrScheduleWhatsappCampaignDto,
): FormData => {
  const formData = new FormData();

  const customerIdsBlob = new Blob(
    [JSON.stringify(SendOrScheduleWhatsappCampaignDto.customerIds)],
    { type: 'application/json' },
  );
  formData.append('file', customerIdsBlob, 'customerIds.json');

  formData.append('templateId', SendOrScheduleWhatsappCampaignDto.templateId);

  if (SendOrScheduleWhatsappCampaignDto.templateArgs) {
    formData.append(
      'templateArgs',
      JSON.stringify(SendOrScheduleWhatsappCampaignDto.templateArgs),
    );
  }

  if (SendOrScheduleWhatsappCampaignDto.filterCriteria) {
    formData.append(
      'filterCriteria',
      SendOrScheduleWhatsappCampaignDto.filterCriteria,
    );
  }

  if (SendOrScheduleWhatsappCampaignDto.scheduledExecutionTime) {
    formData.append(
      'scheduledExecutionTime',
      SendOrScheduleWhatsappCampaignDto.scheduledExecutionTime,
    );
  }

  return formData;
};

const sendOrScheduleWhatsappCampaign = async (
  SendOrScheduleWhatsappCampaignDto: SendOrScheduleWhatsappCampaignDto,
) => {
  const formData = buildFormDataForWhatsappCampaign(
    SendOrScheduleWhatsappCampaignDto,
  );

  return request.post(apiRoutes.sendOrScheduleWhatsappCampaign(), formData, {
    timeout: 90000,
    headers: { 'Content-Type': 'multipart/form-data' },
  });
};

const cancelWhatsappCampaign = async (campaignId: string) => {
  return request.post(apiRoutes.cancelWhatsappCampaign(campaignId));
};

const listCustomersOptOut = async (campaignId: string) => {
  return request.get(apiRoutes.listCustomersOptOut(campaignId));
};

const listWhatsappCampaignDetailedReplies = async (campaignId: string) => {
  return request.get(apiRoutes.listWhatsappCampaignDetailedReplies(campaignId));
};

export const WhatsappCampaignsService = {
  listWhatsappCampaigns,
  getWhatsappCampaignDetails,
  sendOrScheduleWhatsappCampaign,
  cancelWhatsappCampaign,
  listCustomersOptOut,
  listWhatsappCampaignDetailedReplies,
};
