import React from 'react';
import {
  Center,
  VStack,
  Text,
  Card,
  CardBody,
  FormControl,
  FormLabel,
  FormErrorMessage,
  Input,
  Textarea,
  Flex,
} from '@chakra-ui/react';
import { Controller, Control, FieldErrors } from 'react-hook-form';
import { CatalogFormData, CatalogType } from '../../../../../../types/Catalog';

interface CatalogContentStepProps {
  control: Control<CatalogFormData>;
  errors: FieldErrors<CatalogFormData>;
  selectedType: CatalogType;
}

export const CatalogContentStep = ({
  control,
  errors,
  selectedType,
}: CatalogContentStepProps) => {
  return (
    <Center flex="1">
      <VStack spacing={6} maxW="2xl" w="full">
        <VStack spacing={2} textAlign="center">
          <Text fontSize="lg" color="gray.700">
            Configure o conteúdo da sua mensagem
          </Text>
          <Text fontSize="sm" color="gray.500">
            Crie uma mensagem atrativa para engajar seus clientes
          </Text>
        </VStack>

        <Card w="full" variant="outline">
          <CardBody p={6}>
            <VStack spacing={6} align="stretch">
              {selectedType.needsHeader && (
                <FormControl isInvalid={!!errors.header} isRequired>
                  <FormLabel color="gray.700" fontWeight="semibold">
                    Título da Mensagem
                  </FormLabel>
                  <Controller
                    name="header"
                    control={control}
                    rules={{
                      required: selectedType.needsHeader
                        ? 'Título é obrigatório'
                        : false,
                      maxLength: { value: 60, message: 'Máximo 60 caracteres' },
                    }}
                    render={({ field }) => (
                      <VStack align="stretch" spacing={2}>
                        <Input
                          {...field}
                          placeholder="Ex: Confira nossas novidades!"
                          maxLength={60}
                          bg="gray.50"
                          border="2px"
                          borderColor="gray.200"
                          _focus={{ borderColor: 'blue.400', bg: 'white' }}
                          size="lg"
                        />
                        <Flex justify="space-between" align="center">
                          <Text fontSize="xs" color="gray.500">
                            Use um título chamativo e direto
                          </Text>
                          <Text fontSize="xs" color="gray.400">
                            {field.value?.length || 0}/60
                          </Text>
                        </Flex>
                      </VStack>
                    )}
                  />
                  <FormErrorMessage>{errors.header?.message}</FormErrorMessage>
                </FormControl>
              )}

              <FormControl isInvalid={!!errors.body} isRequired>
                <FormLabel color="gray.700" fontWeight="semibold">
                  Conteúdo Principal
                </FormLabel>
                <Controller
                  name="body"
                  control={control}
                  rules={{
                    required: 'Conteúdo é obrigatório',
                    maxLength: {
                      value: 1024,
                      message: 'Máximo 1024 caracteres',
                    },
                  }}
                  render={({ field }) => (
                    <VStack align="stretch" spacing={2}>
                      <Textarea
                        {...field}
                        placeholder="Descreva os produtos, ofertas especiais ou informações importantes..."
                        rows={6}
                        maxLength={1024}
                        bg="gray.50"
                        border="2px"
                        borderColor="gray.200"
                        _focus={{ borderColor: 'blue.400', bg: 'white' }}
                        resize="none"
                      />
                      <Flex justify="space-between" align="center">
                        <Text fontSize="xs" color="gray.500">
                          Seja claro e persuasivo na descrição
                        </Text>
                        <Text fontSize="xs" color="gray.400">
                          {field.value?.length || 0}/1024
                        </Text>
                      </Flex>
                    </VStack>
                  )}
                />
                <FormErrorMessage>{errors.body?.message}</FormErrorMessage>
              </FormControl>

              <FormControl isInvalid={!!errors.footer}>
                <FormLabel color="gray.700" fontWeight="semibold">
                  Rodapé (Opcional)
                </FormLabel>
                <Controller
                  name="footer"
                  control={control}
                  rules={{
                    maxLength: { value: 60, message: 'Máximo 60 caracteres' },
                  }}
                  render={({ field }) => (
                    <VStack align="stretch" spacing={2}>
                      <Input
                        {...field}
                        placeholder="Ex: Aproveite enquanto há estoque!"
                        maxLength={60}
                        bg="gray.50"
                        border="2px"
                        borderColor="gray.200"
                        _focus={{ borderColor: 'blue.400', bg: 'white' }}
                      />
                      <Flex justify="space-between" align="center">
                        <Text fontSize="xs" color="gray.500">
                          Adicione uma chamada para ação
                        </Text>
                        <Text fontSize="xs" color="gray.400">
                          {field.value?.length || 0}/60
                        </Text>
                      </Flex>
                    </VStack>
                  )}
                />
                <FormErrorMessage>{errors.footer?.message}</FormErrorMessage>
              </FormControl>
            </VStack>
          </CardBody>
        </Card>
      </VStack>
    </Center>
  );
};
