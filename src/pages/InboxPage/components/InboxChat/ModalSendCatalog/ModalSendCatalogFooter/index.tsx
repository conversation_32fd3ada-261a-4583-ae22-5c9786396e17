import React from 'react';
import {
  ModalFooter as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>oot<PERSON>,
  <PERSON>lex,
  Button,
  HStack,
  Text,
} from '@chakra-ui/react';
import { ChevronLeftIcon, ChevronRightIcon } from '@chakra-ui/icons';

interface ModalSendCatalogFooterProps {
  step: string;
  canProceed: boolean;
  selectedProductsLength: number;
  maxProducts: number;
  isLoading: boolean;
  onClose: () => void;
  onBack: () => void;
  onNext: () => void;
  onSubmit: () => void;
}

export const ModalSendCatalogFooter = ({
  step,
  canProceed,
  selectedProductsLength,
  maxProducts,
  isLoading,
  onClose,
  onBack,
  onNext,
  onSubmit,
}: ModalSendCatalogFooterProps) => {
  return (
    <ChakraModalFooter
      bg="gray.50"
      py={4}
      px={6}
      borderTop="1px"
      borderColor="gray.200"
    >
      <Flex width="100%" justify="space-between" align="center">
        <Button
          variant="outline"
          onClick={step === 'type' ? onClose : onBack}
          size="lg"
          colorScheme="gray"
          leftIcon={step !== 'type' ? <ChevronLeftIcon /> : undefined}
        >
          {step === 'type' ? 'Cancelar' : 'Voltar'}
        </Button>

        <HStack spacing={3}>
          {step === 'products' && selectedProductsLength > 0 && (
            <Text fontSize="sm" color="gray.600">
              {selectedProductsLength} de{' '}
              {maxProducts === Infinity ? '∞' : maxProducts} produtos
            </Text>
          )}

          {step === 'review' ? (
            <Button
              variant="primary"
              onClick={onSubmit}
              isLoading={isLoading}
              loadingText="Enviando..."
              size="lg"
              px={8}
            >
              Enviar Catálogo
            </Button>
          ) : (
            <Button
              variant="primary"
              onClick={onNext}
              isDisabled={!canProceed}
              size="lg"
              px={8}
              rightIcon={<ChevronRightIcon />}
            >
              {step === 'products' ? 'Revisar' : 'Próximo'}
            </Button>
          )}
        </HStack>
      </Flex>
    </ChakraModalFooter>
  );
};
