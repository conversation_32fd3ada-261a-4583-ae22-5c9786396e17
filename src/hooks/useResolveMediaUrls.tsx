import { useState, useEffect } from 'react';
import { useSignedUrl } from './useSignedUrl';

interface UseResolveMediaUrlsProps {
  fileKey?: string | null;
  initialMediaUrl?: string | null;
}

export const useResolveMediaUrls = ({
  fileKey,
  initialMediaUrl,
}: UseResolveMediaUrlsProps) => {
  const [mediaUrl, setMediaUrl] = useState<string | null>(
    initialMediaUrl ?? null,
  );
  const [fileKeyImage, setFileKeyImage] = useState<string | null>(
    fileKey ?? null,
  );
  const { mutateAsync: getSignedUrl } = useSignedUrl();

  useEffect(() => {
    const resolve = async () => {
      if (fileKeyImage && !mediaUrl) {
        const { data } = await getSignedUrl(fileKeyImage);
        setMediaUrl(data.url);
        setFileKeyImage(null);
      }
    };
    resolve();
  }, [fileKeyImage, mediaUrl, getSignedUrl]);

  return {
    mediaUrl,
    setMediaUrl,
    fileKeyImage,
  };
};
