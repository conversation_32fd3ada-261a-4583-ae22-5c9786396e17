import { Box, Button, Flex, Heading, Text } from '@chakra-ui/react';
import { AddIcon } from '@chakra-ui/icons';
import { FaTrash } from 'react-icons/fa';

interface FaqHeaderProps {
  selectedFaqIds: string[];
  onDeleteMany: () => void;
  onOpenCreateModal: () => void;
}

export const FaqHeader = ({
  selectedFaqIds,
  onDeleteMany,
  onOpenCreateModal,
}: FaqHeaderProps) => {
  return (
    <Flex width="100%" justifyContent="space-between" paddingBottom="16px">
      <Box>
        <Heading size="lg" mb={1}>
          Perguntas Frequentes (FAQ)
        </Heading>
        <Text color="gray.500" mb={8}>
          <PERSON><PERSON><PERSON><PERSON> as perguntas frequentes para ajudar seus clientes e
          atendentes.
        </Text>
      </Box>
      <Flex>
        {selectedFaqIds.length > 0 && (
          <Button
            leftIcon={<FaTrash />}
            colorScheme="red"
            variant="outline"
            onClick={onDeleteMany}
            mr={4}
          >
            Deletar selecionadas ({selectedFaqIds.length})
          </Button>
        )}
        <Button
          leftIcon={<AddIcon />}
          variant="primary"
          onClick={onOpenCreateModal}
        >
          FAQ
        </Button>
      </Flex>
    </Flex>
  );
};
