import { AxiosResponse } from 'axios';
import { apiRoutes } from '../constants/api-routes';
import { request } from '../constants/request';
import {
  EmailCampaignData,
  EmailCampaignsData,
} from '../types/EmailCampaignData';

export interface EmailCampaignsSummary {
  totalEmailCampaigns: number;
  totalEmailsSent: number;
  totalEmailsDelivered: number;
  totalEmailsOpened: number;
  totalEmailsEngaged: number;
  totalEmailsBounced: number;
}

const listEmailCampaigns = async (
  page: number,
  perPage: number,
): Promise<AxiosResponse<EmailCampaignsData>> => {
  return request.get(apiRoutes.listEmailCampaigns(page, perPage));
};

export interface SendOrScheduleEmailCampaignDto {
  emailTemplateId: string;
  customerIds: string[];
  templateArgs?: {
    [key: string]: string | undefined;
  };
  filterCriteria?: string;
  scheduledExecutionTime?: string | null;
}

const buildFormDataForEmailCampaign = (
  SendOrScheduleEmailCampaignDto: SendOrScheduleEmailCampaignDto,
): FormData => {
  const formData = new FormData();

  const customerIdsBlob = new Blob(
    [JSON.stringify(SendOrScheduleEmailCampaignDto.customerIds)],
    { type: 'application/json' },
  );
  formData.append('file', customerIdsBlob, 'customerIds.json');

  formData.append(
    'emailTemplateId',
    SendOrScheduleEmailCampaignDto.emailTemplateId,
  );

  if (SendOrScheduleEmailCampaignDto.templateArgs) {
    formData.append(
      'templateArgs',
      JSON.stringify(SendOrScheduleEmailCampaignDto.templateArgs),
    );
  }

  if (SendOrScheduleEmailCampaignDto.filterCriteria) {
    formData.append(
      'filterCriteria',
      SendOrScheduleEmailCampaignDto.filterCriteria,
    );
  }

  if (SendOrScheduleEmailCampaignDto.scheduledExecutionTime) {
    formData.append(
      'scheduledExecutionTime',
      SendOrScheduleEmailCampaignDto.scheduledExecutionTime,
    );
  }

  return formData;
};

const sendOrScheduleEmailCampaign = async (
  SendOrScheduleEmailCampaignDto: SendOrScheduleEmailCampaignDto,
) => {
  const formData = buildFormDataForEmailCampaign(
    SendOrScheduleEmailCampaignDto,
  );

  return request.post(apiRoutes.sendOrScheduleEmailCampaign(), formData, {
    timeout: 180000,
    headers: { 'Content-Type': 'multipart/form-data' },
  });
};

const getEmailCampaignDetails = async (
  emailCampaignId: string,
): Promise<EmailCampaignData> => {
  return (await request.get(apiRoutes.getEmailCampaignDetails(emailCampaignId)))
    .data;
};

const cancelEmailCampaign = async (emailCampaignId: string) => {
  return request.post(apiRoutes.cancelEmailCampaign(emailCampaignId));
};

const getEmailCampaignsSummary = async (): Promise<EmailCampaignsSummary> => {
  return (await request.get(apiRoutes.getEmailCampaignsSummary())).data;
};

export const EmailCampaignsService = {
  sendOrScheduleEmailCampaign,
  listEmailCampaigns,
  cancelEmailCampaign,
  getEmailCampaignDetails,
  getEmailCampaignsSummary,
};
