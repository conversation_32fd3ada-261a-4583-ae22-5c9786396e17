import InboxToolbar, { InboxToolbarAction, InboxToolbarProps } from '.';
import { useMutation } from 'react-query';
import { ConversationsService } from '../../../../services/conversations.service';
import { Conversation } from '../../../../types/Conversation';
import { useToast } from '@chakra-ui/react';
import { useDispatch } from 'react-redux';
import { AppDispatch } from '../../../../state/store';
import {
  markManyConversationsAsUnread,
  clearConversationsSelection,
} from '../../../../state/inboxSlice';
import { useState } from 'react';
import { MassiveActionsSelect } from './MassiveActionSelect';
import AlertDialogBase from '../../../../components/AlertDialog';

type BulkActionType = 'finish' | 'markAsUnread' | null;

interface OpenConversationToolbarProps
  extends Omit<InboxToolbarProps, 'actions'> {
  selectedConversationsIds?: string[];
  onConversationsTicketsClosed?: (data: Conversation[]) => void;
}

const OpenConversationToolbar = ({
  statusMessage,
  selectedConversationsIds = [],
  totalCount,
  selectedCount = 0,
  onConversationsTicketsClosed,
}: OpenConversationToolbarProps) => {
  const toast = useToast();
  const dispatch = useDispatch<AppDispatch>();
  const [selectedAction, setSelectedAction] = useState<BulkActionType>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const closeManyConversationsTicketsMutation = useMutation(
    async (conversationIds: string[]): Promise<Conversation[]> => {
      const { data } =
        await ConversationsService.closeAllTicketsFromManyConversations(
          conversationIds,
        );
      return data;
    },
    {
      onSuccess: (data: Conversation[]) => {
        onConversationsTicketsClosed?.(data);
        const successCount = data.length;
        const plural = successCount > 1 ? 's' : '';
        toast({
          title: `${successCount} conversa${plural} selecionada${plural} movida${plural} para "finalizado"`,
          status: 'info',
          duration: 3000,
          isClosable: true,
        });
        setIsModalOpen(false);
        setSelectedAction(null);
        dispatch(clearConversationsSelection());
      },
    },
  );

  const markManyConversationsAsUnreadMutation = useMutation(
    async (conversationIds: string[]): Promise<boolean> => {
      const { data } =
        await ConversationsService.markManyConversationsAsUnread(
          conversationIds,
        );
      return data;
    },
    {
      onSuccess: () => {
        dispatch(
          markManyConversationsAsUnread({
            conversationIds: selectedConversationsIds,
          }),
        );

        const successCount = selectedConversationsIds.length;
        const plural = successCount > 1 ? 's' : '';
        toast({
          title: `${successCount} conversa${plural} selecionada${plural} marcada${plural} como não lida${plural}`,
          status: 'info',
          duration: 3000,
          isClosable: true,
        });
        setIsModalOpen(false);
        setSelectedAction(null);
        dispatch(clearConversationsSelection());
      },
    },
  );

  const handleBulkAction = (action: string) => {
    if (action) {
      setSelectedAction(action as BulkActionType);
      setIsModalOpen(true);
    }
  };

  const handleConfirmAction = () => {
    if (selectedAction === 'finish') {
      closeManyConversationsTicketsMutation.mutate(selectedConversationsIds);
    } else if (selectedAction === 'markAsUnread') {
      markManyConversationsAsUnreadMutation.mutate(selectedConversationsIds);
    }
  };

  const getModalConfig = () => {
    const count = selectedConversationsIds.length;
    const plural = count > 1 ? 's' : '';

    if (selectedAction === 'finish') {
      return {
        title: 'Confirmar finalização',
        message: `Tem certeza que deseja finalizar ${count} conversa${plural} selecionada${plural}?`,
        confirmText: 'Finalizar',
        isLoading: closeManyConversationsTicketsMutation.isLoading,
      };
    } else if (selectedAction === 'markAsUnread') {
      return {
        title: 'Confirmar marcação',
        message: `Tem certeza que deseja marcar ${count} conversa${plural} como não lida${plural}?`,
        confirmText: 'Marcar como não lidas',
        isLoading: markManyConversationsAsUnreadMutation.isLoading,
      };
    }

    return {
      title: 'Confirmar ação',
      message: 'Tem certeza que deseja executar esta ação?',
      confirmText: 'Confirmar',
      isLoading: false,
    };
  };

  const actions: InboxToolbarAction[] = [];

  const modalConfig = getModalConfig();

  return (
    <>
      <InboxToolbar
        statusMessage={statusMessage}
        actions={actions}
        selectedCount={selectedCount}
        totalCount={totalCount}
        customComponent={
          <MassiveActionsSelect
            selectedCount={selectedCount}
            handleBulkAction={handleBulkAction}
          />
        }
      />
      <AlertDialogBase
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setSelectedAction(null);
        }}
        onConfirm={handleConfirmAction}
        title={modalConfig.title}
        buttonConfirmText={modalConfig.confirmText}
        isConfirmLoading={modalConfig.isLoading}
      >
        {modalConfig.message}
      </AlertDialogBase>
    </>
  );
};

export default OpenConversationToolbar;
