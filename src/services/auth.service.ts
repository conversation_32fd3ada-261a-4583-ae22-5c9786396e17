import { AxiosResponse } from 'axios';
import { apiRoutes } from '../constants/api-routes';
import { request } from '../constants/request';
import { MixpanelService } from './mixpanel.service';

interface LoginParams {
  email: string;
  password: string;
}

interface LoginResponse {
  access_token: string;
  actions: {
    next_password_change_date: string | Date;
  };
}

const login = async ({
  email,
  password,
}: LoginParams): Promise<AxiosResponse<LoginResponse>> => {
  return request.post(apiRoutes.login(), { username: email, password });
};

const getAccessToken = () => {
  return localStorage.getItem('access_token');
};

const logout = () => {
  localStorage.removeItem('access_token');
  localStorage.removeItem('user.actions');
  MixpanelService.reset();
  window.dispatchEvent(new Event('storage'));
};

export interface UpdatePasswordDto {
  currentPassword: string;
  newPassword: string;
}

export interface UpdatePasswordWithTokenDto {
  updatePasswordToken: string;
  newPassword: string;
}

const updatePassword = (updatePasswordDto: UpdatePasswordDto) => {
  return request.post(apiRoutes.updatePassword(), updatePasswordDto);
};

const updatePasswordWithToken = (
  updatePasswordWithTokenDto: UpdatePasswordWithTokenDto,
) => {
  return request.post(
    apiRoutes.updatePasswordWithToken(),
    updatePasswordWithTokenDto,
  );
};

export interface RequestUpdatePasswordTokenDto {
  userEmail: string;
}

const requestUpdatePasswordToken = (
  requestUpdatePasswordTokenDto: RequestUpdatePasswordTokenDto,
) => {
  return request.post<{ resetToken: string }>(
    apiRoutes.requestUpdatePasswordToken(),
    requestUpdatePasswordTokenDto,
  );
};

export const AuthService = {
  login,
  getAccessToken,
  logout,
  updatePassword,
  updatePasswordWithToken,
  requestUpdatePasswordToken,
};
