import {
  Badge,
  Flex,
  Table,
  TableContainer,
  Tbody,
  Td,
  Text,
  Th,
  Thead,
  Tooltip,
  Tr,
  Box,
  HStack,
  Icon,
  Checkbox,
  Switch,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  IconButton,
  useDisclosure,
  useToast,
} from '@chakra-ui/react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { useSearchParams } from 'react-router-dom';
import { useState, useCallback } from 'react';
import {
  MdOutlineRemoveRedEye,
  MdWarning,
  MdCheckCircle,
  MdMoreVert,
  MdSync,
  MdDelete,
} from 'react-icons/md';
import { PaginatedResponse } from '../../../../types/PaginatedResponse';
import { colors } from '../../../../constants/colors';
import LoadingScreen from '../../../../components/LoadingScreen';
import Pagination from '../../../../components/Pagination';
import { ProductsService } from '../../../../services/products.service';
import ProductDetailsModal from '../ModalProductDetails';
import { IntegrationUtils } from '../../../../utils/integration.utils';
import { useProductSearchParams } from '../../../../hooks/useProductSearchParams';
import { ProductSql } from '../../../../types/ProductPaginatedSql';
import { MoneyUtils } from '../../../../utils/money.utils';
import { scrollbarStyles } from '../../../../styles/scrollbar.styles';
import ModalDeleteProduct from '../ModalDeleteProduct';
import ProductImage from '../ModalProductDetails/ProductImage';
import { apiRoutes } from '../../../../constants/api-routes';

interface TableProductsPaginatedProps {
  selectedProducts: Set<string>;
  onProductSelection: (productIds: Set<string>) => void;
  onSyncSingleProduct: (productId: string) => void;
}

const useProductDetailsModal = () => {
  const [selectedProductId, setSelectedProductId] = useState<string>('');
  const [productToDelete, setProductToDelete] = useState<string>('');

  const productDetailsModal = useDisclosure();
  const deleteModal = useDisclosure();

  const openProductDetails = useCallback(
    (productId: string) => {
      setSelectedProductId(productId);
      productDetailsModal.onOpen();
    },
    [productDetailsModal],
  );

  const closeProductDetails = useCallback(() => {
    productDetailsModal.onClose();
    setSelectedProductId('');
  }, [productDetailsModal]);

  const openDeleteModal = useCallback(
    (productId: string) => {
      setProductToDelete(productId);
      deleteModal.onOpen();
    },
    [deleteModal],
  );

  const closeDeleteModal = useCallback(() => {
    deleteModal.onClose();
    setProductToDelete('');
  }, [deleteModal]);

  return {
    selectedProductId,
    productToDelete,
    productDetailsModal: {
      ...productDetailsModal,
      open: openProductDetails,
      close: closeProductDetails,
    },
    deleteModal: {
      ...deleteModal,
      open: openDeleteModal,
      close: closeDeleteModal,
    },
  };
};

const usePagination = () => {
  const [searchParams, setSearchParams] = useSearchParams();

  const handlePageChange = useCallback(
    (newPage: number) => {
      const newParams = new URLSearchParams(searchParams.toString());
      newParams.set('page', String(newPage));
      setSearchParams(newParams, { replace: false });

      const tableContainer = document.querySelector('[data-table-container]');
      if (tableContainer) {
        tableContainer.scrollTop = 0;
      }
    },
    [searchParams, setSearchParams],
  );

  const handlePerPageChange = useCallback(
    (newLimit: number) => {
      const newParams = new URLSearchParams(searchParams.toString());
      newParams.set('perPage', String(newLimit));
      newParams.set('page', '1');
      setSearchParams(newParams, { replace: false });
    },
    [searchParams, setSearchParams],
  );

  return {
    handlePageChange,
    handlePerPageChange,
  };
};

const getStatusColorScheme = (status: string): string => {
  const statusData: Record<string, string> = {
    ACTIVE: colors.status.completed || 'green',
    DRAFT: 'orange',
  };
  return statusData[status] ?? 'gray';
};

const getStockStatus = (product: ProductSql) => {
  if (!product.hasStock || product.totalStock === 0) {
    return { color: 'red.500', icon: MdWarning, label: 'Sem estoque' };
  }
  if (product.totalStock <= 5) {
    return { color: 'orange.500', icon: MdWarning, label: 'Estoque baixo' };
  }
  return { color: 'green.500', icon: MdCheckCircle, label: 'Em estoque' };
};

const TableProductsPaginated = ({
  selectedProducts,
  onProductSelection,
  onSyncSingleProduct,
}: TableProductsPaginatedProps) => {
  const modals = useProductDetailsModal();
  const pagination = usePagination();
  const toast = useToast();
  const queryClient = useQueryClient();

  const {
    page,
    perPage,
    searchQuery,
    status,
    source,
    minPrice,
    maxPrice,
    minStock,
    maxStock,
    sortBy,
  } = useProductSearchParams();

  const currentPage = Math.max(1, Number(page) || 1);
  const currentPerPage = Math.max(1, Number(perPage) || 10);

  const selectedIntegrationName = source
    ? IntegrationUtils.getIntegrationDisplayName(source)
    : '';

  const toggleProductMetaSyncMutation = useMutation(
    (productId: string) => ProductsService.toggleProductMetaSync(productId),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(apiRoutes.listProducts());
        toast({
          title: 'Configuração atualizada',
          description: 'Configuração de sincronização alterada com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      },
    },
  );

  const { data: products, isLoading: isLoadingProducts } = useQuery<
    PaginatedResponse<ProductSql>
  >(
    [
      'products',
      source,
      currentPage,
      currentPerPage,
      searchQuery,
      status,
      minPrice,
      maxPrice,
      minStock,
      maxStock,
      sortBy,
    ],
    async () => {
      const { data: paginatedData } = await ProductsService.listProducts({
        source,
        page: currentPage,
        perPage: currentPerPage,
        searchQuery: searchQuery || undefined,
        status: status || undefined,
        minPrice: minPrice,
        maxPrice: maxPrice,
        minStock: minStock,
        maxStock: maxStock,
        sortBy: sortBy || undefined,
      });

      return paginatedData;
    },
    {
      enabled: !!source,
      refetchInterval: 30000,
      refetchOnWindowFocus: true,
      keepPreviousData: true,
    },
  );

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const allProductIds = new Set(products?.data?.map((p) => p.id) || []);
      onProductSelection(allProductIds);
    } else {
      onProductSelection(new Set());
    }
  };

  const handleSelectProduct = (productId: string, checked: boolean) => {
    const newSelected = new Set(selectedProducts);
    if (checked) {
      newSelected.add(productId);
    } else {
      newSelected.delete(productId);
    }
    onProductSelection(newSelected);
  };

  const handleToggleSync = useCallback(
    (productId: string) => {
      toggleProductMetaSyncMutation.mutate(productId);
    },
    [toggleProductMetaSyncMutation],
  );

  const handleSyncSingleProduct = useCallback(
    (productId: string) => {
      onSyncSingleProduct(productId);
    },
    [onSyncSingleProduct],
  );

  const productsLength = products?.data?.length ?? 0;
  const isAllSelected =
    productsLength > 0 && selectedProducts.size === productsLength;
  const isIndeterminate =
    selectedProducts.size > 0 && selectedProducts.size < productsLength;
  const hasNoProducts =
    !isLoadingProducts && products?.data && productsLength === 0;

  if (!products?.data) return null;

  return (
    <>
      <LoadingScreen isLoading={isLoadingProducts}>
        <Box height="100vh" display="flex" flexDirection="column">
          <Flex
            justifyContent="space-between"
            alignItems="center"
            mb={4}
            px={4}
          >
            <Text fontSize="xl" fontWeight="semibold" color="gray.700">
              Produtos - {selectedIntegrationName}
            </Text>

            <Flex gap={4} alignItems="center">
              {products?.meta && (
                <HStack spacing={4}>
                  <Text fontSize="sm" color="gray.600" fontWeight="medium">
                    {products.meta.totalItems} produto(s) encontrado(s)
                  </Text>
                </HStack>
              )}
            </Flex>
          </Flex>

          <Box flex="1" overflow="hidden" px={4}>
            <TableContainer
              data-table-container
              overflowY="auto"
              height="100%"
              borderRadius="md"
              border="1px solid"
              borderColor="gray.200"
              css={scrollbarStyles({ height: '4px' })}
            >
              <Table variant="simple" size="md">
                <Thead
                  position="sticky"
                  top={0}
                  bg="white"
                  boxShadow="sm"
                  zIndex={1}
                >
                  <Tr sx={{ '& th': { px: 4, py: 4 } }}>
                    <Th width="40px">
                      <Checkbox
                        isChecked={isAllSelected}
                        isIndeterminate={isIndeterminate}
                        onChange={(e) => handleSelectAll(e.target.checked)}
                      />
                    </Th>
                    <Th>Imagem</Th>
                    <Th>Nome do Produto</Th>
                    <Th>ID Integração</Th>
                    <Th isNumeric>Preço Médio</Th>
                    <Th isNumeric>Estoque</Th>
                    <Th isNumeric>Variantes</Th>
                    <Th>Status</Th>
                    <Th textAlign="center">Sincronizar Meta</Th>
                    <Th>Última Atualização</Th>
                    <Th textAlign="center">Ações</Th>
                  </Tr>
                </Thead>
                <Tbody>
                  {products.data.map((product: ProductSql) => {
                    const stockStatus = getStockStatus(product);
                    const isSelected = selectedProducts.has(product.id);

                    return (
                      <Tr
                        key={product.id}
                        _hover={{ bg: 'gray.50' }}
                        bg={isSelected ? 'gray.50' : 'transparent'}
                      >
                        <Td borderColor="gray.100">
                          <Checkbox
                            isChecked={isSelected}
                            onChange={(e) =>
                              handleSelectProduct(product.id, e.target.checked)
                            }
                          />
                        </Td>
                        <Td borderColor="gray.100">
                          <ProductImage
                            src={product.imageUrl}
                            alt={product.name}
                            boxSize="50px"
                            showTooltip={true}
                            tooltipSize="250px"
                          />
                        </Td>
                        <Td borderColor="gray.100">
                          <Tooltip label={product.name} placement="top">
                            <Text
                              noOfLines={2}
                              maxW={'250px'}
                              display="block"
                              cursor="pointer"
                              color="black"
                              fontWeight="semibold"
                              _hover={{
                                textDecoration: 'underline',
                                color: 'gray.700',
                              }}
                              onClick={() =>
                                modals.productDetailsModal.open(product.id)
                              }
                              lineHeight="1.3"
                            >
                              {product.name}
                            </Text>
                          </Tooltip>
                        </Td>
                        <Td borderColor="gray.100">
                          <Text
                            noOfLines={1}
                            maxW={'120px'}
                            display="block"
                            fontFamily="mono"
                            fontSize="sm"
                            color="gray.600"
                          >
                            {product.sourceId}
                          </Text>
                        </Td>
                        <Td borderColor="gray.100" isNumeric>
                          <Text
                            fontWeight="semibold"
                            color="green.600"
                            fontSize="sm"
                          >
                            {MoneyUtils.formatCurrency(product.averagePrice)}
                          </Text>
                        </Td>
                        <Td borderColor="gray.100" isNumeric>
                          <HStack justify="flex-end" spacing={1}>
                            <Icon
                              as={stockStatus.icon}
                              color={stockStatus.color}
                              boxSize={4}
                            />
                            <Text
                              color={stockStatus.color}
                              fontWeight="medium"
                              fontSize="sm"
                            >
                              {product.totalStock}
                            </Text>
                          </HStack>
                        </Td>
                        <Td borderColor="gray.100" isNumeric>
                          <Text
                            color={
                              product.numberOfVariants <= 1
                                ? 'orange.500'
                                : 'gray.700'
                            }
                            fontWeight={
                              product.numberOfVariants <= 1 ? 'bold' : 'normal'
                            }
                            fontSize="sm"
                          >
                            {product.numberOfVariants}
                          </Text>
                        </Td>
                        <Td borderColor="gray.100">
                          <HStack spacing={2}>
                            <Badge
                              colorScheme={getStatusColorScheme(product.status)}
                              variant="subtle"
                              borderRadius="full"
                              px={2}
                              py={1}
                              fontSize="xs"
                              fontWeight="medium"
                            >
                              {product.status}
                            </Badge>
                          </HStack>
                        </Td>
                        <Td borderColor="gray.100" textAlign="center">
                          <Flex justify="center">
                            <Switch
                              isChecked={product.isMetaSyncEnabled}
                              onChange={(e) => handleToggleSync(product.id)}
                              colorScheme="black"
                              size="md"
                            />
                          </Flex>
                        </Td>
                        <Td borderColor="gray.100">
                          <Text fontSize="xs" color="gray.500">
                            {format(
                              new Date(product.updatedAt),
                              "dd/MM/yyyy 'às' HH:mm",
                              { locale: ptBR },
                            )}
                          </Text>
                        </Td>
                        <Td borderColor="gray.100" textAlign="center">
                          <Menu>
                            <MenuButton
                              as={IconButton}
                              aria-label="Mais ações"
                              icon={<MdMoreVert />}
                              variant="ghost"
                              size="sm"
                              color={colors.darkGrey}
                            />
                            <MenuList zIndex={9999}>
                              <MenuItem
                                icon={<MdOutlineRemoveRedEye />}
                                onClick={() =>
                                  modals.productDetailsModal.open(product.id)
                                }
                              >
                                Visualizar Produto
                              </MenuItem>
                              <MenuItem
                                icon={<MdSync />}
                                onClick={() =>
                                  handleSyncSingleProduct(product.id)
                                }
                                isDisabled={!product.isMetaSyncEnabled}
                              >
                                Sincronizar com Meta
                              </MenuItem>
                              <MenuItem
                                icon={<MdDelete />}
                                onClick={() =>
                                  modals.deleteModal.open(product.id)
                                }
                                isDisabled={!product.isMetaSyncEnabled}
                                color="red.500"
                                _hover={{ bg: 'red.50' }}
                              >
                                Remover da Meta
                              </MenuItem>
                            </MenuList>
                          </Menu>
                        </Td>
                      </Tr>
                    );
                  })}
                </Tbody>
              </Table>
            </TableContainer>

            {hasNoProducts && (
              <Flex
                justifyContent="center"
                alignItems="center"
                py={12}
                flexDirection="column"
                gap={2}
              >
                <Text color="gray.500" fontSize="lg" fontWeight="medium">
                  Nenhum produto encontrado
                </Text>
                <Text color="gray.400" fontSize="sm">
                  Tente ajustar os filtros de busca para encontrar produtos.
                </Text>
              </Flex>
            )}
          </Box>

          {products?.data && products.data.length > 0 && (
            <Box
              mt={4}
              px={4}
              py={3}
              bg="white"
              borderTop="1px solid"
              borderColor="gray.200"
              borderRadius="0 0 md md"
            >
              <Pagination
                initialPage={currentPage}
                rowsPerPage={currentPerPage}
                onChangePage={pagination.handlePageChange}
                onChangeRowsPerPage={pagination.handlePerPageChange}
                totalRows={products?.meta?.totalItems || 0}
                itemsLabel="produtos"
              />
            </Box>
          )}
        </Box>
      </LoadingScreen>

      {modals.productDetailsModal.isOpen && (
        <ProductDetailsModal
          isOpen={modals.productDetailsModal.isOpen}
          onClose={modals.productDetailsModal.close}
          productId={modals.selectedProductId}
        />
      )}

      {modals.deleteModal.isOpen && (
        <ModalDeleteProduct
          isOpen={modals.deleteModal.isOpen}
          onClose={modals.deleteModal.close}
          productId={modals.productToDelete}
        />
      )}
    </>
  );
};

export default TableProductsPaginated;
