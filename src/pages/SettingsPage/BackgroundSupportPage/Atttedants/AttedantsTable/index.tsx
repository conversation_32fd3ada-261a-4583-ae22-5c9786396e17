import {
  Avatar,
  Table,
  <PERSON><PERSON>ontainer,
  <PERSON>body,
  Td,
  Thead,
  Tr,
  Switch,
  Flex,
} from '@chakra-ui/react';
import { useQuery } from 'react-query';
import { apiRoutes } from '../../../../../constants/api-routes';
import { colors } from '../../../../../constants/colors';
import { TextTd, Th } from '../../../../../components/CustomTable';
import { UsersService } from '../../../../../services/users.service';
import { User } from '../../../../../types/Prisma';
import { scrollbarStyles } from '../../../../../styles/scrollbar.styles';
import { useUserToggle } from '../../../../../hooks/useUserToggle';

const TableAttedants = () => {
  const queryKey = apiRoutes.listCompanyAgents({
    skipInactiveUsers: false,
    skipAiAgents: false,
  });

  const { data: companyAgents = [] } = useQuery(queryKey, async () => {
    const { data } = await UsersService.listCompanyAgents({
      skipInactiveUsers: false,
      skipAiAgents: false,
    });
    return data;
  });

  const { toggleUserStatus, toggleCanViewAllConversations } =
    useUserToggle(queryKey);

  return (
    <TableContainer
      borderRadius="md"
      border="1px solid"
      borderColor={colors.border}
      maxHeight="300px"
      overflowY="auto"
      css={scrollbarStyles({ width: '4px' })}
    >
      <Table variant="simple" style={{ tableLayout: 'fixed' }}>
        <Thead>
          <Tr display="flex" width="100%" bgColor={colors.slateSuperLight}>
            <Th width="25%">NOME</Th>
            <Th width="25%">EMAIL</Th>
            <Th width="10%" display="flex" justifyContent="center">
              USUÁRIO ATIVO
            </Th>
            <Th width="25%" display="flex" justifyContent="center">
              PODE VER TODAS AS CONVERSAS
            </Th>
            <Th width="15%" display="flex" justifyContent="center">
              ATENDENTE PADRÃO
            </Th>
          </Tr>
        </Thead>
        <Tbody>
          {companyAgents?.map((agent: User) => (
            <Tr display="flex" width="100%" key={agent.id} bg={colors.white}>
              <TextTd width="25%" textForTooltip={agent.name}>
                <Flex align="center" gap="2">
                  <Avatar name={agent.name} size="sm" />
                  {agent.name}
                </Flex>
              </TextTd>
              <TextTd width="25%" textForTooltip={agent.email}>
                {agent.email}
              </TextTd>
              <Td
                width="10%"
                display="flex"
                justifyContent="center"
                alignItems="center"
              >
                <Switch
                  isChecked={agent.isActive}
                  colorScheme="black"
                  isDisabled={toggleUserStatus.isLoading}
                  onChange={() => toggleUserStatus.mutate(agent.id)}
                />
              </Td>
              <Td
                width="25%"
                display="flex"
                justifyContent="center"
                alignItems="center"
              >
                <Switch
                  isChecked={agent.canViewAllConversations}
                  colorScheme="black"
                  isDisabled={toggleCanViewAllConversations.isLoading}
                  onChange={() =>
                    toggleCanViewAllConversations.mutate(agent.id)
                  }
                />
              </Td>
              <Td
                width="15%"
                display="flex"
                justifyContent="center"
                alignItems="center"
              >
                <Switch
                  isChecked={agent.canViewAllConversations}
                  colorScheme="black"
                  isDisabled={toggleCanViewAllConversations.isLoading}
                  onChange={() =>
                    toggleCanViewAllConversations.mutate(agent.id)
                  }
                />
              </Td>
            </Tr>
          ))}
        </Tbody>
      </Table>
    </TableContainer>
  );
};

export default TableAttedants;
