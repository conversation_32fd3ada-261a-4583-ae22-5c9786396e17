import { Box, useToast } from '@chakra-ui/react';
import { useMemo, useState } from 'react';
import { useMutation, useQuery } from 'react-query';
import { useNavigate, useParams } from 'react-router-dom';
import { appPaths } from '../../../../constants/app-paths';
import {
  AutomationsService,
  CreateAutomationFromIntegrationDto,
} from '../../../../services/automations.service';
import AutomationByTypeForm from '../components/AutomationByTypeForm';
import { apiRoutes } from '../../../../constants/api-routes';
import GlassmorphisContainer from '../../../../components/GlassmorphismContainer';
import { IntegrationsService } from '../../../../services/integrations.service';
import IntegrationActiveKeyBySourceIntegration from '../constants/integration-active-key-by-source-integration';
import TopIntegrationInactiveErrorMessage from '../components/TopIntegrationInactiveErrorMessage';
import LoadingScreen from '../../../../components/LoadingScreen';
import { IntegrationUtils } from '../../../../utils/integration.utils';

const CreateBackgroundAutomationByTypePage = () => {
  const { automationTypeId } = useParams();
  const navigate = useNavigate();
  const toast = useToast();

  const { data: integrationStatus, isFetching: isFetchingIntegrationStatus } =
    useQuery(apiRoutes.getIntegrationStatusSummary(), async () => {
      const { data } = await IntegrationsService.getIntegrationStatusSummary();
      return data;
    });

  const { data: automationType, isFetching: isFetchingAutomationType } =
    useQuery(apiRoutes.showAutomationType(automationTypeId!), async () => {
      const { data } = await AutomationsService.showAutomationType(
        automationTypeId!,
      );
      return data;
    });

  const isIntegrationActive = useMemo(() => {
    if (!integrationStatus || !automationType) return false;
    const integrationActiveKey =
      IntegrationActiveKeyBySourceIntegration[
        automationType.sourceIntegration!
      ];
    return (integrationStatus as Record<string, boolean>)[integrationActiveKey];
  }, [integrationStatus, automationType]);

  const createAutomation = useMutation(
    (createAutomationDto: CreateAutomationFromIntegrationDto) =>
      AutomationsService.createAutomationFromIntegration(createAutomationDto),
    {
      onSuccess: () => {
        toast({
          title: 'Automação criada com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
        navigate(appPaths.automations.backgroundAutomations.index());
      },
    },
  );

  const [whatsappTemplateArgs, setWhatsappTemplateArgs] = useState<{
    [key: string]: string | undefined;
  }>({});

  const [emailTemplateArgs, setEmailTemplateArgs] = useState<{
    [key: string]: string | undefined;
  }>({});

  async function onSubmit(data: any) {
    const {
      name,
      isActive,
      messageTemplateId,
      emailTemplateId,
      dailyMessageLimitOnWhatsapp,
      dailyEmailLimit,
      action,
      flowId,
      data: automationData,
    } = data;
    await createAutomation.mutateAsync({
      name,
      isActive,
      messageTemplateId,
      emailTemplateId,
      data: automationData,
      templateArgs: whatsappTemplateArgs,
      emailTemplateArgs: emailTemplateArgs,
      dailyMessageLimitOnWhatsapp,
      dailyEmailLimit,
      automationTypeId: automationTypeId!,
      isAutomationRepetitionAllowed: true,
      action,
      flowId,
    });
  }

  return (
    <>
      {automationType &&
      IntegrationUtils.requiresManualConfiguration(
        automationType?.sourceIntegration!,
      ) ? (
        <TopIntegrationInactiveErrorMessage
          sourceIntegration={automationType?.sourceIntegration!}
          requiresManualConfiguration={true}
        />
      ) : (
        automationType &&
        !isIntegrationActive && (
          <TopIntegrationInactiveErrorMessage
            sourceIntegration={automationType?.sourceIntegration!}
          />
        )
      )}
      <GlassmorphisContainer show={!isIntegrationActive}>
        <LoadingScreen
          isLoading={isFetchingAutomationType || isFetchingIntegrationStatus}
        >
          <AutomationByTypeForm
            onSubmit={onSubmit}
            whatsappTemplateArgs={whatsappTemplateArgs}
            setWhatsappTemplateArgs={setWhatsappTemplateArgs}
            emailTemplateArgs={emailTemplateArgs}
            setEmailTemplateArgs={setEmailTemplateArgs}
            automationType={automationType}
          />
        </LoadingScreen>
      </GlassmorphisContainer>
    </>
  );
};

export default CreateBackgroundAutomationByTypePage;
