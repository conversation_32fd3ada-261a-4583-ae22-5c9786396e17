import { AxiosResponse } from 'axios';
import { apiRoutes } from '../constants/api-routes';
import { request } from '../constants/request';
import { Product } from '../types/Product';
import { ListFieldValuesResponse } from '../types/FieldValuesResponse';
import { PaginatedResponse } from '../types/PaginatedResponse';
import { SourceIntegration } from '../types/Prisma';
import { ProductSql } from '../types/ProductPaginatedSql';

export type ListProductsParams = {
  page?: number;
  perPage?: number;
  searchQuery?: string;
  status?: string;
  source?: string;
  integrationConfigId?: string;
  minPrice?: number;
  maxPrice?: number;
  minStock?: number;
  maxStock?: number;
  sortBy?: string;
  sortOrder?: string;
};

const listProducts = async (
  params?: ListProductsParams,
): Promise<AxiosResponse<PaginatedResponse<ProductSql>>> => {
  return request.get(apiRoutes.listProducts(params));
};

const listCatalogProducts = async (
  params?: ListProductsParams,
): Promise<AxiosResponse<PaginatedResponse<any>>> => {
  return request.get(apiRoutes.listCatalogProducts(params));
};

const listProductDetails = async (
  productId: string,
): Promise<AxiosResponse<any>> => {
  return request.get(apiRoutes.listProductDetails(productId));
};

const syncProducts = async (
  source: SourceIntegration,
): Promise<AxiosResponse<Product[]>> => {
  return request.post(apiRoutes.syncProducts(source), { source });
};

const syncProductsWithMeta = async (
  source: SourceIntegration,
  productIds?: string[],
): Promise<any> => {
  const { data } = await request.post(
    apiRoutes.syncProductsWithMeta(source),
    productIds ? { productIds } : {},
  );
  return data;
};

const toggleProductMetaSync = async (
  productId: string,
): Promise<AxiosResponse<Product>> => {
  return request.patch(apiRoutes.toggleProductMetaSync(productId));
};

const listProductVariantsFieldValues = async (
  field: string,
  source?: string,
): Promise<AxiosResponse<ListFieldValuesResponse>> => {
  return request.get(apiRoutes.listProductVariantsFieldValues(field, source));
};

const deleteProductFromMeta = async (productId: string) => {
  return request.delete(apiRoutes.deleteProductFromMeta(productId));
};

export const ProductsService = {
  listProducts,
  listCatalogProducts,
  listProductDetails,
  syncProducts,
  syncProductsWithMeta,
  toggleProductMetaSync,
  listProductVariantsFieldValues,
  deleteProductFromMeta,
};
