import MessageTemplateCard, {
  MessageTemplateCardSimplified,
} from './MessageTemplateCard';
import Prisma, { MessageTemplateButton } from './Prisma';

type MessageStatus = Prisma.MessageStatus;
export type MediaType = Prisma.MediaType;

export interface Message extends Prisma.Message {
  createdAt: string;
  updatedAt?: string;
  uploadProgress?: number;
}

export interface MessageWithIncludes extends Message {
  conversation: {
    id: string;
    company: {
      isAutomaticSortingActive: boolean;
    };
  };
}

export interface MessageCard {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  text: string;
  cardIndex: number;
  messageId: string;
  messageTemplateCardId: string;
  messageTemplateCard: MessageTemplateCardSimplified;
}

export interface MessageProductCardItems {
  id: string;
  cardId: string;
  messageProductCardId: string;
  name: string;
  priceCents: number;
  section?: string | null;
  createdAt: string;
  updatedAt: string;
  productVariant: ProductVariant;
}

export interface MessageProductCard {
  id: string;
  type: string;
  createdAt: string;
  updatedAt: string;
  bodyText: string;
  footerText?: string;
  headerText?: string;
  messageProductCardItems: MessageProductCardItems[];
}

export interface WhatsappOrder {
  id: string;
  companyId: string;
  totalCents: number;
  catalogId: string;
  createdAt: string;
  updatedAt: string;
  whatsappOrderItems: WhatsappOrderItem[];
}

export interface WhatsappOrderItem {
  id: string;
  whatsappOrderId: string;
  productVariantId: string;
  priceCents: number;
  quantity: number;
  currency: string;
  createdAt: string;
  updatedAt: string;
  productVariant: ProductVariant;
}

export interface MessageWithCardsIncludes extends Message {
  messageCards?: MessageCard[];
  messageProductCards?: MessageProductCard[];
  MessageCatalogOrder?: MessageCatalogOrder[];
  context?: ListConversationDetailedItem;
  internal?: boolean;
}
