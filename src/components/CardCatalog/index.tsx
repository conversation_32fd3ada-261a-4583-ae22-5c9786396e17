import { Box, Flex, Text, Image, VStack } from '@chakra-ui/react';
import ButtonWhatsapp from '../ButtonWhatsapp';

interface CatalogCardProps {
  catalogImage?: string;
  catalogTitle?: string;
  catalogDescription?: string;
  headerText?: string;
  bodyText?: string;
  footerText?: string;
}

const CatalogCard = ({
  catalogImage,
  catalogTitle = 'Veja nosso catálogo',
  catalogDescription,
  headerText,
  bodyText,
  footerText,
}: CatalogCardProps) => {
  return (
    <Box
      bg="white"
      borderRadius="8px"
      overflow="hidden"
      maxWidth="100%"
      boxShadow="0 1px 3px rgba(0,0,0,0.12)"
      cursor="pointer"
      _hover={{ boxShadow: '0 2px 8px rgba(0,0,0,0.15)' }}
      transition="all 0.2s"
      p={3}
    >
      <Flex alignItems="center" mb={2}>
        {catalogImage && (
          <Image
            src={catalogImage}
            alt="Catálogo"
            width="60px"
            height="60px"
            objectFit="cover"
            flexShrink={0}
            borderRadius="6px"
            mr={3}
          />
        )}
        <Box>
          <Text fontSize="14px" fontWeight="bold" color="#1c1c1c">
            {catalogTitle}
          </Text>
          {catalogDescription && (
            <Text fontSize="12px" color="gray.600">
              {catalogDescription}
            </Text>
          )}
        </Box>
      </Flex>

      <VStack align="start" spacing={1} mb={2}>
        {headerText && (
          <Text fontSize="13px" fontWeight="semibold" color="gray.700">
            {headerText}
          </Text>
        )}
        {bodyText && (
          <Text fontSize="13px" color="gray.800">
            {bodyText}
          </Text>
        )}
        {footerText && (
          <Text fontSize="12px" color="gray.500">
            {footerText}
          </Text>
        )}
      </VStack>

      <ButtonWhatsapp variant="secondary">Mostra catálogo</ButtonWhatsapp>
    </Box>
  );
};

export default CatalogCard;
