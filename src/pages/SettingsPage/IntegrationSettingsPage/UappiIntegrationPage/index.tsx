import {
  Box,
  Button,
  Checkbox,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  Input,
  Stack,
  Text,
  useToast,
} from '@chakra-ui/react';
import { yupResolver } from '@hookform/resolvers/yup';
import { useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useMutation, useQuery } from 'react-query';
import * as yup from 'yup';
import { colors } from '../../../../constants/colors';
import { apiRoutes } from '../../../../constants/api-routes';
import {
  IntegrationsService,
  SaveUappiCredentialsDto,
  SaveIntegrationsConfigDto,
  IntegrationsConfigDto,
} from '../../../../services/integrations.service';
import { SourceIntegration } from '../../../../types/SourceIntegration';
import IntegrationsTable, {
  IntegrationsTableColumn,
} from '../../../../components/IntegrationsTable';

const createSchema = (isEditing: boolean) =>
  yup.object({
    description: yup.string().default('').required('A descrição é obrigatória'),
    uappiPass: isEditing
      ? yup.string().default('')
      : yup.string().default('').required("'Uappi Pass' é obrigatório"),
    uappiToken: isEditing
      ? yup.string().default('')
      : yup.string().default('').required("'Uappi Token' é obrigatória"),
    isOrderActive: yup.boolean().default(false),
    isAbandonedCartActive: yup.boolean().default(false),
    isActive: yup.boolean().default(true),
  });

const UappiIntegrationPage = () => {
  const toast = useToast();
  const [showForm, setShowForm] = useState(false);
  const [editingIntegrationId, setEditingIntegrationId] = useState<
    string | null
  >(null);

  // Configuração das colunas da tabela - para Uappi, mostramos pedidos e carrinhos abandonados
  const tableColumns: IntegrationsTableColumn[] = [
    { key: 'isOrderActive', label: 'Sinc. de Pedidos', show: true },
    {
      key: 'isAbandonedCartActive',
      label: 'Sinc. de Carrinhos Abandonados',
      show: true,
    },
  ];

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    getValues,
    setValue,
    reset,
  } = useForm({
    resolver: yupResolver(createSchema(!!editingIntegrationId)),
  });

  const { data: integrationsConfig, refetch: refetchIntegrationsConfig } =
    useQuery(
      apiRoutes.getIntegrationsConfig(SourceIntegration.uappi),
      async () => {
        const { data } = await IntegrationsService.getIntegrationsConfig(
          SourceIntegration.uappi,
        );
        return data;
      },
    );

  const saveUappiCredentials = useMutation(
    (data: SaveIntegrationsConfigDto<SaveUappiCredentialsDto>) =>
      IntegrationsService.saveIntegrationConfig(data),
    {
      onSuccess: () => {
        refetchIntegrationsConfig();
        toast({
          title: 'Configurações salvas com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      },
    },
  );

  async function handleSaveData() {
    const config: SaveUappiCredentialsDto = {
      uappiPass: getValues('uappiPass') || '',
      uappiToken: getValues('uappiToken') || '',
    };

    const data = {
      id: editingIntegrationId || undefined,
      source: SourceIntegration.uappi,
      description: getValues('description'),
      config,
      isActive: !!getValues('isActive'),
      isAbandonedCartActive: !!getValues('isAbandonedCartActive'),
      isOrderActive: !!getValues('isOrderActive'),
      isProductActive: false,
      companyId: '',
    };

    await saveUappiCredentials.mutateAsync(data);

    reset();
    setShowForm(false);
    setEditingIntegrationId(null);
  }

  async function onSubmit() {
    handleSaveData();
  }

  const handleEditIntegration = (integration: IntegrationsConfigDto) => {
    setShowForm(true);
    setValue('description', integration.description);
    setValue('isOrderActive', integration.isOrderActive);
    setValue('isAbandonedCartActive', integration.isAbandonedCartActive);
    setValue('isActive', integration.isActive);

    const config = integration.config as SaveUappiCredentialsDto;
    if (config) {
      setValue('uappiToken', config.uappiToken || '');
      setValue('uappiPass', config.uappiPass || '');
    }
    setEditingIntegrationId(integration.id);
  };

  return (
    <Box>
      <Heading size="md" mb={4}>
        Integração Uappi
      </Heading>
      {!showForm ? (
        <Flex justifyContent="flex-end" mb={4}>
          <Button
            colorScheme="green"
            onClick={() => setShowForm(true)}
            isLoading={saveUappiCredentials.isLoading}
          >
            Adicionar Nova Integração
          </Button>
        </Flex>
      ) : (
        <Box mb={6}>
          <Heading size="sm" mb={4}>
            {editingIntegrationId
              ? 'Editar Integração'
              : 'Cadastrar Nova Integração'}
          </Heading>
          <form onSubmit={handleSubmit(onSubmit)}>
            <Stack spacing={4} mt={4}>
              <FormControl isRequired>
                <FormLabel>Descrição</FormLabel>
                <Input
                  placeholder="Descrição da integração"
                  {...register('description')}
                  isInvalid={!!errors.description?.message}
                />
                <Text color={colors.danger} fontSize="xs">
                  {errors.description?.message}
                </Text>
              </FormControl>

              <FormControl isRequired={!editingIntegrationId}>
                <FormLabel>API Token</FormLabel>
                <Input
                  type="password"
                  placeholder={'************'}
                  sx={{
                    '&::placeholder': {
                      color: 'black',
                    },
                  }}
                  {...register('uappiToken')}
                  isInvalid={!!errors.uappiToken?.message}
                />
                <Text color={colors.danger} fontSize="xs">
                  {errors.uappiToken?.message}
                </Text>
              </FormControl>

              <FormControl isRequired={!editingIntegrationId}>
                <FormLabel>API Pass</FormLabel>
                <Input
                  type="password"
                  placeholder={'************'}
                  sx={{
                    '&::placeholder': {
                      color: 'black',
                    },
                  }}
                  {...register('uappiPass')}
                  isInvalid={!!errors.uappiPass?.message}
                />
                <Text color={colors.danger} fontSize="xs">
                  {errors.uappiPass?.message}
                </Text>
              </FormControl>

              <FormControl>
                <Controller
                  name="isOrderActive"
                  control={control}
                  render={({ field }) => (
                    <Checkbox
                      isChecked={field.value}
                      onChange={(e) => field.onChange(e.target.checked)}
                    >
                      Sincronização de Pedidos Ativa
                    </Checkbox>
                  )}
                />
              </FormControl>

              <FormControl>
                <Controller
                  name="isAbandonedCartActive"
                  control={control}
                  render={({ field }) => (
                    <Checkbox
                      isChecked={field.value}
                      onChange={(e) => field.onChange(e.target.checked)}
                    >
                      Sincronização de Carrinhos Abandonados Ativa
                    </Checkbox>
                  )}
                />
              </FormControl>

              <FormControl>
                <Controller
                  name="isActive"
                  control={control}
                  render={({ field }) => (
                    <Checkbox
                      isChecked={field.value}
                      onChange={(e) => field.onChange(e.target.checked)}
                    >
                      Integração Ativa
                    </Checkbox>
                  )}
                />
              </FormControl>

              <Flex justify="flex-end" gap={2}>
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowForm(false);
                    setEditingIntegrationId(null);
                    reset();
                  }}
                >
                  Cancelar
                </Button>
                <Button
                  type="submit"
                  colorScheme="blue"
                  isLoading={saveUappiCredentials.isLoading}
                >
                  Salvar
                </Button>
              </Flex>
            </Stack>
          </form>
        </Box>
      )}

      <IntegrationsTable
        integrationsConfig={integrationsConfig}
        sourceIntegration={SourceIntegration.uappi}
        columns={tableColumns}
        onEdit={handleEditIntegration}
        isLoading={saveUappiCredentials.isLoading}
      />
    </Box>
  );
};

export default UappiIntegrationPage;
