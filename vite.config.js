import { defineConfig } from 'vite';
import ViteSWC from '@vitejs/plugin-react-swc';
import { VitePWA } from 'vite-plugin-pwa';
import path from 'path'

// TODO: Change to package semver version
const BUILD_VERSION = Date.now() 
const MAX_FILE_CACHE_IN_BYTES = 1024 * 1024 * 5 // 5 MB

export default defineConfig({
  plugins: [
    ViteSWC(),
    VitePWA({
      strategies: 'injectManifest',
      srcDir: 'src',
      filename: 'service-worker.ts',
      registerType: 'autoUpdate',
      includeAssets: ['favicon.ico','logo192.png','logo512.png','apple-touch-icon.png'],
      injectManifest: {
        maximumFileSizeToCacheInBytes: MAX_FILE_CACHE_IN_BYTES,
      },
      manifestFilename: 'manifest.json',
      manifest: {
        short_name: '<PERSON><PERSON>',
        name: '<PERSON><PERSON>',
        icons: [
          { src: 'favicon.ico', sizes: '64x64 32x32 24x24 16x16', type: 'image/x-icon' },
          { src: "logo192.png", sizes: "192x192",type: "image/png", purpose: "maskable" },
          { src: "logo512.png", sizes: "512x512",type: "image/png",purpose: "maskable" },
          { src: 'apple-touch-icon.png', sizes: '180x180', type: 'image/png' }
        ],
        start_url: '/',
        scope: '/',
        display: 'standalone',
        orientation: 'portrait',
        theme_color: '#000000',
        background_color: '#ffffff',
        categories: ['business','productivity'],
        lang: 'pt-BR',
        dir: 'ltr'
      },
      workbox: { 
        globPatterns: ['**/*.{js,css,html,png,ico,json}'],
        cleanupOutdatedCaches: true,   
        skipWaiting: true,             
        clientsClaim: true,           
        runtimeCaching: [
          {
            urlPattern: ({ url }) =>
              !/(\.|^)api\./i.test(url.hostname) && // ignora api.* (ex: api.userevi.com, staging.api.userevi.com)
              !/^ws/.test(url.protocol),            // ignora websocket (ws://, wss://)       
            handler: 'CacheFirst',
            options: {
              cacheName: 'revi-runtime',
              expiration: {
                maxEntries: 100,
                maxAgeSeconds: 60 * 60 * 24 * 3 // 3 dias
              }
            }
          }
        ]
       },
      devOptions: { 
        enabled: false, 
        type: 'module'
      }
    })
  ],
  resolve: {
    alias: {
      react: path.resolve('./node_modules/react'),
      'react-dom': path.resolve('./node_modules/react-dom')
    }
  },
  build: {
    target: 'esnext',
    minify: 'esbuild',
    outDir: 'build', 
    sourcemap: false,
    brotliSize: true,
    chunkSizeWarningLimit: MAX_FILE_CACHE_IN_BYTES,
    maximumFileSizeToCacheInBytes: MAX_FILE_CACHE_IN_BYTES,
    rollupOptions: {
      output: {
        manualChunks(id) {
          if (id.includes('node_modules') && !id.includes('react') && !id.includes('react-dom')) {
            return 'vendor'
          }
        },
        chunkFileNames: `assets/v${BUILD_VERSION}/js/[name]-[hash].js`,
        entryFileNames: `assets/v${BUILD_VERSION}/js/[name]-[hash].js`,
        assetFileNames: `assets/v${BUILD_VERSION}/[ext]/[name]-[hash].[ext]`
      }
    }
  },
  optimizeDeps: { 
    esbuildOptions: { 
      target: 'esnext'
    }
  },
  esbuild: { 
   minify: true, 
   treeShaking: true 
  }
})
