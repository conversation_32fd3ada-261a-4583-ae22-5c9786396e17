import React from 'react';
import {
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Accordion,
  Box,
  Text,
  Badge,
  Grid,
  VStack,
  HStack,
  Tooltip,
  Button,
} from '@chakra-ui/react';
import {
  FiInfo,
  FiDollarSign,
  FiBarChart2,
  FiTag,
  FiSettings,
  FiShoppingBag,
} from 'react-icons/fi';
import NAValue from '../FallbackValue';
import ProductImage from '../ProductImage';
import { MoneyUtils } from '../../../../../utils/money.utils';
import InfoItem from '../InfoItem';
import { ProductVariant } from '../../../../../types/Product';

export interface VariantItemProps {
  variant: ProductVariant;
  index: number;
  onExternalLink: (url: string, e: React.MouseEvent) => void;
  formatDate: (dateString: string) => string | null;
  getStatusColorScheme: (status: string) => string;
  theme: {
    borderColor: string;
    cardBg: string;
    sectionBg: string;
    textColor: string;
    mutedColor: string;
  };
}

const VariantItem = React.memo(
  ({
    variant,
    index,
    onExternalLink,
    formatDate,
    getStatusColorScheme,
    theme,
  }: VariantItemProps) => {
    const { borderColor, cardBg, sectionBg, textColor, mutedColor } = theme;

    const renderBasicInfo = () => (
      <AccordionItem
        border="1px"
        borderColor={borderColor}
        borderRadius="md"
        mb={3}
      >
        <AccordionButton p={3} _hover={{ bg: cardBg }}>
          <HStack spacing={3} flex={1} textAlign="left">
            <FiInfo size="16px" color="var(--chakra-colors-blue-500)" />
            <Text fontSize="sm" fontWeight="600" color={textColor}>
              Informações Básicas
            </Text>
          </HStack>
          <AccordionIcon />
        </AccordionButton>
        <AccordionPanel p={4}>
          <Grid templateColumns="repeat(auto-fit, minmax(200px, 1fr))" gap={4}>
            <InfoItem
              label="Nome"
              value={
                variant.name || (
                  <NAValue tooltipText="Nome da variante não informado" />
                )
              }
              theme={theme}
            />
            <InfoItem
              label="Título"
              value={
                variant.title || (
                  <NAValue tooltipText="Título da variante não informado" />
                )
              }
              theme={theme}
            />
            <InfoItem
              label="SKU"
              value={
                variant.sku || <NAValue tooltipText="SKU não cadastrado" />
              }
              isCode={!!variant.sku}
              theme={theme}
            />
            <InfoItem
              label="Código da Variante"
              value={
                variant.sourceId || (
                  <NAValue tooltipText="Código de origem não disponível" />
                )
              }
              isCode={!!variant.sourceId}
              theme={theme}
            />
            <InfoItem
              label="Código de Barras"
              value={
                variant.barcode || (
                  <NAValue tooltipText="Código de barras não cadastrado" />
                )
              }
              isCode={!!variant.barcode}
              theme={theme}
            />
            <Box>
              <Text fontSize="sm" fontWeight="600" color={mutedColor} mb={2}>
                Status
              </Text>
              <Badge
                fontSize="xs"
                colorScheme={getStatusColorScheme(variant.status)}
                px={2}
                py={1}
                borderRadius="md"
              >
                {variant.status || 'draft'}
              </Badge>
            </Box>
          </Grid>
          {variant.imageUrl && (
            <Box mt={4}>
              <Text fontSize="sm" fontWeight="600" color={mutedColor} mb={2}>
                Imagem do Produto
              </Text>
              <ProductImage
                src={variant.imageUrl}
                alt={variant.name}
                boxSize="120px"
              />
            </Box>
          )}
        </AccordionPanel>
      </AccordionItem>
    );

    const renderPricesAndStock = () => (
      <AccordionItem
        border="1px"
        borderColor={borderColor}
        borderRadius="md"
        mb={3}
      >
        <AccordionButton p={3} _hover={{ bg: cardBg }}>
          <HStack spacing={3} flex={1} textAlign="left">
            <FiDollarSign size="16px" color="var(--chakra-colors-green-500)" />
            <Text fontSize="sm" fontWeight="600" color={textColor}>
              Preços e Estoque
            </Text>
          </HStack>
          <AccordionIcon />
        </AccordionButton>
        <AccordionPanel p={4}>
          <Grid templateColumns="repeat(auto-fit, minmax(200px, 1fr))" gap={4}>
            <Box>
              <Text fontSize="sm" fontWeight="600" color={mutedColor} mb={2}>
                Preço Principal
              </Text>
              <Text fontSize="lg" fontWeight="700" color="green.500">
                {MoneyUtils.formatCurrency(variant.price)}
              </Text>
            </Box>
            <InfoItem
              label="Preço de Venda"
              value={
                variant.salePrice ? (
                  MoneyUtils.formatCurrency(variant.salePrice)
                ) : (
                  <NAValue tooltipText="Preço de venda não informado" />
                )
              }
              theme={theme}
            />
            <InfoItem
              label="Preço de Custo"
              value={
                variant.costPrice ? (
                  MoneyUtils.formatCurrency(variant.costPrice)
                ) : (
                  <NAValue tooltipText="Preço de custo não informado" />
                )
              }
              theme={theme}
            />
            <Box>
              <Text fontSize="sm" fontWeight="600" color={mutedColor} mb={2}>
                Estoque Disponível
              </Text>
              <HStack>
                <Text
                  fontSize="sm"
                  color={variant.stockQuantity <= 5 ? 'red.500' : textColor}
                  fontWeight={variant.stockQuantity <= 5 ? '700' : '500'}
                >
                  {variant.stockQuantity || 0} unidades
                </Text>
                {variant.stockQuantity <= 5 && (
                  <Badge colorScheme="red" fontSize="xs">
                    Estoque Baixo
                  </Badge>
                )}
              </HStack>
            </Box>
          </Grid>
        </AccordionPanel>
      </AccordionItem>
    );

    const renderSpecifications = () => (
      <AccordionItem
        border="1px"
        borderColor={borderColor}
        borderRadius="md"
        mb={3}
      >
        <AccordionButton p={3} _hover={{ bg: cardBg }}>
          <HStack spacing={3} flex={1} textAlign="left">
            <FiBarChart2 size="16px" color="var(--chakra-colors-orange-500)" />
            <Text fontSize="sm" fontWeight="600" color={textColor}>
              Especificações
            </Text>
          </HStack>
          <AccordionIcon />
        </AccordionButton>
        <AccordionPanel p={4}>
          <Grid templateColumns="repeat(auto-fit, minmax(200px, 1fr))" gap={4}>
            <InfoItem
              label="Peso"
              value={
                variant.weight ? (
                  `${variant.weight}g`
                ) : (
                  <NAValue tooltipText="Peso não informado" />
                )
              }
              theme={theme}
            />
            <InfoItem
              label="Dimensões"
              value={
                variant.width && variant.height && variant.length ? (
                  `${variant.width} × ${variant.height} × ${variant.length} cm`
                ) : (
                  <NAValue tooltipText="Dimensões não informadas" />
                )
              }
              theme={theme}
            />
            <InfoItem
              label="Criado em"
              value={
                variant.createdAt ? (
                  formatDate(variant.createdAt)
                ) : (
                  <NAValue tooltipText="Data de criação não disponível" />
                )
              }
              theme={theme}
            />
            <InfoItem
              label="Atualizado em"
              value={
                variant.updatedAt ? (
                  formatDate(variant.updatedAt)
                ) : (
                  <NAValue tooltipText="Data de atualização não disponível" />
                )
              }
              theme={theme}
            />
          </Grid>
        </AccordionPanel>
      </AccordionItem>
    );

    const renderDescription = () => {
      if (!variant.description) return null;

      return (
        <AccordionItem
          border="1px"
          borderColor={borderColor}
          borderRadius="md"
          mb={3}
        >
          <AccordionButton p={3} _hover={{ bg: cardBg }}>
            <HStack spacing={3} flex={1} textAlign="left">
              <FiTag size="16px" color="var(--chakra-colors-purple-500)" />
              <Text fontSize="sm" fontWeight="600" color={textColor}>
                Descrição da Variante
              </Text>
            </HStack>
            <AccordionIcon />
          </AccordionButton>
          <AccordionPanel p={4}>
            <Box
              dangerouslySetInnerHTML={{ __html: variant.description }}
              sx={{
                '& p': { mb: 2 },
                '& strong': { fontWeight: 'bold' },
                '& div': { mb: 2 },
              }}
            />
          </AccordionPanel>
        </AccordionItem>
      );
    };

    const renderMetadata = () => {
      if (!variant.metadata || Object.keys(variant.metadata).length === 0)
        return null;

      return (
        <AccordionItem border="1px" borderColor={borderColor} borderRadius="md">
          <AccordionButton p={3} _hover={{ bg: cardBg }}>
            <HStack spacing={3} flex={1} textAlign="left">
              <FiSettings size="16px" color="var(--chakra-colors-gray-500)" />
              <Text fontSize="sm" fontWeight="600" color={textColor}>
                Metadados da Variante
              </Text>
            </HStack>
            <AccordionIcon />
          </AccordionButton>
          <AccordionPanel p={4}>
            <Box
              bg="gray.900"
              color="green.300"
              p={4}
              borderRadius="lg"
              fontSize="xs"
              fontFamily="mono"
              overflowX="auto"
              border="1px"
              borderColor="gray.700"
              maxH="200px"
              overflowY="auto"
            >
              <pre>{JSON.stringify(variant.metadata, null, 2)}</pre>
            </Box>
          </AccordionPanel>
        </AccordionItem>
      );
    };

    return (
      <AccordionItem
        border="1px"
        borderColor={borderColor}
        borderRadius="lg"
        mb={3}
        bg={cardBg}
      >
        <AccordionButton p={4} _hover={{ bg: sectionBg }}>
          <HStack spacing={4} flex={1} textAlign="left">
            <ProductImage
              src={variant.imageUrl}
              alt={variant.name}
              boxSize="50px"
            />
            <VStack align="start" spacing={1} flex={1}>
              <Text fontWeight="600" fontSize="md" color={textColor}>
                {variant.name || `Variante ${index + 1}`}
              </Text>
              <HStack spacing={4} fontSize="sm" color={mutedColor}>
                <Text>
                  SKU:{' '}
                  {variant.sku || <NAValue tooltipText="SKU não informado" />}
                </Text>
                <Text>Estoque: {variant.stockQuantity || 0}</Text>
                <Text fontWeight="600" color="green.500">
                  {MoneyUtils.formatCurrency(variant.price)}
                </Text>
              </HStack>
            </VStack>
            {variant.url && (
              <Tooltip label="Ver produto na loja" placement="top" mr={6}>
                <Button
                  colorScheme="blue"
                  size="sm"
                  variant="outline"
                  leftIcon={<FiShoppingBag />}
                  onClick={(e) => onExternalLink(variant.url, e)}
                >
                  Loja
                </Button>
              </Tooltip>
            )}
          </HStack>
          <AccordionIcon />
        </AccordionButton>
        <AccordionPanel p={6} bg={sectionBg}>
          <Accordion allowToggle defaultIndex={[0]}>
            {renderBasicInfo()}
            {renderPricesAndStock()}
            {renderSpecifications()}
            {renderDescription()}
            {renderMetadata()}
          </Accordion>
        </AccordionPanel>
      </AccordionItem>
    );
  },
);

VariantItem.displayName = 'VariantItem';

export default VariantItem;
