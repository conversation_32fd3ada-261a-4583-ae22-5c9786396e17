import {
  Box,
  Button,
  Checkbox,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  Input,
  Stack,
  Text,
  useToast,
} from '@chakra-ui/react';
import { useMutation, useQuery } from 'react-query';
import { apiRoutes } from '../../../../constants/api-routes';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { colors } from '../../../../constants/colors';
import {
  IntegrationsService,
  SaveWooCommerceCredentialsDto,
  IntegrationsConfigDto,
  SaveIntegrationsConfigDto,
} from '../../../../services/integrations.service';
import { useState } from 'react';
import { SourceIntegration } from '../../../../types/SourceIntegration';
import IntegrationsTable, {
  IntegrationsTableColumn,
} from '../../../../components/IntegrationsTable';
import { ViewIcon, ViewOffIcon } from '@chakra-ui/icons';
import { InputGroup, InputRightElement } from '@chakra-ui/react';

const createSchema = () =>
  yup.object({
    id: yup.string().default(''),
    description: yup.string().default('').required('A descrição é obrigatória'),
    wooCommerceClientId: yup
      .string()
      .default('')
      .when('id', {
        is: (id: string) => id == '',
        then: (schema) => schema.required('O "Client ID" é obrigatório'),
        otherwise: (schema) => schema,
      }),
    wooCommerceClientSecret: yup
      .string()
      .default('')
      .when('id', {
        is: (id: string) => id == '',
        then: (schema) => schema.required('O "Client Secret" é obrigatório'),
        otherwise: (schema) => schema,
      }),
    wooCommerceStoreDomain: yup
      .string()
      .default('')
      .when('id', {
        is: (id: string) => id == '',
        then: (schema) => schema.required('O domínio da loja é obrigatório'),
        otherwise: (schema) => schema,
      }),
    isOrderActive: yup.boolean().default(true),
    isProductActive: yup.boolean().default(false),
    isActive: yup.boolean().default(true),
  });

const WooCommercePage = () => {
  const toast = useToast();
  const [showForm, setShowForm] = useState(false);
  const [showClientId, setShowClientId] = useState(false);

  const tableColumns: IntegrationsTableColumn[] = [
    { key: 'isOrderActive', label: 'Sincronização de Pedidos', show: true },
    { key: 'isProductActive', label: 'Sincronização de Produtos', show: false },
  ];

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    getValues,
    setValue,
    reset,
    watch,
  } = useForm({
    resolver: yupResolver(createSchema()),
  });

  const watchedId = watch('id');

  const { data: integrationsConfig, refetch: refetchIntegrationsConfig } =
    useQuery(
      apiRoutes.getIntegrationsConfig(SourceIntegration.woo_commerce),
      async () => {
        const { data } = await IntegrationsService.getIntegrationsConfig(
          SourceIntegration.woo_commerce,
        );
        return data;
      },
    );

  const saveWooCommerceCredentials = useMutation(
    (data: SaveIntegrationsConfigDto<SaveWooCommerceCredentialsDto>) =>
      IntegrationsService.saveIntegrationConfig(data),
    {
      onSuccess: () => {
        refetchIntegrationsConfig();
        toast({
          title: 'Configurações salvas com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      },
    },
  );

  async function handleSaveData() {
    const config: SaveWooCommerceCredentialsDto = {
      wooCommerceClientId: getValues('wooCommerceClientId') || '',
      wooCommerceClientSecret: getValues('wooCommerceClientSecret') || '',
      wooCommerceStoreDomain: getValues('wooCommerceStoreDomain') || '',
    };

    const data = {
      id: getValues('id') || undefined,
      isOrderActive: getValues('isOrderActive'),
      source: SourceIntegration.woo_commerce,
      description: getValues('description'),
      config,
      isActive: getValues('isActive'),
      isAbandonedCartActive: true,
      isProductActive: getValues('isProductActive'),
      companyId: '',
    };
    await saveWooCommerceCredentials.mutateAsync(data);

    reset();
    setShowForm(false);
  }

  async function onSubmit() {
    handleSaveData();
  }

  const handleEditIntegration = (integration: IntegrationsConfigDto) => {
    setShowForm(true);
    setValue('id', integration.id);
    setValue('description', integration.description);
    setValue('isOrderActive', integration.isOrderActive);
    setValue('isProductActive', integration.isProductActive);
    setValue('isAbandonedCartActive', true);
    setValue('isActive', integration.isActive);

    const config = integration.config as SaveWooCommerceCredentialsDto;
    if (config) {
      setValue('wooCommerceClientId', config.wooCommerceClientId || '');
      setValue('wooCommerceClientSecret', '');
      setValue('wooCommerceStoreDomain', config.wooCommerceStoreDomain || '');
    }
  };

  return (
    <Box>
      <Heading size="md" mb={4}>
        Integração WooCommerce
      </Heading>
      {!showForm ? (
        <Flex justifyContent="flex-end" mb={4}>
          <Button
            colorScheme="green"
            onClick={() => setShowForm(true)}
            isLoading={saveWooCommerceCredentials.isLoading}
          >
            Adicionar Nova Integração
          </Button>
        </Flex>
      ) : (
        <Box mb={6}>
          <Heading size="sm" mb={4}>
            {watchedId ? 'Editar Integração' : 'Cadastrar Nova Integração'}
          </Heading>
          <form onSubmit={handleSubmit(onSubmit)}>
            <Stack spacing={4} mt={4}>
              <FormControl isRequired>
                <FormLabel>Descrição</FormLabel>
                <Input
                  placeholder="Descrição da integração"
                  {...register('description')}
                  isInvalid={!!errors.description?.message}
                />
                <Text color={colors.danger} fontSize="xs">
                  {errors.description?.message}
                </Text>
              </FormControl>
              <FormControl isRequired>
                <FormLabel>Domínio da Loja</FormLabel>
                <Input
                  placeholder="Domínio da loja"
                  {...register('wooCommerceStoreDomain')}
                  isInvalid={!!errors.wooCommerceStoreDomain?.message}
                />
                <Text color={colors.danger} fontSize="xs">
                  {errors.wooCommerceStoreDomain?.message}
                </Text>
              </FormControl>

              <FormControl isRequired>
                <FormLabel>Client ID</FormLabel>
                <InputGroup>
                  <Input
                    placeholder="Client ID"
                    type={showClientId ? 'text' : 'password'}
                    {...register('wooCommerceClientId')}
                    isInvalid={!!errors.wooCommerceClientId?.message}
                  />
                  <InputRightElement h="full">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowClientId((v) => !v)}
                      tabIndex={-1}
                      aria-label={
                        showClientId
                          ? 'Esconder Client Id'
                          : 'Mostrar Client Id'
                      }
                    >
                      {showClientId ? <ViewOffIcon /> : <ViewIcon />}
                    </Button>
                  </InputRightElement>
                </InputGroup>
                <Text color={colors.danger} fontSize="xs">
                  {errors.wooCommerceClientId?.message}
                </Text>
              </FormControl>

              <FormControl>
                <FormLabel>Client Secret</FormLabel>
                <InputGroup>
                  <Input
                    placeholder="Client Secret"
                    type={showClientId ? 'text' : 'password'}
                    {...register('wooCommerceClientSecret')}
                    isInvalid={!!errors.wooCommerceClientSecret?.message}
                  />
                  <InputRightElement h="full">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowClientId((v) => !v)}
                      tabIndex={-1}
                      aria-label={
                        showClientId
                          ? 'Esconder Client Secret'
                          : 'Mostrar Client Secret'
                      }
                    >
                      {showClientId ? <ViewOffIcon /> : <ViewIcon />}
                    </Button>
                  </InputRightElement>
                </InputGroup>
                <Text color={colors.danger} fontSize="xs">
                  {errors.wooCommerceClientSecret?.message}
                </Text>
              </FormControl>

              <FormControl>
                <Controller
                  name="isOrderActive"
                  control={control}
                  defaultValue={false}
                  render={({ field }) => (
                    <Checkbox
                      isChecked={field.value}
                      onChange={(e) =>
                        setValue('isOrderActive', e.target.checked)
                      }
                    >
                      Sincronizar Pedidos
                    </Checkbox>
                  )}
                />
              </FormControl>

              <FormControl>
                <Controller
                  name="isProductActive"
                  control={control}
                  defaultValue={false}
                  render={({ field }) => (
                    <Checkbox
                      isChecked={field.value}
                      onChange={(e) =>
                        setValue('isProductActive', e.target.checked)
                      }
                    >
                      Sincronizar Produtos
                    </Checkbox>
                  )}
                />
              </FormControl>

              <FormControl>
                <Controller
                  name="isActive"
                  control={control}
                  defaultValue={true}
                  render={({ field }) => (
                    <Checkbox
                      isChecked={field.value}
                      onChange={(e) => setValue('isActive', e.target.checked)}
                    >
                      Integração Ativa
                    </Checkbox>
                  )}
                />
              </FormControl>

              <Flex justify="flex-end">
                <Box
                  flexDirection="row"
                  gap="14px"
                  display="flex"
                  alignItems="center"
                >
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setShowForm(false);
                      reset();
                    }}
                  >
                    Cancelar
                  </Button>
                  <Button
                    type="submit"
                    color={colors.white}
                    bgColor={colors.primary}
                    width="fit-content"
                    isLoading={saveWooCommerceCredentials.isLoading}
                  >
                    Salvar
                  </Button>
                </Box>
              </Flex>
            </Stack>
          </form>
        </Box>
      )}

      <IntegrationsTable
        integrationsConfig={integrationsConfig}
        sourceIntegration={SourceIntegration.woo_commerce}
        columns={tableColumns}
        onEdit={handleEditIntegration}
        isLoading={saveWooCommerceCredentials.isLoading}
      />
    </Box>
  );
};

export default WooCommercePage;
