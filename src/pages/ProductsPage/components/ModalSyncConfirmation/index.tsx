import {
  <PERSON>dal,
  Modal<PERSON>verlay,
  <PERSON>dal<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ModalBody,
  ModalCloseButton,
  Button,
  Text,
  HStack,
  VStack,
  Icon,
  Box,
  Alert,
  AlertIcon,
  AlertDescription,
  Flex,
} from '@chakra-ui/react';
import { MdSync } from 'react-icons/md';

interface SyncMetaConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  selectedCount?: number;
}

const SyncMetaConfirmationModal = ({
  isOpen,
  onClose,
  onConfirm,
  selectedCount = 0,
}: SyncMetaConfirmationModalProps) => {
  const hasSelection = selectedCount > 0;

  const getTitle = () => {
    return hasSelection
      ? `Sincronizar ${selectedCount} produtos com Meta`
      : 'Sincronizar produtos com Meta';
  };

  const getMessage = () => {
    return hasSelection
      ? `${selectedCount} produtos serão enviados para o catálogo da Meta.`
      : 'Todos os produtos elegíveis serão enviados para o catálogo da Meta.';
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="md" isCentered>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>
          <HStack spacing={3}>
            <Icon as={MdSync} color="blue.500" boxSize={5} />
            <Text fontSize="lg">{getTitle()}</Text>
          </HStack>
        </ModalHeader>
        <ModalCloseButton />

        <ModalBody>
          <VStack spacing={4} align="stretch">
            <Text color="gray.600">{getMessage()}</Text>
            <Box>
              <Text fontWeight="semibold" mb={2} color="gray.700" fontSize="sm">
                O que vai acontecer:
              </Text>
              <VStack align="start" spacing={1}>
                <Text fontSize="sm" color="gray.600">
                  • Produtos serão enviados para o catálogo da Meta
                </Text>
                <Text fontSize="sm" color="gray.600">
                  • Produtos ficarão em aprovação pela Meta
                </Text>
                <Text fontSize="sm" color="gray.600">
                  • Após aprovação, estarão disponíveis no WhatsApp
                </Text>
              </VStack>
            </Box>
            <Alert status="info" borderRadius="md">
              <AlertIcon />
              <Box>
                <AlertDescription fontSize="sm">
                  <Text mb={1}>
                    Apenas produtos com "Sincronizar Meta" ativado serão
                    enviados e listados no catálogo.
                  </Text>
                  <Text color="orange.600" fontWeight="medium">
                    ⚠️ Produtos ficam em aprovação pela Meta após o envio e só
                    podem ser usados no carrinho do WhatsApp após aprovação.
                  </Text>
                </AlertDescription>
              </Box>
            </Alert>
            {hasSelection && (
              <Box p={3} bg="blue.50" borderRadius="md">
                <Text fontSize="sm" color="blue.700">
                  <strong>{selectedCount}</strong> produtos selecionados para
                  sincronização
                </Text>
              </Box>
            )}
          </VStack>
        </ModalBody>

        <ModalFooter>
          <Flex width="100%" justify="space-between" gap={4}>
            <Button variant="outline" mr={3} onClick={onClose}>
              Cancelar
            </Button>
            <Button variant="primary" onClick={onConfirm} leftIcon={<MdSync />}>
              Sincronizar com Meta
            </Button>
          </Flex>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default SyncMetaConfirmationModal;
