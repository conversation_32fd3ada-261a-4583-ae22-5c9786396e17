# Etapa 1: Build (com cache e variáveis de build)
FROM node:20-alpine AS builder

ARG VITE_BACKEND_URL=localhost:3001
ARG VITE_REACT_DISABLE_SSL=true
ARG VITE_ENABLE_DEBUG_TOOLS=false
ARG VITE_FRONTEND_DOMAIN=localhost:3000

ENV VITE_BACKEND_URL=$VITE_BACKEND_URL \
    VITE_REACT_DISABLE_SSL=$VITE_REACT_DISABLE_SSL \
    VITE_ENABLE_DEBUG_TOOLS=$VITE_ENABLE_DEBUG_TOOLS \
    VITE_FRONTEND_DOMAIN=$VITE_FRONTEND_DOMAIN \
    GENERATE_SOURCEMAP=false \
    NODE_OPTIONS=--max_old_space_size=4096

WORKDIR /app

COPY package*.json yarn.lock ./
RUN yarn install --frozen-lockfile

COPY . .

RUN yarn build

# Etapa 2: Runtime com serve (imagem mais enxuta)
FROM node:20-alpine

WORKDIR /app

RUN yarn global add serve

COPY --from=builder /app/build ./build

EXPOSE 3000
CMD ["serve", "-s", "build", "-l", "3000"]
