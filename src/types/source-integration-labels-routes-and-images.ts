export const SourceIntegrationLabelsRoutesAndImages = {
  bling: {
    label: 'Bling',
    image: 'integrations-logomark/bling.png',
    routeKey: 'bling',
  },
  cartPanda: {
    label: 'Cartpanda',
    image: 'integrations-logomark/cart-panda.png',
    routeKey: 'cartPanda',
  },
  custom: {
    label: 'Custom',
    image: '',
    routeKey: '',
  },
  direct_message: {
    label: 'Mensagem Direta',
    image: '',
    routeKey: '',
  },
  file_import: {
    label: 'Importação de Arquivo',
    image: '',
    routeKey: '',
  },
  google_tag_manager: {
    label: 'Google Tag Manager',
    image: 'integrations-logomark/google-tag-manager.png',
    routeKey: '',
  },
  hubspot_crm: {
    label: 'HubSpot CRM',
    image: '',
    routeKey: '',
  },
  ingresse: {
    label: 'Ingresse',
    image: 'integrations-logomark/ingresse.png',
    routeKey: 'ingresse',
  },
  loja_integrada: {
    label: 'Loja Integrada',
    image: 'integrations-logomark/loja-integrada.png',
    routeKey: 'lojaIntegrada',
  },
  magazord: {
    label: 'Magazord',
    image: 'integrations-logomark/magazord.png',
    routeKey: 'magazord',
  },
  magento1_ecommerce: {
    label: 'Magento Versão 1',
    image: 'integrations-logomark/magento.png',
    routeKey: 'magento',
  },
  magento2_ecommerce: {
    label: 'Magento Versão 2',
    image: 'integrations-logomark/magento.png',
    routeKey: 'magento',
  },
  omie: {
    label: 'Omie',
    image: 'integrations-logomark/omie.png',
    routeKey: 'omie',
  },
  omny: {
    label: 'Omny',
    image: 'integrations-logomark/omny.png',
    routeKey: 'omny',
  },
  shopify_ecommerce: {
    label: 'Shopify Ecommerce',
    image: 'integrations-logomark/shopify.png',
    routeKey: 'shopify',
  },
  tray: {
    label: 'Tray',
    image: 'integrations-logomark/tray.png',
    routeKey: 'tray',
  },
  unbox: {
    label: 'Unbox',
    image: 'integrations-logomark/unbox.png',
    routeKey: 'unbox',
  },
  unknown: {
    label: 'Desconhecido',
    image: '',
    routeKey: '',
  },
  visual_ecommerce: {
    label: 'Visual Ecommerce',
    image: 'integrations-logomark/visual-ecommerce.png',
    routeKey: '',
  },
  vtex_ecommerce: {
    label: 'VTEX Ecommerce',
    image: 'integrations-logomark/vtex.png',
    routeKey: 'vtex',
  },
  woo_commerce: {
    label: 'WooCommerce',
    image: 'integrations-logomark/woo-commerce.png',
    routeKey: 'wooCommerce',
  },
  yampi: {
    label: 'Yampi',
    image: 'integrations-logomark/yampi.png',
    routeKey: 'yampi',
  },
  nuvem_shop: {
    label: 'Nuvem Shop',
    image: 'integrations-logomark/nuvem-shop.svg',
    routeKey: 'nuvemShop',
  },
  shoppub: {
    label: 'Shoppub',
    image: 'integrations-logomark/shoppub.png',
    routeKey: 'shoppub',
  },
  linx_commerce: {
    label: 'Linx Commerce',
    image: 'integrations-logomark/linx_commerce.png',
    routeKey: 'linxCommerce',
  },
  tiny: {
    label: 'Tiny',
    image: 'integrations-logomark/tiny_olist.svg',
    routeKey: 'tiny',
  },
  vnda: {
    label: 'Vnda',
    image: 'integrations-logomark/vnda_by_olist.png',
    routeKey: 'vnda',
  },
  millennium: {
    label: 'Millennium',
    image: 'integrations-logomark/e-millennium.png',
    routeKey: 'millennium',
  },
  varejo_online: {
    label: 'Varejo Online',
    image: 'integrations-logomark/varejo_online.svg',
    routeKey: 'varejo_online',
  },
  venda_ai: {
    label: 'Venda Ai',
    image: 'integrations-logomark/venda_ai.png',
    routeKey: 'venda_ai',
  },
  irroba: {
    label: 'Irroba',
    image: 'integrations-logomark/irroba.svg',
    routeKey: 'irroba',
  },
  wake_commerce: {
    label: 'Wake Commerce',
    image: 'integrations-logomark/wake-commerce.svg',
    routeKey: 'wake_commerce',
  },
  uappi: {
    label: 'Uappi',
    image: 'integrations-logomark/uappi-logo.svg',
    routeKey: 'uappi',
  },
};
