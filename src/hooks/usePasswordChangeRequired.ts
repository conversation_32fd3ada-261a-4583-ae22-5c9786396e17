import { useSelector } from 'react-redux';
import { useLocation, useNavigate } from 'react-router-dom';
import { RootState } from '../state/store';
import { useEffect } from 'react';
import { appPaths } from '../constants/app-paths';
import { useQuery } from 'react-query';
import { AuthService } from '../services/auth.service';
import { apiRoutes } from '../constants/api-routes';

export const usePasswordChangeRequired = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { userEmail, isPasswordChangeRequired } = useSelector(
    (state: RootState) => ({
      userEmail: state.auth.currentUser?.email!,
      isPasswordChangeRequired: state.auth.userActions.isPasswordChangeRequired,
    }),
  );

  const { data, isLoading: isLoadingRequestUpdatePasswordToken } = useQuery({
    queryKey: [apiRoutes.requestUpdatePasswordToken(), userEmail],
    queryFn: async () => {
      const { data } = await AuthService.requestUpdatePasswordToken({
        userEmail,
      });
      return data;
    },
    enabled: isPasswordChangeRequired,
  });

  useEffect(() => {
    if (isPasswordChangeRequired && data) {
      const urlParams = new URLSearchParams();
      urlParams.set('token', data?.resetToken);
      urlParams.set('redirect', location.pathname);
      navigate(`${appPaths.updatePassword()}?${urlParams.toString()}`);
    }
  }, [isPasswordChangeRequired, data]);

  return {
    isPasswordChangeRequired,
    isLoadingRequestUpdatePasswordToken,
  };
};
