import React from 'react';
import {
  Card,
  CardBody,
  HStack,
  InputGroup,
  Input,
  InputRightElement,
  Button,
} from '@chakra-ui/react';
import { SearchIcon } from '@chakra-ui/icons';

interface ProductSearchProps {
  searchInput: string;
  onSearchInputChange: (value: string) => void;
  onSearch: () => void;
  isLoading: boolean;
}

export const ProductSearch = ({
  searchInput,
  onSearchInputChange,
  onSearch,
  isLoading,
}: ProductSearchProps) => {
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      onSearch();
    }
  };

  return (
    <Card variant="outline">
      <CardBody p={4}>
        <HStack spacing={3}>
          <InputGroup flex={1}>
            <Input
              placeholder="Buscar produtos por nome, SKU ou descrição..."
              value={searchInput}
              onChange={(e) => onSearchInputChange(e.target.value)}
              bg="gray.50"
              border="2px"
              borderColor="gray.200"
              _focus={{ borderColor: 'blue.400', bg: 'white' }}
              onKeyPress={handleKeyPress}
            />
            <InputRightElement>
              <SearchIcon color="gray.400" />
            </InputRightElement>
          </InputGroup>
          <Button
            onClick={onSearch}
            isLoading={isLoading}
            colorScheme="blue"
            variant="outline"
            leftIcon={<SearchIcon />}
          >
            Buscar
          </Button>
        </HStack>
      </CardBody>
    </Card>
  );
};
