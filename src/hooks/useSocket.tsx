import {
  createContext,
  ReactNode,
  useContext,
  useEffect,
  useState,
} from 'react';
import { useDispatch, useSelector } from 'react-redux';
import io from 'socket.io-client';
import { baseURL } from '../constants/base-url';
import { AuthService } from '../services/auth.service';
import {
  addMessagesToConversation,
  closeConversationTickets,
  insertConversation,
  updateConversation,
  updateMessageByTempId,
  updateMessageStatusByTempId,
  updateTicketAgentIdForConversation,
} from '../state/inboxSlice';
import { RootState } from '../state/store';
import { CloseAllTicketsEventPayload } from '../types/CloseAllTicketsEventPayload';
import { NewConversationEventPayload } from '../types/NewConversationEventPayload';
import { NewConversationTicketEventPayload } from '../types/NewConversationTicketEventPayload';
import { NewMessageEventPayload } from '../types/NewMessageEventPayload';
import { UpdateConversationEventPayload } from '../types/UpdateConversationEventPayload';
import { UpdateMessageStatusEventPayload } from '../types/UpdateMessageStatusEventPayload';
import { UpdateConversationTicketAgentEventPayload } from '../types/UpdateConversationTicketAgentEventPayload';
import { UpdateMessageEventPayload } from '../types/UpdateMessageUploadProgressEventPayload';
import ErrorService from '../services/errorService';
import { useNotification } from './useNotification';
import { useEventEmitter } from './useEventEmitter';
import { EventsEnum } from '../constants/events';

const protocol = !!import.meta.env.VITE_REACT_DISABLE_SSL ? 'ws' : 'wss';
const SOCKET_URL = `${protocol}://${baseURL}`;
const PING_PONG_INTERVAL = 30000;
const RECONNECT_DELAY = 5000;

export enum SocketEventsEnum {
  NEW_MESSAGE = 'new.message',
  UPDATE_MESSAGE = 'update.message',
  UPDATE_MESSAGE_STATUS = 'update.message.status',

  NEW_CONVERSATION = 'new.conversation',
  UPDATE_CONVERSATION = 'update.conversation',
  CLOSE_ALL_CONVERSATION_TICKETS = 'close.all.conversation.tickets',

  NEW_CONVERSATION_TICKET = 'new.conversation.ticket',
  UPDATE_CONVERSATION_TICKET_AGENT = 'update.conversation.ticket.agent',
  PING = 'ping',
  PONG = 'pong',
}

interface SocketContextData {
  socket: SocketIOClient.Socket;
  isConnected: boolean;
}

interface SocketProviderProps {
  children?: ReactNode;
}

const SocketContext = createContext({} as SocketContextData);

export let socket: any;
let pingPontInterval: NodeJS.Timeout;
let lastPongTime: number;

export function SocketProvider({ children }: SocketProviderProps) {
  const { playNewMessageNotification } = useNotification();
  const { emitEvent } = useEventEmitter();
  const { currentUser } = useSelector((state: RootState) => state.auth);
  const dispatch = useDispatch();
  const [isTabActive, setIsTabActive] = useState(true);
  const [isConnected, setIsConnected] = useState(false);

  const startPingPong = () => {
    if (pingPontInterval) {
      clearInterval(pingPontInterval);
    }

    pingPontInterval = setInterval(() => {
      if (socket && isConnected) {
        lastPongTime = Date.now();
        socket.emit(SocketEventsEnum.PING);
      }
    }, PING_PONG_INTERVAL);
  };

  const checkConnection = () => {
    if (lastPongTime && Date.now() - lastPongTime > PING_PONG_INTERVAL * 2) {
      console.log('Connection lost, attempting to reconnect...');
      socket.disconnect();
      socket.connect();
    }
  };

  useEffect(() => {
    document.addEventListener('visibilitychange', () => {
      const tabState = document.visibilityState === 'visible';
      setIsTabActive(tabState);

      if (tabState) {
        checkConnection();
      }
    });

    return () => {
      document.removeEventListener('visibilitychange', () => {});
    };
  }, []);

  useEffect(() => {
    socket = io.connect(SOCKET_URL, {
      reconnectionDelayMax: 10000,
      reconnectionAttempts: 5,
      timeout: 20000,
      auth: {
        token: AuthService.getAccessToken(),
      },
      transports: ['websocket', 'polling'],
    });

    socket.on('connect', () => {
      console.log('Socket connected!');
      setIsConnected(true);
      startPingPong();
    });

    socket.on('disconnect', () => {
      console.log('Socket disconnected');
      setIsConnected(false);
      clearInterval(pingPontInterval);
    });

    socket.on('connect_error', (error: unknown) => {
      console.error('Falha na conexão do socket', error);
      ErrorService.captureError(error, {
        message: 'Falha na conexão do socket',
      });
      setIsConnected(false);
      setTimeout(() => {
        socket.connect();
      }, RECONNECT_DELAY);
    });

    socket.on(SocketEventsEnum.PONG, () => {
      lastPongTime = Date.now();
    });

    socket.on(
      SocketEventsEnum.NEW_MESSAGE,
      (event: { event: string; data: NewMessageEventPayload }) => {
        dispatch(
          addMessagesToConversation({
            conversationId: event.data.message.conversationId,
            messages: [event.data.message],
          }),
        );

        if (!event.data.message.fromSystem) {
          playNewMessageNotification('Nova mensagem');
        }
      },
    );

    socket.on(
      SocketEventsEnum.UPDATE_MESSAGE_STATUS,
      (event: { event: string; data: UpdateMessageStatusEventPayload }) => {
        dispatch(
          updateMessageStatusByTempId({
            conversationId: event.data.conversationId,
            status: event.data.status,
            tempId: event.data.tempId,
          }),
        );
      },
    );

    socket.on(
      SocketEventsEnum.UPDATE_MESSAGE,
      (event: { event: string; data: UpdateMessageEventPayload }) => {
        dispatch(
          updateMessageByTempId({
            conversationId: event.data.conversationId,
            tempId: event.data.tempId,
            message: event.data.message,
          }),
        );
      },
    );

    socket.on(
      SocketEventsEnum.NEW_CONVERSATION,
      (event: { event: string; data: NewConversationEventPayload }) => {
        dispatch(
          insertConversation({
            conversation: {
              id: event.data.conversation.id,
              lastMessage: event.data.conversation.messages[0],
              recipientName: event.data.conversation.recipientName,
              categoryId: event.data.conversation.categoryId,
              hasOpenTicket:
                event.data.conversation.conversationTickets.at(-1)?.status ===
                'open',
              ticketAgentId:
                event.data.conversation.conversationTickets.at(-1)?.agentId ||
                null,
            },
          }),
        );
      },
    );

    socket.on(
      SocketEventsEnum.CLOSE_ALL_CONVERSATION_TICKETS,
      (event: { event: string; data: CloseAllTicketsEventPayload }) => {
        dispatch(
          closeConversationTickets({
            conversation: event.data.conversation,
          }),
        );
      },
    );

    socket.on(
      SocketEventsEnum.NEW_CONVERSATION_TICKET,
      (event: { event: string; data: NewConversationTicketEventPayload }) => {
        dispatch(
          insertConversation({
            conversation: {
              id: event.data.conversationTicket.conversation.id,
              lastMessage:
                event.data.conversationTicket.conversation.messages[0],
              recipientName:
                event.data.conversationTicket.conversation.recipientName,
              hasOpenTicket: event.data.conversationTicket.status === 'open',
              categoryId: event.data.conversationTicket.conversation.categoryId,
              ticketAgentId: event.data.conversationTicket.agentId || null,
            },
          }),
        );
      },
    );

    socket.on(
      SocketEventsEnum.UPDATE_CONVERSATION_TICKET_AGENT,
      (event: {
        event: string;
        data: UpdateConversationTicketAgentEventPayload;
      }) => {
        emitEvent(EventsEnum.UPDATED_CONVERSATION_TICKET_AGENT, event.data);
        dispatch(
          updateTicketAgentIdForConversation({
            conversationId: event.data.conversationId,
            agentId: event.data.agentId,
            oldAgentId: event.data.oldAgentId,
          }),
        );
      },
    );

    socket.on(
      SocketEventsEnum.UPDATE_CONVERSATION,
      (event: { event: string; data: UpdateConversationEventPayload }) => {
        dispatch(
          updateConversation({
            currentConversation: event.data.currentConversation,
            previoustConversation: event.data.previoustConversation,
          }),
        );
      },
    );

    return () => {
      clearInterval(pingPontInterval);
      socket.disconnect();
    };
  }, [currentUser, dispatch, isTabActive]);

  return (
    <SocketContext.Provider
      value={{
        socket,
        isConnected,
      }}
    >
      {children}
    </SocketContext.Provider>
  );
}

export function useSocket(): SocketContextData {
  const context = useContext(SocketContext);
  if (!context) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return context;
}
