/// <reference lib="WebWorker" />
import { clientsClaim } from 'workbox-core';
import { precacheAndRoute } from 'workbox-precaching';

declare let self: ServiceWorkerGlobalScope;

type NotificationProps = NotificationOptions & {
  vibrate?: number | number[];
  actions?: any[];
};

clientsClaim();
precacheAndRoute(self.__WB_MANIFEST);

self.addEventListener('install', () => {
  self.skipWaiting();
});

self.addEventListener('activate', (event) => {
  event.waitUntil(self.clients.claim());
});

self.addEventListener('push', (event) => {
  let payload: any = null;
  try {
    payload = event.data?.json();
  } catch {}
  if (!payload)
    payload = {
      type: 'notification',
      title: 'Revi',
      body: 'Você tem uma nova mensagem!',
      icon: '/logo192.png',
      badge: '/logo192.png',
      tag: 'revi-notification',
    };

  if (payload.type === 'notification') {
    const options: NotificationProps = {
      body: payload.body,
      icon: payload.icon,
      badge: payload.badge,
      tag: payload.tag,
      vibrate: [200, 100, 200],
      data: payload.data || {},
      actions: payload.actions || [],
      ...(payload.ios && {
        silent: payload.ios.silent || false,
        requireInteraction: payload.ios.requireInteraction || false,
      }),
    };
    event.waitUntil(self.registration.showNotification(payload.title, options));
  }
});

self.addEventListener('notificationclick', (event) => {
  event.notification.close();
  event.waitUntil(
    self.clients
      .matchAll({ type: 'window', includeUncontrolled: true })
      .then((clients) => {
        for (const client of clients) {
          if (client.url.includes('/inbox') && 'focus' in client)
            return client.focus();
        }
        if (self.clients.openWindow) return self.clients.openWindow('/inbox');
      }),
  );
});

self.addEventListener('message', (event) => {
  if (event.data?.type === 'SKIP_WAITING') self.skipWaiting();
  if (event.data?.type === 'GET_VERSION')
    event.ports[0].postMessage({ version: 'revi-runtime' });
});
