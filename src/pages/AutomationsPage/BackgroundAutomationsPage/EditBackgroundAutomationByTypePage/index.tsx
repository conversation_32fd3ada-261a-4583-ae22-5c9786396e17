import { useToast } from '@chakra-ui/react';
import { useEffect, useMemo, useState } from 'react';
import { useMutation, useQuery } from 'react-query';
import { useNavigate, useParams } from 'react-router-dom';
import { apiRoutes } from '../../../../constants/api-routes';
import { appPaths } from '../../../../constants/app-paths';
import {
  AutomationsService,
  UpdateAutomationFromIntegrationDto,
} from '../../../../services/automations.service';
import { Automation } from '../../../../types/Automation';
import AutomationByTypeForm from '../components/AutomationByTypeForm';
import LoadingScreen from '../../../../components/LoadingScreen';
import { AutomationAction } from '../../../../types/Prisma';
import { IntegrationsService } from '../../../../services/integrations.service';
import IntegrationActiveKeyBySourceIntegration from '../constants/integration-active-key-by-source-integration';
import TopIntegrationInactiveErrorMessage from '../components/TopIntegrationInactiveErrorMessage';
import GlassmorphisContainer from '../../../../components/GlassmorphismContainer';

export interface OrderStatusAutomationData {
  orderStatuses: string[];
  paymentMethods?: string[];
  cancelReasons?: string[];
}

export type AutomationData = OrderStatusAutomationData;
export interface AutomationByTypeFormValues {
  name: string;
  isActive: boolean;
  messageTemplateId: string | null;
  emailTemplateId: string | null;
  flowId: string | null;
  data: AutomationData;
  dailyMessageLimitOnWhatsapp: number;
  dailyEmailLimit: number;
  action: AutomationAction;
}

const EditBackgroundAutomationByTypePage = () => {
  const { automationId, automationTypeId } = useParams();

  function processData(data: Automation) {
    if (data.templateArgs) {
      setWhatsappTemplateArgs(data.templateArgs);
    }

    if (data.emailTemplateArgs) {
      setEmailTemplateArgs(data.emailTemplateArgs);
    }
  }

  const { data: automationType, isFetching: isFetchingAutomationType } =
    useQuery(apiRoutes.showAutomationType(automationTypeId!), async () => {
      const { data } = await AutomationsService.showAutomationType(
        automationTypeId!,
      );
      return data;
    });

  const { data: automation, isFetching: isFetchingAutomation } = useQuery(
    apiRoutes.showAutomation(automationId!),
    async () => {
      const { data } = await AutomationsService.showAutomation(automationId!);
      return data;
    },
  );

  useEffect(() => {
    if (automation) {
      processData(automation);
    }
  }, [automation]);

  const { data: integrationStatus, isFetching: isFetchingIntegrationStatus } =
    useQuery(apiRoutes.getIntegrationStatusSummary(), async () => {
      const { data } = await IntegrationsService.getIntegrationStatusSummary();
      return data;
    });

  const isIntegrationActive = useMemo(() => {
    if (!integrationStatus || !automationType) return false;
    const integrationActiveKey =
      IntegrationActiveKeyBySourceIntegration[
        automationType.sourceIntegration!
      ];
    return (integrationStatus as Record<string, boolean>)[integrationActiveKey];
  }, [integrationStatus, automationType]);

  const navigate = useNavigate();
  const toast = useToast();

  const updateAutomation = useMutation(
    (updateAutomationDto: UpdateAutomationFromIntegrationDto) =>
      AutomationsService.updateAutomationFromIntegration(
        automationId!,
        updateAutomationDto,
      ),
    {
      onSuccess: () => {
        toast({
          title: 'Automação atualizada com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
        navigate(appPaths.automations.backgroundAutomations.index());
      },
    },
  );

  const [whatsappTemplateArgs, setWhatsappTemplateArgs] = useState<{
    [key: string]: string | undefined;
  }>({});

  const [emailTemplateArgs, setEmailTemplateArgs] = useState<{
    [key: string]: string | undefined;
  }>({});

  async function onSubmit(data: any) {
    const {
      name,
      isActive,
      messageTemplateId,
      emailTemplateId,
      dailyMessageLimitOnWhatsapp,
      flowId,
      action,
      dailyEmailLimit,
      data: automationData,
    } = data;
    await updateAutomation.mutateAsync({
      name,
      isActive,
      messageTemplateId,
      emailTemplateId,
      templateArgs: whatsappTemplateArgs,
      emailTemplateArgs: emailTemplateArgs,
      data: automationData,
      dailyMessageLimitOnWhatsapp,
      dailyEmailLimit,
      automationTypeId: automationTypeId!,
      isAutomationRepetitionAllowed: true,
      flowId,
      action,
    });
  }

  return (
    <LoadingScreen
      isLoading={
        isFetchingAutomationType ||
        isFetchingAutomation ||
        isFetchingIntegrationStatus
      }
    >
      <>
        {automationType && !isIntegrationActive && (
          <TopIntegrationInactiveErrorMessage
            sourceIntegration={automationType?.sourceIntegration!}
          />
        )}
        <GlassmorphisContainer show={!isIntegrationActive}>
          <AutomationByTypeForm
            onSubmit={onSubmit}
            whatsappTemplateArgs={whatsappTemplateArgs}
            setWhatsappTemplateArgs={setWhatsappTemplateArgs}
            emailTemplateArgs={emailTemplateArgs}
            setEmailTemplateArgs={setEmailTemplateArgs}
            automation={automation}
            automationType={automationType}
          />
        </GlassmorphisContainer>
      </>
    </LoadingScreen>
  );
};

export default EditBackgroundAutomationByTypePage;
