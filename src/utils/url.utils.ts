export function convertObjectToQueryString(
  object: Record<string, any> | undefined,
): string {
  if (!object) {
    return '';
  }

  return Object.keys(object)
    .filter(
      (key) =>
        object[key] !== undefined && object[key] !== null && object[key] !== '',
    )
    .map((key) => `${key}=${encodeURIComponent(object[key])}`)
    .join('&');
}

export function convertQueryStringToObject(queryString: string) {
  const params = new URLSearchParams(queryString);
  const resultObject: Record<string, any> = {};

  for (const [key, value] of params) {
    if (key && value) {
      resultObject[key] = value;
    }
  }

  return resultObject;
}

export function isValidUrl(urlVal: string): boolean {
  const urlPattern =
    /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})(\/[\w\-\.~!$&'\(\)*+,;=:@/?#%]*)*(\?([\w\-\.~!$&'\(\)*+,;=:@/?#%]*=[\w\-\.~!$&'\(\)*+,;=:@/?#%]*)&?)?$/;
  return urlPattern.test(urlVal);
}

export const UrlUtils = {
  convertObjectToQueryString: (object: any) =>
    convertObjectToQueryString(object),
  convertQueryStringToObject: (queryString: string) =>
    convertQueryStringToObject(queryString),
  isValidUrl: (urlVal: string) => isValidUrl(urlVal),
};
