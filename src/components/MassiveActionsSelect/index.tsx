import { Select } from '@chakra-ui/react';

interface MassiveActionsSelectProps {
  selectedCount: number;
  handleBulkAction: (action: string) => void;
}

export const MassiveActionsSelect = ({
  selectedCount,
  handleBulkAction,
}: MassiveActionsSelectProps) => {
  const bulkActionsSelect = (
    <Select
      placeholder="Ações em massa"
      size="xs"
      width="150px"
      isDisabled={selectedCount === 0}
      onChange={(e) => handleBulkAction(e.target.value)}
      value=""
    >
      <option value="finish">Finalizar selecionadas</option>
      <option value="markAsUnread">Marcar como não lidas</option>
    </Select>
  );

  return bulkActionsSelect;
};
