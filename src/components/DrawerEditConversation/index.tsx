import {
  Button,
  FormControl,
  FormLabel,
  Input,
  Select,
  Stack,
  Switch,
  Textarea,
  Tabs,
  TabPanel,
  TabPanels,
  useId,
  useToast,
  Box,
  Flex,
  Text,
  useDisclosure,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Badge,
  HStack,
} from '@chakra-ui/react';
import { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useMutation, useQuery, useQueryClient } from 'react-query';
import { apiRoutes } from '../../constants/api-routes';
import { ConversationCategoriesService } from '../../services/conversation-categories.service';
import {
  ConversationsService,
  UpdateConversationDto,
} from '../../services/conversations.service';
import { TagsService } from '../../services/tags.service';
import InputSelect from '../InputSelect';
import { DeleteCustomerModal } from '../../pages/CustomersPage/components/DeleteCustomerModal';
import { appPaths } from '../../constants/app-paths';
import { ConversationCategoriesUtils } from '../../utils/conversation-categories.utils';
import { UsersService } from '../../services/users.service';
import { User } from '../../types/Prisma';
import { CompanyDefinedFieldsService } from '../../services/company-defined-fields.service';
import { CompanyDefinedFieldTableEnum } from '../../types/CompanyDefinedField';
import { CompanyDefinedField } from '../../types/Prisma';
import { useSelector } from 'react-redux';
import { RootState } from '../../state/store';
import { dataTestIds } from '../../constants/data-testids';
import Tooltip from '../Tooltip';
import { getConversationById } from '../../state/inboxSlice';

interface DrawerEditConversationProps {
  conversationId: string;
  onClose: () => void;
}

const DrawerEditConversation = ({
  conversationId,
  onClose,
}: DrawerEditConversationProps) => {
  const toast = useToast();
  const formId = useId();
  const [tagOptions, setTagOptions] = useState<
    {
      value: string;
      label: string;
    }[]
  >([]);
  const { register, handleSubmit, setValue, control, getValues, reset } =
    useForm();
  const deleteCustomerModal = useDisclosure();
  const { daysSinceLastMessage } = useSelector(
    (state: RootState) => state.inbox,
  );

  const selectedConversation = useSelector((state: RootState) =>
    getConversationById(state, conversationId!),
  );

  const agentId = selectedConversation?.ticketAgentId;

  useQuery(
    apiRoutes.listTags(),
    async () => {
      const { data } = await TagsService.listTags();
      return data;
    },
    {
      onSuccess: (data) => {
        setTagOptions(
          data.map((tag) => ({
            value: tag.id,
            label: tag.name,
          })),
        );
      },
    },
  );

  const { data: conversation } = useQuery(
    apiRoutes.showConversation(conversationId!),
    async () => {
      const { data } = await ConversationsService.showConversation(
        conversationId!,
      );
      return data;
    },
    {
      enabled: !!conversationId,
    },
  );

  const { data: companyAgents = [] } = useQuery(
    apiRoutes.listCompanyAgents({ skipAiAgents: false }),
    async () => {
      const { data } = await UsersService.listCompanyAgents({
        skipAiAgents: false,
      });
      return data.filter((agent) => agent.isActive);
    },
  );

  const { data: conversationCategories } = useQuery(
    apiRoutes.listConversationCategories(),
    async () => {
      const { data } =
        await ConversationCategoriesService.listConversationCategories();
      return data;
    },
  );

  const { data: CompanyDefinedFields } = useQuery<CompanyDefinedField[]>(
    apiRoutes.listCompanyDefinedFields(CompanyDefinedFieldTableEnum.CUSTOMERS),
    async () => {
      const { data } =
        await CompanyDefinedFieldsService.listCompanyDefinedFields(
          CompanyDefinedFieldTableEnum.CUSTOMERS,
        );
      return data;
    },
  );

  const currentAgent = companyAgents.find((agent) => agent.id === agentId);
  const defaultAgent = companyAgents.find(
    (agent) => agent.id === conversation?.customer?.defaultAgentId,
  );

  const queryClient = useQueryClient();
  const updateConversation = useMutation(
    async (updateConversationDto: UpdateConversationDto) => {
      const { data } = await ConversationsService.updateConversation(
        updateConversationDto,
      );
      return data;
    },
    {
      onSuccess: (data) => {
        queryClient.invalidateQueries(
          apiRoutes.showConversation(conversationId!),
        );

        queryClient.invalidateQueries(
          apiRoutes.listConversationCategoriesDetailed(daysSinceLastMessage),
        );

        toast({
          title: 'Conversa salva com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
        onClose();
      },
    },
  );

  const createTagMutation = useMutation(
    async (tag: { name: string }) => {
      const { data } = await TagsService.createTag(tag);
      return data;
    },
    {
      onSuccess: (data) => {
        const newTagOption = {
          value: data.id,
          label: data.name,
        };
        setTagOptions((prev) => [...prev, newTagOption]);
        setValue('customer[tags]', [
          ...getValues('customer[tags]'),
          newTagOption,
        ]);
      },
    },
  );

  useEffect(() => {
    const setFormValues = () => {
      if (!conversation) return;
      const customerTags =
        conversation?.customer?.customerTags.map((customerTag) => ({
          value: customerTag.tag.id,
          label: customerTag.tag.name,
        })) || [];

      if (
        getValues('customer.customFields') === undefined ||
        Object.keys(getValues('customer.customFields')).length === 0
      ) {
        reset({
          customer: {
            customFields: conversation?.customer.customFields || {},
          },
        });
      }

      const formValues = {
        'conversation[recipientPhoneNumberId]':
          conversation?.recipientPhoneNumberId || '',
        'conversation[recipientName]': conversation?.recipientName || '',
        'conversation[categoryId]': conversation?.categoryId || '',
        'customer[tags]': customerTags,
        'customer[email]': conversation?.customer.email || '',
        'customer[isOptedOut]': conversation?.customer.isOptedOut || false,
        'customer[notes]': conversation?.customer.notes || '',
        'customer[defaultAgentId]':
          conversation?.customer?.defaultAgentId || null,
        'customer[customFields]': conversation?.customer.customFields || {},
      };

      Object.entries(formValues).forEach(([key, value]) => {
        setValue(key, value);
      });
    };

    setFormValues();
  }, [conversation, setValue]);

  async function onSubmit(data: any) {
    const { conversation, customer } = data;
    const { tags, ...updateCustomerData } = customer;

    await updateConversation.mutateAsync({
      conversationId: conversationId!,
      conversation,
      customer: {
        ...updateCustomerData,
        tagIds: tags.map((tag: any) => tag.value),
      },
    });
  }

  async function handleCreateTag(category: string) {
    await createTagMutation.mutate({ name: category });
  }

  const selectedUser: Record<string, boolean> = {
    [conversation?.customer?.id!]: true,
  };

  return (
    <Tabs>
      <TabPanels>
        <TabPanel>
          <Box
            display="flex"
            flexDirection="column"
            minHeight="100vh"
            justifyContent="space-between"
          >
            <form onSubmit={handleSubmit(onSubmit)} id={formId}>
              <Stack spacing={5} flex="1" overflowY="auto">
                <FormControl>
                  <FormLabel>Nome</FormLabel>
                  <Input
                    placeholder="Nome"
                    {...register('conversation[recipientName]')}
                  />
                </FormControl>

                <FormControl>
                  <FormLabel>Categoria da conversa</FormLabel>
                  <Select
                    placeholder="Selecione a categoria"
                    data-testid={
                      dataTestIds.inboxPage.drawerConversationSettings
                        .categorySelect
                    }
                    {...register('conversation[categoryId]')}
                  >
                    {ConversationCategoriesUtils.formatAndSortCategories(
                      conversationCategories ?? [],
                    )?.map((category) => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </Select>
                </FormControl>

                <FormControl>
                  <HStack justify="space-between" align="center">
                    <FormLabel mb={0}>Atendente atual da conversa</FormLabel>
                    <Tooltip
                      label="Este é o atendente que está responsável por esta conversa específica no momento. Ele foi atribuído automaticamente ou manualmente para este ticket."
                      children={undefined}
                    />
                  </HStack>
                  <Box
                    p={3}
                    border="1px solid"
                    borderColor="gray.200"
                    borderRadius="md"
                    bg="gray.50"
                  >
                    {currentAgent ? (
                      <HStack justify="space-between">
                        <Text fontWeight="medium">{currentAgent.name}</Text>
                        <Badge colorScheme="blue" size="sm">
                          Atendente Atual
                        </Badge>
                      </HStack>
                    ) : (
                      <Text color="gray.500" fontStyle="italic">
                        Nenhum atendente atribuído a esta conversa
                      </Text>
                    )}
                  </Box>
                  <Text fontSize="sm" color="gray.600" mt={1}>
                    Para reatribuir esta conversa, use a opção de atribuição na
                    caixa de entrada.
                  </Text>
                </FormControl>

                <FormControl>
                  <HStack justify="space-between" align="center">
                    <FormLabel mb={0}>Atendente padrão do cliente</FormLabel>
                    <Tooltip
                      label="Este atendente será automaticamente responsável por todas as novas conversas deste cliente. É uma configuração permanente do perfil do cliente."
                      children={undefined}
                    />
                  </HStack>
                  <Select
                    placeholder="Selecione um atendente padrão"
                    {...register('customer[defaultAgentId]')}
                  >
                    {companyAgents.map((agent: User) => (
                      <option key={agent.id} value={agent.id}>
                        {agent.name}
                      </option>
                    ))}
                  </Select>
                  <Text fontSize="sm" color="gray.600" mt={1}>
                    Quando este cliente iniciar uma nova conversa, ela será
                    automaticamente atribuída a este atendente.
                  </Text>
                  {defaultAgent && (
                    <Box
                      mt={2}
                      p={2}
                      bg="blue.50"
                      borderRadius="md"
                      border="1px solid"
                      borderColor="blue.200"
                    >
                      <HStack>
                        <Text fontSize="sm" color="blue.700">
                          <strong>Atual:</strong> {defaultAgent.name}
                        </Text>
                        <Badge colorScheme="blue" size="sm">
                          Padrão
                        </Badge>
                      </HStack>
                    </Box>
                  )}
                </FormControl>

                <FormControl>
                  <FormLabel>Tags</FormLabel>
                  <Controller
                    name="customer[tags]"
                    control={control}
                    render={({ field }) => (
                      <InputSelect
                        onCreateOption={handleCreateTag}
                        placeholder="Adicionar tags"
                        options={tagOptions}
                        isMulti
                        value={field.value}
                        onChange={field.onChange}
                      />
                    )}
                  />
                </FormControl>

                <FormControl>
                  <FormLabel>Anotações</FormLabel>
                  <Textarea
                    placeholder="Anotações"
                    {...register('customer[notes]')}
                  />
                </FormControl>

                <FormControl>
                  <FormLabel>Bloqueado?</FormLabel>
                  <Switch
                    {...register('customer[isOptedOut]')}
                    colorScheme="red"
                    defaultChecked={conversation?.customer?.isOptedOut ?? false}
                  />
                </FormControl>

                <FormControl>
                  <FormLabel>Telefone</FormLabel>
                  <Input
                    placeholder="Telefone"
                    disabled={true}
                    {...register('conversation[recipientPhoneNumberId]')}
                  />
                </FormControl>

                <FormControl>
                  <FormLabel>Email</FormLabel>
                  <Input placeholder="Email" {...register('customer[email]')} />
                </FormControl>

                <Accordion allowToggle>
                  <AccordionItem
                    borderWidth="1px"
                    borderRadius="md"
                    overflow="hidden"
                    mb={4}
                  >
                    <AccordionButton
                      bg="gray.50"
                      _hover={{ bg: 'gray.100' }}
                      px={4}
                      py={3}
                    >
                      <Box flex="1" textAlign="left" fontWeight="semibold">
                        Colunas customizadas
                      </Box>
                      <AccordionIcon />
                    </AccordionButton>

                    <AccordionPanel pb={4} px={4} bg="white">
                      <Stack spacing={4}>
                        {CompanyDefinedFields?.filter(
                          (field) => field.isActive,
                        ).map((field) => (
                          <FormControl key={field.id}>
                            <FormLabel fontWeight="medium">
                              {field.name}
                            </FormLabel>
                            <Controller
                              name="customer.customFields"
                              control={control}
                              render={({ field: controllerField }) => (
                                <Input
                                  type={
                                    field.dataType === 'number'
                                      ? 'number'
                                      : 'text'
                                  }
                                  value={
                                    controllerField.value?.[field.name] !==
                                    undefined
                                      ? controllerField.value[field.name]
                                      : ''
                                  }
                                  onChange={(e) => {
                                    const value =
                                      field.dataType === 'number'
                                        ? Number(e.target.value)
                                        : e.target.value;

                                    controllerField.onChange({
                                      ...controllerField.value,
                                      [field.name]: value,
                                    });
                                  }}
                                  bg="white"
                                  borderRadius="md"
                                />
                              )}
                            />
                          </FormControl>
                        ))}
                      </Stack>
                    </AccordionPanel>
                  </AccordionItem>
                </Accordion>

                <FormControl>
                  <Button
                    colorScheme="red"
                    variant="outline"
                    onClick={deleteCustomerModal.onOpen}
                    width="full"
                    mt={4}
                  >
                    Deletar Cliente
                  </Button>
                </FormControl>
              </Stack>
            </form>

            <Box
              padding="20px"
              bg="white"
              position="sticky"
              bottom="0"
              width="100%"
            >
              <Flex justify="space-between">
                <Button variant="outline" onClick={onClose} minWidth="120px">
                  Cancelar
                </Button>
                <Button
                  bg="black"
                  color="white"
                  form={formId}
                  type="submit"
                  isLoading={updateConversation.isLoading}
                  minWidth="120px"
                  data-testid={
                    dataTestIds.inboxPage.drawerConversationSettings.saveButton
                  }
                >
                  Salvar
                </Button>
              </Flex>
            </Box>
          </Box>
        </TabPanel>
      </TabPanels>
      <DeleteCustomerModal
        isOpen={deleteCustomerModal.isOpen}
        onClose={deleteCustomerModal.onClose}
        selectedUsers={selectedUser}
        redirectTo={appPaths.conversations()}
      />
    </Tabs>
  );
};

export default DrawerEditConversation;
