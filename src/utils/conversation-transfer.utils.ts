import type { MessageStatus } from '../types/Prisma';

function generateInternalTransferMessage(
  oldAgentName: string,
  agentName: string,
  conversationId: string,
  status: MessageStatus,
) {
  const message = {
    id: '',
    tempId: `internal-transfer-${Date.now()}`,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    text: `Usuário ${oldAgentName} transferiu a conversa para ${agentName}`,
    fromSystem: true,
    senderPhoneNumberId: '',
    recipientPhoneNumberId: '',
    conversationId: conversationId,
    status: status,
    whatsappMessageId: null,
    mediaUrl: null,
    mediaType: null,
    fileKey: null,
    messageTemplateId: null,
    whatsappCampaignId: null,
    firstReplyId: null,
    errorCode: null,
    errorMessage: null,
    automationId: null,
    uploadProgress: 0,
    flowNodeId: null,
    contextMessageId: 'InternalConversationTransfer',
    context: {},
    internal: true,
  };
  return message;
}

export { generateInternalTransferMessage };
