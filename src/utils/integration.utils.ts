import IntegrationActiveKeyBySourceIntegration from '../pages/AutomationsPage/BackgroundAutomationsPage/constants/integration-active-key-by-source-integration';
import { IntegrationDisplayNames } from '../pages/AutomationsPage/BackgroundAutomationsPage/constants/integration-display-names';
import { MANUAL_CONFIGURATION_INTEGRATIONS } from '../pages/AutomationsPage/BackgroundAutomationsPage/constants/manual-configuration-integrations';

const SourceIntegrationByActiveKey: Record<string, string> = Object.fromEntries(
  Object.entries(IntegrationActiveKeyBySourceIntegration).map(
    ([key, value]) => [value, key],
  ),
);

interface IntegrationStatus {
  [key: string]: boolean;
}

interface ProcessedIntegration {
  key: string;
  name: string;
  active: boolean;
  activeKey: string;
}

// Converte um objeto de status de integrações para um array de integrações ativas
function getActiveIntegrations(
  integrationStatus: IntegrationStatus,
): ProcessedIntegration[] {
  return Object.entries(integrationStatus)
    .filter(([_, isActive]) => isActive)
    .map(([activeKey, isActive]) => {
      const integrationKey = SourceIntegrationByActiveKey[activeKey];
      return {
        key: integrationKey,
        name: IntegrationDisplayNames[integrationKey] || integrationKey,
        active: isActive,
        activeKey,
      };
    })
    .filter((integration) => integration.key); // Remove integrações não mapeadas
}

// Converte um objeto de status de integrações para um array de todas as integrações (ativas e inativas)
function getAllIntegrations(
  integrationStatus: IntegrationStatus,
): ProcessedIntegration[] {
  return Object.entries(integrationStatus)
    .map(([activeKey, isActive]) => {
      const integrationKey = SourceIntegrationByActiveKey[activeKey];
      return {
        key: integrationKey,
        name: IntegrationDisplayNames[integrationKey] || integrationKey,
        active: isActive,
        activeKey,
      };
    })
    .filter((integration) => integration.key); // Remove integrações não mapeadas
}

// Obtém o nome amigável de uma integração pela sua chave técnica
function getIntegrationDisplayName(integrationKey: string): string {
  return IntegrationDisplayNames[integrationKey] || integrationKey;
}

// Obtém a chave técnica de uma integração pela sua chave de status
function getIntegrationKeyByActiveKey(activeKey: string): string | undefined {
  return SourceIntegrationByActiveKey[activeKey];
}

// Obtém a chave de status de uma integração pela sua chave técnica
function getActiveKeyByIntegrationKey(
  integrationKey: string,
): string | undefined {
  return IntegrationActiveKeyBySourceIntegration[integrationKey];
}

// Verifica se uma integração está ativa
function isIntegrationActive(
  integrationStatus: IntegrationStatus,
  integrationKey: string,
): boolean {
  const activeKey = getActiveKeyByIntegrationKey(integrationKey);
  return activeKey ? !!integrationStatus[activeKey] : false;
}

// Obtém a primeira integração ativa
function getFirstActiveIntegration(
  integrationStatus: IntegrationStatus,
): ProcessedIntegration | undefined {
  const activeIntegrations = getActiveIntegrations(integrationStatus);
  return activeIntegrations.length > 0 ? activeIntegrations[0] : undefined;
}

export {
  IntegrationActiveKeyBySourceIntegration,
  SourceIntegrationByActiveKey,
  type IntegrationStatus,
  type ProcessedIntegration,
};

function requiresManualConfiguration(sourceIntegration: string): boolean {
  return MANUAL_CONFIGURATION_INTEGRATIONS.has(sourceIntegration as any);
}

export const IntegrationUtils = {
  getActiveIntegrations,
  getAllIntegrations,
  getIntegrationDisplayName,
  getIntegrationKeyByActiveKey,
  getActiveKeyByIntegrationKey,
  isIntegrationActive,
  getFirstActiveIntegration,
  requiresManualConfiguration,
};
