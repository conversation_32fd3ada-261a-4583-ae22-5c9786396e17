import {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  Box,
  Text,
  Badge,
  Grid,
  Skeleton,
  SkeletonText,
  VStack,
  HStack,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Center,
} from '@chakra-ui/react';
import {
  FiPackage,
  FiTag,
  FiInfo,
  FiEye,
  FiSettings,
  FiLayers,
  FiCalendar,
} from 'react-icons/fi';
import { format } from 'date-fns';
import { useQuery } from 'react-query';
import { ProductsService } from '../../../../services/products.service';
import { IntegrationUtils } from '../../../../utils/integration.utils';
import { useMemo, useCallback } from 'react';
import React from 'react';
import { Product } from '../../../../types/Product';
import { scrollbarStyles } from '../../../../styles/scrollbar.styles';
import NAValue from './FallbackValue';
import VariantItem from './VariantItem';
import InfoItem from './InfoItem';

interface ProductDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  productId: string;
}

const ProductDetailsModal = ({
  isOpen,
  onClose,
  productId,
}: ProductDetailsModalProps) => {
  const { data: product, isLoading } = useQuery<Product>(
    ['product-details', productId],
    async () => {
      const { data } = await ProductsService.listProductDetails(productId);
      return data;
    },
    {
      enabled: isOpen && !!productId,
    },
  );

  const variants = useMemo(() => {
    if (!product?.variants) return [];
    return product.variants;
  }, [product?.variants]);

  const borderColor = 'gray.200';
  const cardBg = 'white';
  const sectionBg = 'gray.50';
  const textColor = 'gray.800';
  const mutedColor = 'gray.600';
  const theme = {
    borderColor,
    cardBg,
    sectionBg,
    textColor,
    mutedColor,
  };

  const formatDate = useCallback((dateString: string) => {
    if (!dateString) return null;
    return format(new Date(dateString), 'dd/MM/yyyy HH:mm');
  }, []);

  const getStatusColorScheme = useCallback((status: string): string => {
    const statusData: Record<string, string> = {
      active: 'green',
      inactive: 'red',
      out_of_stock: 'orange',
      draft: 'yellow',
    };
    return statusData[status] || 'gray';
  }, []);

  const handleExternalLink = useCallback((url: string, e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    window.open(url, '_blank', 'noopener,noreferrer');
  }, []);

  const renderVariants = () => {
    if (variants.length === 0) return null;

    return variants.map((variant, index) => (
      <VariantItem
        key={variant.id}
        variant={variant}
        index={index}
        onExternalLink={handleExternalLink}
        formatDate={formatDate}
        getStatusColorScheme={getStatusColorScheme}
        theme={theme}
      />
    ));
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="5xl" isCentered>
      <ModalOverlay backdropFilter="blur(4px)" bg="blackAlpha.300" />
      <ModalContent
        maxH="90vh"
        overflowY="auto"
        borderRadius="2xl"
        shadow="2xl"
        css={scrollbarStyles({ width: '0px', background: 'white' })}
      >
        <ModalHeader
          borderBottom="1px"
          borderColor={borderColor}
          bg={sectionBg}
          borderTopRadius="2xl"
          py={6}
        >
          {isLoading ? (
            <Skeleton height="32px" width="400px" />
          ) : (
            <HStack spacing={4}>
              <Box
                p={3}
                bg="blue.50"
                borderRadius="xl"
                border="1px"
                borderColor="blue.200"
              >
                <FiPackage size="24px" color="var(--chakra-colors-blue-500)" />
              </Box>
              <VStack align="start" spacing={1} flex={1}>
                <Text fontSize="xl" fontWeight="700" color={textColor}>
                  {product?.name || 'Detalhes do Produto'}
                </Text>
                <HStack spacing={4}>
                  <Text fontSize="sm" color={mutedColor} fontFamily="mono">
                    ID: {product?.sourceId}
                  </Text>
                  {product?.status && (
                    <Badge
                      colorScheme={getStatusColorScheme(product.status)}
                      fontSize="xs"
                      px={2}
                      py={1}
                      borderRadius="md"
                    >
                      {product.status}
                    </Badge>
                  )}
                </HStack>
              </VStack>
            </HStack>
          )}
        </ModalHeader>
        <ModalCloseButton
          size="lg"
          borderRadius="full"
          bg="gray.100"
          _hover={{ bg: 'gray.200' }}
        />

        <ModalBody py={8}>
          {isLoading ? (
            <VStack spacing={8} align="stretch">
              <Skeleton height="200px" borderRadius="xl" />
              <SkeletonText mt="4" noOfLines={6} spacing="4" />
              <Skeleton height="300px" borderRadius="xl" />
            </VStack>
          ) : product ? (
            <Accordion defaultIndex={[0]}>
              <AccordionItem
                border="1px"
                borderColor={borderColor}
                borderRadius="xl"
                mb={4}
              >
                <AccordionButton
                  p={6}
                  _hover={{ bg: sectionBg }}
                  borderRadius="xl"
                  transition="all 0.2s"
                >
                  <HStack spacing={4} flex={1} textAlign="left">
                    <Box
                      p={2}
                      bg="green.50"
                      borderRadius="lg"
                      border="1px"
                      borderColor="green.200"
                    >
                      <FiInfo
                        size="20px"
                        color="var(--chakra-colors-green-500)"
                      />
                    </Box>
                    <VStack align="start" spacing={1}>
                      <Text fontSize="lg" fontWeight="700" color={textColor}>
                        Informações Gerais
                      </Text>
                      <Text fontSize="sm" color={mutedColor}>
                        Dados principais do produto
                      </Text>
                    </VStack>
                  </HStack>
                  <AccordionIcon />
                </AccordionButton>
                <AccordionPanel p={6} bg={sectionBg}>
                  <Grid
                    templateColumns="repeat(auto-fit, minmax(250px, 1fr))"
                    gap={6}
                  >
                    <InfoItem
                      label="Nome do Produto"
                      value={
                        product.name || (
                          <NAValue tooltipText="Nome do produto não informado" />
                        )
                      }
                      icon={FiPackage}
                      theme={theme}
                    />
                    <InfoItem
                      label="Título"
                      value={
                        product.title || (
                          <NAValue tooltipText="Título do produto não informado" />
                        )
                      }
                      icon={FiTag}
                      theme={theme}
                    />
                    <InfoItem
                      label="Fonte de Integração"
                      value={
                        product.source ? (
                          IntegrationUtils.getIntegrationDisplayName(
                            product.source,
                          )
                        ) : (
                          <NAValue tooltipText="Fonte de integração não identificada" />
                        )
                      }
                      theme={theme}
                    />
                    <InfoItem
                      label="ID da Fonte"
                      value={
                        product.sourceId || (
                          <NAValue tooltipText="ID da fonte não disponível" />
                        )
                      }
                      isCode={!!product.sourceId}
                      theme={theme}
                    />
                    <InfoItem
                      label="Total de Variantes"
                      value={`${variants.length || 0} variantes`}
                      icon={FiLayers}
                      theme={theme}
                    />
                    <InfoItem
                      label="Data de Criação"
                      value={
                        product.createdAt ? (
                          formatDate(product.createdAt)
                        ) : (
                          <NAValue tooltipText="Data de criação não disponível" />
                        )
                      }
                      icon={FiCalendar}
                      theme={theme}
                    />
                  </Grid>

                  {product.description && (
                    <Box
                      mt={6}
                      pt={6}
                      borderTop="1px"
                      borderColor={borderColor}
                    >
                      <InfoItem
                        label="Descrição do Produto"
                        value={
                          <Box
                            dangerouslySetInnerHTML={{
                              __html: product.description,
                            }}
                            sx={{
                              '& p': { mb: 2 },
                              '& strong': { fontWeight: 'bold' },
                              '& div': { mb: 2 },
                            }}
                          />
                        }
                        theme={theme}
                      />
                    </Box>
                  )}
                </AccordionPanel>
              </AccordionItem>

              {variants.length > 0 && (
                <AccordionItem
                  border="1px"
                  borderColor={borderColor}
                  borderRadius="xl"
                  mb={4}
                >
                  <AccordionButton
                    p={6}
                    _hover={{ bg: sectionBg }}
                    borderRadius="xl"
                    transition="all 0.2s"
                  >
                    <HStack spacing={4} flex={1} textAlign="left">
                      <Box
                        p={2}
                        bg="purple.50"
                        borderRadius="lg"
                        border="1px"
                        borderColor="purple.200"
                      >
                        <FiEye
                          size="20px"
                          color="var(--chakra-colors-purple-500)"
                        />
                      </Box>
                      <VStack align="start" spacing={1}>
                        <Text fontSize="lg" fontWeight="700" color={textColor}>
                          Variantes do Produto
                        </Text>
                        <Text fontSize="sm" color={mutedColor}>
                          {variants.length || 0} variantes encontradas
                        </Text>
                      </VStack>
                      <Badge
                        colorScheme="purple"
                        fontSize="sm"
                        px={3}
                        py={1}
                        borderRadius="full"
                      >
                        {variants.length || 0}
                      </Badge>
                    </HStack>
                    <AccordionIcon />
                  </AccordionButton>
                  <AccordionPanel p={6} bg={sectionBg}>
                    {variants.length > 0 ? (
                      <Box>
                        <Accordion allowMultiple>{renderVariants()}</Accordion>
                        <Box
                          mt={4}
                          p={3}
                          bg={cardBg}
                          borderRadius="md"
                          border="1px"
                          borderColor={borderColor}
                        >
                          <Text
                            fontSize="xs"
                            color={mutedColor}
                            textAlign="center"
                          >
                            Total de {variants.length} variantes carregadas
                          </Text>
                        </Box>
                      </Box>
                    ) : (
                      <Center py={12}>
                        <VStack spacing={4}>
                          <Box
                            p={4}
                            bg="gray.100"
                            borderRadius="full"
                            border="1px"
                            borderColor="gray.200"
                          >
                            <FiPackage
                              size="32px"
                              color="var(--chakra-colors-gray-400)"
                            />
                          </Box>
                          <Text
                            color={mutedColor}
                            fontSize="md"
                            fontWeight="500"
                          >
                            Nenhuma variante encontrada para este produto
                          </Text>
                        </VStack>
                      </Center>
                    )}
                  </AccordionPanel>
                </AccordionItem>
              )}

              {product.metadata && Object.keys(product.metadata).length > 0 && (
                <AccordionItem
                  border="1px"
                  borderColor={borderColor}
                  borderRadius="xl"
                >
                  <AccordionButton
                    p={6}
                    _hover={{ bg: sectionBg }}
                    borderRadius="xl"
                    transition="all 0.2s"
                  >
                    <HStack spacing={4} flex={1} textAlign="left">
                      <Box
                        p={2}
                        bg="orange.50"
                        borderRadius="lg"
                        border="1px"
                        borderColor="orange.200"
                      >
                        <FiSettings
                          size="20px"
                          color="var(--chakra-colors-orange-500)"
                        />
                      </Box>
                      <VStack align="start" spacing={1}>
                        <Text fontSize="lg" fontWeight="700" color={textColor}>
                          Metadados do Produto
                        </Text>
                        <Text fontSize="sm" color={mutedColor}>
                          Dados técnicos e configurações
                        </Text>
                      </VStack>
                    </HStack>
                    <AccordionIcon />
                  </AccordionButton>
                  <AccordionPanel p={6} bg={sectionBg}>
                    <Box
                      bg="gray.900"
                      color="green.300"
                      p={6}
                      borderRadius="xl"
                      fontSize="sm"
                      fontFamily="mono"
                      overflowX="auto"
                      maxH="400px"
                      overflowY="auto"
                      border="2px"
                      borderColor="gray.700"
                    >
                      <pre>{JSON.stringify(product.metadata, null, 2)}</pre>
                    </Box>
                  </AccordionPanel>
                </AccordionItem>
              )}
            </Accordion>
          ) : (
            <Center py={16}>
              <VStack spacing={4}>
                <Box
                  p={4}
                  bg="red.50"
                  borderRadius="full"
                  border="1px"
                  borderColor="red.200"
                >
                  <FiPackage size="32px" color="var(--chakra-colors-red-400)" />
                </Box>
                <Text color={mutedColor} fontSize="lg" fontWeight="500">
                  Produto não encontrado
                </Text>
              </VStack>
            </Center>
          )}
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

export default ProductDetailsModal;
