import {
  AlertDialog,
  AlertDialogBody,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogContent,
  AlertDialogOverlay,
  Button,
  useToast,
  Flex,
} from '@chakra-ui/react';
import { useRef } from 'react';
import { useMutation, useQueryClient } from 'react-query';
import { ProductsService } from '../../../../services/products.service';
import { colors } from '../../../../constants/colors';
import { apiRoutes } from '../../../../constants/api-routes';

interface ModalDeleteProductProps {
  isOpen: boolean;
  onClose: () => void;
  productId: string;
}

const ModalDeleteProduct = ({
  isOpen,
  onClose,
  productId,
}: ModalDeleteProductProps) => {
  const toast = useToast();
  const queryClient = useQueryClient();
  const cancelRef = useRef<HTMLButtonElement>(null);

  const deleteProductFromMetaMutation = useMutation(
    (productId: string) => ProductsService.deleteProductFromMeta(productId),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(apiRoutes.listProducts());
        toast({
          title: 'Produto removido',
          description: 'Produto removido do Catálogo com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
        onClose();
      },
    },
  );

  const confirmDelete = () => {
    deleteProductFromMetaMutation.mutate(productId);
  };

  return (
    <AlertDialog
      isOpen={isOpen}
      leastDestructiveRef={cancelRef}
      onClose={onClose}
      isCentered
    >
      <AlertDialogOverlay>
        <AlertDialogContent>
          <AlertDialogHeader fontSize="lg" fontWeight="bold">
            Confirmar Remoção
          </AlertDialogHeader>

          <AlertDialogBody>
            Tem certeza que deseja remover este produto da Meta? Esta ação não
            pode ser desfeita.
          </AlertDialogBody>

          <AlertDialogFooter>
            <Flex width="100%" justify="space-between" gap={4}>
              <Button ref={cancelRef} onClick={onClose} variant="outline">
                Cancelar
              </Button>
              <Button
                variant="solid"
                bgColor={colors.red}
                color={colors.white}
                _hover={{ bgColor: colors.red }}
                onClick={confirmDelete}
                ml={3}
                isLoading={deleteProductFromMetaMutation.isLoading}
              >
                Remover
              </Button>
            </Flex>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialogOverlay>
    </AlertDialog>
  );
};

export default ModalDeleteProduct;
