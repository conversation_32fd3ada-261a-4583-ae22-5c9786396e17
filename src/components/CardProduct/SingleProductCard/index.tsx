import { MessageProductCard } from '../../../types/Message';
import { MoneyUtils } from '../../../utils/money.utils';
import ButtonWhatsapp from '../../ButtonWhatsapp';
import { Image, Text } from '@chakra-ui/react';

interface SingleProductCardProps {
  card: MessageProductCard;
  onOpen: () => void;
}

const SingleProductCard = ({ card, onOpen }: SingleProductCardProps) => {
  const firstProduct = card.messageProductCardItems[0];
  return (
    <>
      {firstProduct.productVariant.imageUrl && (
        <Image
          src={firstProduct.productVariant.imageUrl}
          alt={firstProduct.name}
          width="100%"
          height="160px"
          objectFit="cover"
          borderRadius="6px"
          mb={3}
        />
      )}

      <Text fontSize="14px" fontWeight="400" color="#000" mb={1} noOfLines={2}>
        {firstProduct.name}
      </Text>
      <Text fontSize="12px" fontWeight="600" color="#000" mb={2}>
        {MoneyUtils.formatCurrency(firstProduct.priceCents)}
      </Text>
      <Text
        fontWeight={500}
        fontSize="13px"
        color="#54656f"
        noOfLines={2}
        mb={1}
      >
        {card.bodyText}
      </Text>
      <Text fontSize="12px" color="#54656f" mb={2}>
        {card.footerText}
      </Text>

      <ButtonWhatsapp onClick={onOpen}>Ver</ButtonWhatsapp>
    </>
  );
};

export default SingleProductCard;
