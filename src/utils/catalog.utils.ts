function getStepTitle(step: string): string {
  const steps = {
    type: 'Tipo de Catálogo',
    content: '<PERSON><PERSON><PERSON><PERSON> da Mensagem',
    products: 'Selecionar Produtos',
    review: 'Revisar e Enviar',
  };
  return steps[step as keyof typeof steps];
}

function getStepProgress(step: string): number {
  const steps = ['type', 'content', 'products', 'review'];
  return ((steps.indexOf(step) + 1) / steps.length) * 100;
}

export const CatalogUtils = {
  getStepTitle,
  getStepProgress,
};
