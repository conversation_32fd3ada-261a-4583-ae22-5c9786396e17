import { Box, Button, Text } from '@chakra-ui/react';
import { useNavigate } from 'react-router-dom';
import { appPaths } from '../../../../../constants/app-paths';
import { colors } from '../../../../../constants/colors';
import { SourceIntegration } from '../../../../../types/Prisma';
import { SourceIntegrationLabelsRoutesAndImages } from '../../../../../types/source-integration-labels-routes-and-images';
import { SettingsIcon } from '@chakra-ui/icons';

const TopIntegrationInactiveErrorMessage = ({
  sourceIntegration,
  requiresManualConfiguration,
}: {
  sourceIntegration: SourceIntegration;
  requiresManualConfiguration?: boolean;
}) => {
  const navigate = useNavigate();
  function handleOpenIntegrationPage() {
    const routeKey =
      SourceIntegrationLabelsRoutesAndImages[sourceIntegration!].routeKey;
    if (!routeKey) {
      return;
    }
    const pathToOpen = appPaths.settings.integrationSettings[routeKey]?.();
    navigate(pathToOpen);
  }

  const text = requiresManualConfiguration
    ? `Para configurar o ${SourceIntegrationLabelsRoutesAndImages[sourceIntegration].label}, entre em contato com o time de
    suporte`
    : `Antes de criar uma automação, você precisa ativar a integração ${SourceIntegrationLabelsRoutesAndImages[sourceIntegration].label}`;

  const showConfigureButton = !requiresManualConfiguration;

  return (
    <Box
      backgroundColor={requiresManualConfiguration ? '#c2e0ff' : '#ffe6e6'}
      margin="10px"
      borderRadius="5px"
      textAlign="center"
      padding="10px"
      sx={{ '& > *': { margin: '4px' } }}
    >
      <Text fontSize="22px" textColor="#333333" fontWeight="bold">
        {requiresManualConfiguration && <SettingsIcon marginRight="8px" />}
        {text}
      </Text>
      {showConfigureButton && (
        <Button
          width="auto"
          color="white"
          bgColor={colors.primary}
          onClick={handleOpenIntegrationPage}
        >
          Configurar Automação
        </Button>
      )}
    </Box>
  );
};

export default TopIntegrationInactiveErrorMessage;
