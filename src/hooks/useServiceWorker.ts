import { useEffect } from 'react';
import { registerSW } from 'virtual:pwa-register';

const SW_UPDATE_TIME = 60 * 1000 * 10; // 10 Minutes

export const useServiceWorker = () => {
  useEffect(() => {
    const updateFn = registerSW({
      immediate: true,
      onRegisteredSW(swUrl, registration) {
        console.info('✅ Service Worker registrado:', swUrl);

        if (registration) {
          registration.addEventListener('updatefound', () => {
            console.info('🔃 Verificando nova versão da Revi app.');
          });
          setInterval(() => {
            console.info('🔃 Procurando por atualizações da Revi app');
            registration.update().then(() => {});
          }, SW_UPDATE_TIME);
        }
      },
      onNeedRefresh() {
        console.info('✅ Nova versão da Revi app instalada.');
        updateFn(false);
      },
      onOfflineReady() {
        console.info('✅ Revi app pronto para uso offline');
      },
    });
  }, []);
};
