import { apiRoutes } from '../constants/api-routes';
import { request } from '../constants/request';

const syncShopifyOrders = async () => {
  return request.post(apiRoutes.syncShopifyOrders());
};

const syncShopifyOrdersByIntegration = async (integrationId: string) => {
  return request.post(apiRoutes.syncShopifyOrdersByIntegration(integrationId));
};

export const ShopifyService = {
  syncShopifyOrders,
  syncShopifyOrdersByIntegration,
};
