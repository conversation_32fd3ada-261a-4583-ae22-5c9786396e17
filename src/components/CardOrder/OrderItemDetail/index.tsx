import { Flex, Box, Text, Image } from '@chakra-ui/react';
import { WhatsappOrderItem } from '../../../types/Message';
import { MoneyUtils } from '../../../utils/money.utils';

interface OrderItemDetailProps {
  item: WhatsappOrderItem;
}

const OrderItemDetail = ({ item }: OrderItemDetailProps) => {
  const totalItemPrice = (item.priceCents || 0) * (item.quantity || 1);

  return (
    <Flex p={3} alignItems="center" gap={3}>
      {item.productVariant?.imageUrl && (
        <Image
          src={item.productVariant.imageUrl}
          alt={item.productVariant?.name || 'Produto'}
          width="50px"
          height="50px"
          objectFit="cover"
          borderRadius="6px"
          flexShrink={0}
        />
      )}

      <Box flex={1}>
        <Text fontSize="14px" fontWeight="500" color="#000" mb={1}>
          {item.productVariant?.name || item.productVariant?.title || 'Produto'}
        </Text>

        <Flex justifyContent="space-between" alignItems="center" mb={1}>
          <Text fontSize="12px" color="#54656f">
            Qtd: {item.quantity || 1} ×{' '}
            {MoneyUtils.formatCurrency(item.priceCents)}
          </Text>
          <Text fontSize="14px" fontWeight="600" color="#00a884">
            {MoneyUtils.formatCurrency(totalItemPrice)}
          </Text>
        </Flex>

        {item.productVariant?.sku && (
          <Text fontSize="11px" color="#54656f" mb={1}>
            SKU: {item.productVariant.sku}
          </Text>
        )}

        {item.productVariant?.description && (
          <Text
            fontSize="11px"
            color="#54656f"
            noOfLines={2}
            dangerouslySetInnerHTML={{
              __html:
                item.productVariant.description
                  .replace(/<[^>]*>/g, '')
                  .substring(0, 80) + '...',
            }}
          />
        )}
      </Box>
    </Flex>
  );
};

export default OrderItemDetail;
