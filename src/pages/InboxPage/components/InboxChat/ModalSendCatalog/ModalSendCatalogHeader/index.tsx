import React from 'react';
import {
  ModalHeader as ChakraModalHeader,
  VStack,
  Flex,
  Text,
  Badge,
  Progress,
} from '@chakra-ui/react';
import { CatalogUtils } from '../../../../../../utils/catalog.utils';

interface ModalSendCatalogHeaderProps {
  step: string;
}

export const ModalSendCatalogHeader = ({
  step,
}: ModalSendCatalogHeaderProps) => {
  const stepIndex = ['type', 'content', 'products', 'review'].indexOf(step) + 1;

  return (
    <ChakraModalHeader py={4} px={6} position="relative">
      <VStack spacing={3} align="stretch">
        <Flex justify="space-between" align="center">
          <Text fontSize="xl" fontWeight="bold">
            {CatalogUtils.getStepTitle(step)}
          </Text>
          <Badge
            colorScheme="blue"
            fontSize="sm"
            px={3}
            py={1}
            borderRadius="full"
          >
            Etapa {stepIndex} de 4
          </Badge>
        </Flex>
        <Progress
          value={CatalogUtils.getStepProgress(step)}
          colorScheme="blue"
          borderRadius="full"
        />
      </VStack>
    </ChakraModalHeader>
  );
};
