import {
  <PERSON><PERSON>,
  Drawer,
  Drawer<PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DrawerFooter,
  Drawer<PERSON>eader,
  Drawer<PERSON><PERSON><PERSON>,
  useId,
} from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import { Edge, Node } from 'reactflow';
import TriggerNodeEditor from './TriggerNodeEditor';
import MoveConversationToCategoryEditor from './MoveConversationToCategoryNodeEditor';
import SendWhatsappMessageNodeEditor from './SendWhatsappMessageNodeEditor';
import SendWhatsappMediaNodeEditor from './SendWhatsappMediaNodeEditor';
import AddTagToCustomerNodeEditor from './AddTagToCustomerNodeEditor';
import EndWhatsappConversationNodeEditor from './EndWhatsappConversationNodeEditor';
import SaveCustomerResponseNodeEditor from './SaveCustomerResponseNodeEditor';
import Conditions<PERSON>heckNodeEditor from './ConditionsChe<PERSON>';
import TimeD<PERSON>yNodeEditor from './TimeDelayNodeEditor';
import SendWhatsappMessageTemplateNodeEditor from './SendWhatsappMessageTemplateNodeEditor';
import HttpRequestNodeEditor from './HttpRequestNodeEditor';
import SendEmailTemplateNodeEditor from './SendEmailTemplateNodeEditor';
import AssignConversationTicketToAgentNodeEditor from './AssignConversationTicketToAgentNodeEditor';

export interface DrawerNodeEditorProps {
  isOpen: boolean;
  onClose: () => void;
  onSaveNode: (node: Node) => void;
  selectedNode: Node;
  onDeleteNode: (nodeId: string) => void;
  flowType: string;
  nodes: Node<any, string | undefined>[];
  edges: Edge<any>[];
}

const DrawerNodeEditor = ({
  isOpen,
  onClose,
  onSaveNode,
  selectedNode,
  onDeleteNode,
  flowType,
  nodes,
  edges,
}: DrawerNodeEditorProps) => {
  const { t } = useTranslation();
  const formId = useId();

  return (
    <Drawer isOpen={isOpen} placement="left" onClose={onClose} size={'md'}>
      <DrawerOverlay />
      <DrawerContent>
        <DrawerCloseButton />
        <DrawerHeader>
          {t(`enums.FlowNodeType.${selectedNode?.type}`) || null}
        </DrawerHeader>
        {selectedNode?.type === 'send_whatsapp_message' ? (
          <SendWhatsappMessageNodeEditor
            nodeId={selectedNode?.id}
            data={selectedNode?.data}
            onSaveNode={onSaveNode}
            formId={formId}
            nodes={nodes}
            edges={edges}
          />
        ) : selectedNode?.type === 'trigger' ? (
          <TriggerNodeEditor
            data={selectedNode?.data}
            onSaveNode={onSaveNode}
            formId={formId}
          />
        ) : selectedNode.type === 'move_conversation_to_category' ? (
          <MoveConversationToCategoryEditor
            data={selectedNode.data}
            onSaveNode={onSaveNode}
            formId={formId}
          />
        ) : selectedNode.type === 'send_whatsapp_media' ? (
          <SendWhatsappMediaNodeEditor
            formId={formId}
            data={selectedNode.data}
            onSaveNode={onSaveNode}
          />
        ) : selectedNode.type === 'send_whatsapp_message_template' ? (
          <SendWhatsappMessageTemplateNodeEditor
            formId={formId}
            data={selectedNode.data}
            onSaveNode={onSaveNode}
            flowType={flowType}
          />
        ) : selectedNode.type === 'send_email_template' ? (
          <SendEmailTemplateNodeEditor
            formId={formId}
            data={selectedNode.data}
            onSaveNode={onSaveNode}
          />
        ) : selectedNode.type === 'add_tag_to_customer' ? (
          <AddTagToCustomerNodeEditor
            formId={formId}
            data={selectedNode.data}
            onSaveNode={onSaveNode}
          />
        ) : selectedNode.type === 'end_whatsapp_conversation' ? (
          <EndWhatsappConversationNodeEditor
            formId={formId}
            onSaveNode={onSaveNode}
          />
        ) : selectedNode.type === 'save_customer_response' ? (
          <SaveCustomerResponseNodeEditor
            formId={formId}
            data={selectedNode.data}
            onSaveNode={onSaveNode}
          />
        ) : selectedNode.type === 'conditions_check' ? (
          <ConditionsCheckNodeEditor
            formId={formId}
            data={selectedNode.data}
            onSaveNode={onSaveNode}
            flowType={flowType}
            key={selectedNode.type}
          />
        ) : selectedNode.type === 'http_request' ? (
          <HttpRequestNodeEditor
            formId={formId}
            data={selectedNode.data}
            onSaveNode={onSaveNode}
          />
        ) : selectedNode.type === 'time_delay' ? (
          <TimeDelayNodeEditor
            formId={formId}
            data={selectedNode.data}
            onSaveNode={onSaveNode}
          />
        ) : selectedNode.type === 'assign_conversation_ticket_to_agent' ? (
          <AssignConversationTicketToAgentNodeEditor
            formId={formId}
            data={selectedNode.data}
            onSaveNode={onSaveNode}
          />
        ) : null}
        <DrawerFooter
          display="flex"
          justifyContent={
            selectedNode.type === 'trigger' ? 'flex-end' : 'space-between'
          }
        >
          {selectedNode.type !== 'trigger' && (
            <Button
              variant="outline"
              type="button"
              onClick={() => onDeleteNode(selectedNode?.id)}
            >
              Excluir
            </Button>
          )}
          <Button variant="primary" type="submit" form={formId}>
            Salvar
          </Button>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
};

export default DrawerNodeEditor;
