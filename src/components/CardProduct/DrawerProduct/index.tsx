import {
  <PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>er<PERSON><PERSON>,
  DrawerCloseButton,
  Box,
  Text,
  VStack,
} from '@chakra-ui/react';
import { MessageProductCard } from '../../../types/Message';
import ListProductItem from '../ListProductItem';

interface DrawerProductProps {
  isOpen: boolean;
  onClose: () => void;
  card: MessageProductCard;
}

const DrawerProduct = ({ isOpen, onClose, card }: DrawerProductProps) => {
  const productsBySection = card.messageProductCardItems.reduce<
    Record<string, typeof card.messageProductCardItems>
  >((acc, product) => {
    const section = product.section || 'Outros';
    if (!acc[section]) acc[section] = [];
    acc[section].push(product);
    return acc;
  }, {});

  return (
    <Drawer isOpen={isOpen} placement="right" onClose={onClose} size="sm">
      <DrawerOverlay />
      <DrawerContent>
        <DrawerCloseButton />
        <DrawerHeader>
          {card.messageProductCardItems.length === 1
            ? card.messageProductCardItems[0].name
            : `Produtos (${card.messageProductCardItems.length} ${card.messageProductCardItems.length === 1 ? 'item' : 'itens'})`}
        </DrawerHeader>

        <DrawerBody p={3} overflowY="auto">
          {Object.entries(productsBySection).map(([section, products]) => (
            <Box key={section} mb={4}>
              <Text fontWeight="bold" mb={2} fontSize="14px" color="#333">
                {section}
              </Text>
              <VStack spacing={3} align="stretch">
                {products.map((product) => (
                  <ListProductItem key={product.id} product={product} />
                ))}
              </VStack>
            </Box>
          ))}
        </DrawerBody>
      </DrawerContent>
    </Drawer>
  );
};

export default DrawerProduct;
