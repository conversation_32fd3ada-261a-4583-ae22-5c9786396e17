import { init } from 'mixpanel-browser';

/**
 * Model User
 *
 */
export type User = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  name: string;
  email: string;
  password: string;
  socketId: string | null;
  companyId: string;
  roleId?: string;
  isActive: boolean;
  canViewAllConversations: boolean;
  isAgent: boolean;
};

/**
 * Model Company
 *
 */
export type Company = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  name: string;
  phoneNumber: string | null;
  phoneNumberId: string;
  whatsappBusinessId: string | null;
  whatsappAccessToken: string | null;
  firstContactMessage: string | null;
  isAutomaticSortingActive: boolean;
  shouldIncludeAttendantNameInMessages: boolean;
  gupshupAppName: string | null;
  gupshupAppId: string | null;
  dailyMessageLimitOnWhatsapp: number;
  monthlyMessageLimitOnWhatsapp: number;
  dailyEmailLimit: number;
  monthlyEmailLimit: number;
  monthlyMessageLimitOnSms: number;
  vtexAppKey: string | null;
  vtexAppToken: string | null;
  vtexAccountName: string | null;
  isVtexActive: boolean;
  shopifyShopName: string | null;
  shopifySession: Prisma.JsonValue | null;
  shopifyApiKey: string | null;
  shopifyApiSecretKey: string | null;
  shopifyAdminAccessToken: string | null;
  isShopifyActive: boolean;
  visualECommerceApiKey: string | null;
  visualECommerceStoreDomain: string | null;
  isVisualECommerceActive: boolean;
  lojaIntegradaApiKey: string | null;
  isLojaIntegradaActive: boolean;
  costPerMessage: number;
  productDescription: string | null;
  valueProposition: string | null;
  isActive: boolean;
  afterHoursMessage: string | null;
  cnpj: string | null;
  razaoSocial: string | null;
};

/**
 * Model ApiKey
 *
 */
export type ApiKey = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  key: string;
  companyId: string;
};

/**
 * Model AutomaticSortingOption
 *
 */
export type AutomaticSortingOption = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  firstMessage: string | null;
  fileId: string | null;
  companyId: string;
  isActive: boolean;
  conversationCategoryId: string;
  pos: number;
};

/**
 * Model AutomaticSortingOptionMessage
 *
 */
export type AutomaticSortingOptionMessage = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  text: string | null;
  automaticSortingOptionId: string;
};

/**
 * Model AutomaticReply
 *
 */
export type AutomaticReply = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  companyId: string;
  keyword: string;
  message: string;
  condition: AutomaticReplyCondition;
  conversationCategoryId: string | null;
  fileId: string | null;
  conversationTicketStatus: ConversationTicketStatus;
};

/**
 * Model Automation
 *
 */
export type Automation = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  companyId: string;
  name: string;
  isActive: boolean;
  data: Prisma.JsonValue | null;
  messageTemplateId: string | null;
  emailTemplateId: string | null;
  automationTypeId: string;
  filterId: string | null;
  cronExpression: string | null;
  dailyMessageLimitOnWhatsapp: number;
  dailyEmailLimit: number;
  scheduledJobId: string | null;
  nextExecutionAt: Date | null;
  templateArgs: Prisma.JsonValue | null;
  emailTemplateArgs: Prisma.JsonValue | null;
  isAutomationRepetitionAllowed: boolean;
  minDaysSinceLastCampaign: number;
  automationType: { slug: AutomationTypeSlug };
  flowId: string | null;
  action: AutomationAction;
};

export const AutomationAction: {
  sendMessageTemplate: 'send_message_template';
  triggerFlow: 'trigger_flow';
};

export type AutomationAction =
  (typeof AutomationAction)[keyof typeof AutomationAction];

/**
 * Model AutomationType
 *
 */
export type AutomationType = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  name: string;
  slug: AutomationTypeSlug;
  sourceIntegration?: SourceIntegration;
};

/**
 * Model Conversation
 *
 */
export type Conversation = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  recipientPhoneNumberId: string;
  recipientName: string;
  currentFlowNodeId: string | null;
  customerId: string;
  categoryId: string | null;
  companyId: string;
};

/**
 * Model Customer
 *
 */
export type Customer = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  sourceId: string | null;
  sourceCustomerId: string | null;
  sourceUserId: string | null;
  sourceCreatedAt: Date | null;
  sourceUpdatedAt: Date | null;
  phoneNumberId: string;
  name: string;
  email: string | null;
  tags: string | null;
  isOptedIn: boolean;
  isOptedOut: boolean;
  isOptedInToNewsletter: boolean;
  companyId: string;
  customFields: Prisma.JsonValue | null;
  source: CustomerSource;
  birthDate: Date | null;
  state: string | null;
  city: string | null;
  isDeleted: boolean;
  notes: string | null;
  defaultAgentId: string | null;
};

/**
 * Model CashbackConfig
 */
export type CashbackConfig = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  companyId: string;
  discountValue: number;
  discountType: DiscountType;
  daysToExpire: number;
  cumulative: boolean;
  minOrderValue: number;
  maxOrderValue: number;
  integration: SourceIntegration;
  creationMessageTemplateId: string;
  reminderMessageTemplateId: string;
  lastDayMessageTemplateId: string;
  reminderDays: number;
  isReminderEnabled: boolean;
  statusTrigger: string;
  dailyMessageLimitOnWhatsapp: number;
  minValueToUseCoupon: number;
  isActive: boolean;
  coupons?: Coupon[];
};

/**
 * Model Coupon
 */
export type Coupon = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  sourceId: string | null;
  companyId: string;
  code: string;
  discountValue: number;
  discountType: DiscountType;
  usageLimit: number | null;
  usagePerCustomer: number | null;
  startAt: Date | null;
  endsAt: Date | null;
  type: CouponType;
  isActive: boolean;
  cashbackConfigId: string;
  customerCoupons?: CustomerCoupon[];
};

/**
 * Model CustomerCoupon
 */
export type CustomerCoupon = {
  id: string;
  couponId: string;
  customerId: string;
  status: StatusCoupon;
  scheduledSendTime: Date | null;
};

/**
 * Model Tag
 *
 */
export type Tag = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  name: string;
  companyId: string;
};

/**
 * Model CustomerTag
 *
 */
export type CustomerTag = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  customerId: string;
  tagId: string;
};

/**
 * Model CompanyDefinedField
 *
 */
export type CompanyDefinedField = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  companyId: string;
  table: CompanyDefinedFieldTable;
  name: string;
  dataType: CompanyDefinedFieldDataType;
  isActive: boolean;
};

/**
 * Model ConversationTicket
 *
 */
export type ConversationTicket = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  conversationId: string;
  finishedAt: Date | null;
  score: number | null;
  firstAgentResponseAt: Date | null;
  ownerId: string | null;
  status: ConversationTicketStatus;
  categoryId: string | null;
  isWaitingForUserMessage: boolean;
  agentId: string | null;
};

/**
 * Model WhatsappSession
 *
 */
export type WhatsappSession = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  conversationId: string;
};

/**
 * Model Message
 *
 */
export type Message = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  tempId: string | null;
  text: string;
  senderPhoneNumberId: string;
  recipientPhoneNumberId: string;
  contextMessageId?: string;
  conversationId: string;
  fromSystem: boolean;
  status: MessageStatus;
  whatsappMessageId: string | null;
  mediaUrl: string | null;
  mediaType: MediaType | null;
  fileKey: string | null;
  messageTemplateId: string | null;
  whatsappCampaignId: string | null;
  firstReplyId: string | null;
  errorCode: string | null;
  errorMessage: string | null;
  automationId: string | null;
  flowNodeId: string | null;
  messageProductCardId: string | null;
  whatsappOrderId: string | null;
};

/**
 * Model MessageTemplateSuggestion
 *
 */
export type MessageTemplateSuggestion = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  templateText: string;
  name: string;
  isActive: boolean;
};

/**
 * Model MessageTemplate
 *
 */
export type MessageTemplate = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  companyId: string;
  mediaUrl: string | null;
  mediaType: MediaType | null;
  gupshupTemplateId: string | null;
  name: string;
  templateText: string;
  status: MessageTemplateStatus;
  type: MessageTemplateType;
  ctaLink: string | null;
  messageTemplateSuggestionId: string | null;
  footerText: string | null;
  communicationChannel: CommunicationChannel;
  whatsappTemplateCategory: WhatsappTemplateCategory;
  isDeleted: boolean;
  isLimitedOffer: boolean;
  limitedOfferText: string;
  limitedOfferHasExpiration: boolean;
  limitedOfferExpirationDate: Date;
};

/**
 * Model EmailTemplate
 *
 */
export type EmailTemplate = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  companyId: string;
  name: string;
  subject: string;
  type: EmailTemplateType;
  category: EmailTemplateCategory;
  html: string;
  text: string;
  unlayerDesign: Prisma.JsonValue;
  isDeleted: boolean;
};

/**
 * Model EmailDomain
 *
 */
export type EmailDomain = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  companyId: string;
  domain: string;
  address: string;
  mailFromDomain: string;
  verificationToken: string;
  domainVerificationStatus: 'success' | 'pending' | 'failed';
  lastVerifiedAt: Date | null;
  dnsRecords: Json | null;
  configurationSet: string | null;
};

/**
 * Model MessageTemplateButton
 *
 */
export type MessageTemplateButton = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  messageTemplateId: string;
  text: string;
  offerCode?: string;
  url: string | null;
  type: MessageButtonType;
};

/**
 * Model EmailTemplate
 *
 */
export type EmailTemplate = {
  createdAt: Date;
  updatedAt: Date;
  id: String;
  companyId: String;
  name: String;
  subject: String;
  html: String;
  text: String;
  category: EmailTemplateCategory;
  company: Company;
};

/**
 * Model Order
 *
 */
export type Order = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  source: string;
  sourceType?: string | null;
  sourceId: string;
  sourceCreatedAt: Date;
  sourceUpdatedAt?: Date | null;
  platformOrderId?: string | null;
  platformOrderSource?: string | null;
  value: number;
  totalItemsValue?: number | null;
  totalItemsQuantity?: number | null;
  status?: string | null;
  companyId: string;
  customerId: string;
  coupon?: string | null;
  totalDiscountsValue?: number | null;
  totalShippingValue?: number | null;
  totalTaxValue?: number | null;
  shippingCarrier?: string | null;
  trackingCode?: string | null;
  trackingUrl?: string | null;
  isTrackingCodeSent?: boolean | null;
  isTrackingCodeSentByEmail?: boolean | null;
  salesChannel?: string | null;
  storeName?: string | null;
  userAgent?: string | null;
  note?: string | null;
  noteAtributes?: any | null;
  currency?: string | null;
  statusUrl?: string | null;
  marketingData?: any | null;
  integrationConfigId?: string | null;
  cancelReason?: string | null;
  cancelledAt?: Date | null;
  isSubscription?: boolean | null;
  paymentMethods?: string | null;
  salesRep?: string;
};

/**
 * Model OrderItem
 *
 */
export type OrderItem = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  quantity: number | null;
  productId: string;
  orderId: string;
};

/**
 * Model Product
 */
export type Product = {
  id: string;
  companyId: string;
  sourceId: string | null;
  name: string;
  brand: string | null;
  sku: string | null;
  createdAt: Date;
  updatedAt: Date;
};

/**
 * Model ProductVariant
 */
export type ProductVariant = {
  id: string;
  productId: string;
  sourceId: string | null;
  name: string | null;
  title: string | null;
  description: string | null;
  sku: string | null;
  barcode: string | null;
  price: number | null;
  salePrice: number | null;
  costPrice: number | null;
  stockQuantity: number | null;
  weight: number | null;
  width: number | null;
  height: number | null;
  length: number | null;
  imageUrl: string | null;
  url: string | null;
  variantCode: string | null;
  status: string | null;
  sourceCreatedAt: Date | null;
  sourceUpdatedAt: Date | null;
  createdAt: Date;
  updatedAt: Date;
};

/**
 * Model OrderProduct
 *
 */
export type OrderProduct = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  companyId: string;
  sourceId: string | null;
  name: string;
  brand: string | null;
  sku: string | null;
};

/**
 * Model ProductCategory
 *
 */
export type ProductCategory = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  productId: string;
  categoryId: string;
};

/**
 * Model Category
 *
 */
export type Category = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  companyId: string;
  sourceId: string | null;
  name: string | null;
};

/**
 * Model GupshupTemplate
 *
 */
export type GupshupTemplate = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  messageTemplateId: string;
  elementName: string;
  languageCode: string;
  category: WhatsappTemplateCategory;
  templateType: GupshupTemplateType;
  content: string;
  buttons: Prisma.JsonValue | null;
  example: string;
  vertical: string;
  header: string | null;
  footer: string | null;
  enableSample: boolean;
  exampleHeader: string | null;
};

/**
 * Model ShortUrl
 *
 */
export type ShortUrl = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  originalUrl: string | null;
  messageId: string | null;
  smsMessageId: string | null;
  companyId: string;
};

/**
 * Model ShortUrlClick
 *
 */
export type ShortUrlClick = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  shortUrlId: string;
  userAgent: string | null;
};

/**
 * Model ConversationCategory
 *
 */
export type ConversationCategory = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  name: string;
  pos: number;
  isDeleted: boolean;
  companyId: string;
  automaticSortingOptionMessageId: string | null;
  conversationSector: ConversationSector;
};

/**
 * Model ConversationSector
 *
 */
export type ConversationSector = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  name: string;
  pos: number;
  isDeleted: boolean;
  companyId: string;
  automaticSortingOptionMessageId: string | null;
};

/**
 * Model WhatsappCampaign
 *
 */
export type WhatsappCampaign = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  companyId: string;
  templateId: string;
  totalRecipients: number;
  totalProcessed: number;
  status: WhatsappCampaignStatus;
  filterCriteria: string | null;
  templateArgs: Prisma.JsonValue | null;
  scheduledExecutionTime: Date | null;
  scheduledJobId: string | null;
  createdByUserId: string | null;
  errorMessage: string | null;
  automationId: string | null;
};

/**
 * Model EmailCampaign
 *
 */
export type EmailCampaign = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  companyId: string;
  emailTemplateId: string;
  totalRecipients: number;
  totalProcessed: number;
  filterCriteria: string | null;
  status: EmailCampaignStatus;
  createdByUserId: string | null;
  scheduledExecutionTime: Date | null;
  scheduledJobId: string | null;
  errorMessage: string | null;
  templateArgs: Prisma.JsonValue | null;
  emailCampaignResult: EmailCampaignResult;
};

/**
 * Model EmailCampaignResult
 *
 */
export type EmailCampaignResult = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  emailCampaignId: string;
  totalRecipients: number;
  totalSent: number;
  totalFailures: number;
  totalDelivered: number;
  totalReads: number;
  totalOpens: number;
  totalClicks: number;
  totalUniqueClicks: number;
  totalBounces: number;
  totalUnsubscribes: number;
  totalSpamReports: number;
};

/**
 * Model CampaignRecipient
 *
 */
export type CampaignRecipient = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  whatsappCampaignId: string | null;
  customerId: string;
  smsCampaignId: string | null;
  emailCampaignId: string | null;
};

/**
 * Model Socket
 *
 */
export type Socket = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  userId: string;
};

/**
 * Model File
 *
 */
export type File = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  name: string;
  mimeType: string;
  mediaType: MediaType | null;
  size: number;
  key: string;
  publicUrl: string | null;
};

/**
 * Model SmsCampaign
 *
 */
export type SmsCampaign = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  companyId: string;
  messageTemplateId: string;
  totalRecipients: number;
  totalProcessed: number;
  status: SmsCampaignStatus;
  filterCriteria: string | null;
  templateArgs: Prisma.JsonValue | null;
  scheduledExecutionTime: Date | null;
  scheduledJobId: string | null;
  createdByUserId: string | null;
  errorMessage: string | null;
};

/**
 * Model SmsMessage
 *
 */
export type SmsMessage = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  text: string;
  recipientPhoneNumberId: string;
  smsId: string | null;
  smsCampaignId: string;
  messageTemplateId: string;
  status: MessageStatus;
  errorMessage: string | null;
};

/**
 * Model Log
 *
 */
export type Log = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  message: string;
  severity: LogSeverity;
  source: LogSource | null;
  type: LogType | null;
  meta: Prisma.JsonValue | null;
  companyId: string | null;
};

/**
 * Model ColumnMappingConfig
 *
 */
export type ColumnMappingConfig = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  companyId: string;
  table: ColumnMappingConfigTable;
  mapping: Prisma.JsonValue;
};

/**
 * Model SocketIoAttachments
 *
 */
export type SocketIoAttachments = {
  createdAt: Date;
  id: string;
  payload: Buffer;
};

/**
 * Model Filter
 *
 */
export type Filter = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  companyId: string;
  name: string;
  type: FilterType;
  criteria: string;
};

/**
 * Model AudienceRecommendation
 *
 */
export type AudienceRecommendation = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  companyId: string;
  filterCriteria: string;
  classId: string;
  meta: Prisma.JsonValue | null;
};

/**
 * Model AudienceRecommendationClass
 *
 */
export type AudienceRecommendationClass = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  name: string;
  description: string;
  slug: AudienceRecommendationClassSlug;
};

/**
 * Model PromptThread
 *
 */
export type PromptThread = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  initialPromptId: string;
  userId: string | null;
};

/**
 * Model PromptCompletition
 *
 */
export type PromptCompletition = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  promptThreadId: string;
  promptText: string;
  completitionText: string | null;
  promptTokens: number | null;
  completitionTokens: number | null;
  finishReason: string | null;
};

/**
 * Model InitialPrompt
 *
 */
export type InitialPrompt = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  type: InitialPromptType;
  promptText: string;
};

/**
 * Model GupshupBillingEvent
 *
 */
export type GupshupBillingEvent = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  gupshupAppName: string | null;
  deductionsType: string | null;
  deductionsModel: string | null;
  deductionsSource: string | null;
  deductionsBillable: boolean | null;
  referencesId: string | null;
  referencesGsId: string | null;
  referencesConversationId: string | null;
  referencesDestination: string | null;
  timestamp: Date;
};

/**
 * Model QuickReply
 *
 */
export type QuickReply = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  text: string;
  title: string;
  companyId: string;
};

/**
 * Model Flow
 *
 */
export type Flow = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  title: string;
  companyId: string;
  isActive: boolean;
  repeatOnInvalidInput: boolean;
};

/**
 * Model FlowNode
 *
 */
export type FlowNode = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  type: FlowNodeType;
  data: Prisma.JsonValue;
  posX: number;
  posY: number;
  flowId: string;
  nextFlowNodeId: string | null;
};

/**
 * Model FlowTrigger
 *
 */
export type FlowTrigger = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  type: FlowTriggerType;
  text: string;
  companyId: string;
  flowId: string;
  targetFlowNodeId: string;
  invocationCount: number;
  isDefault: boolean;
  isDeleted: boolean;
  _count?: {
    flowEvents: number;
  };
};

/**
 * Model FlowNodeButton
 *
 */
export type FlowNodeButton = {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  type: MessageButtonType;
  index: number;
  text: string;
  url: string | null;
  clickCount: number;
  flowNodeId: string;
  targetFlowNodeId: string | null;
};

/**
 * Enums
 */

// Based on
// https://github.com/microsoft/TypeScript/issues/3192#issuecomment-*********

export const AutomaticReplyCondition: {
  contains: 'contains';
  equals: 'equals';
};

export type AutomaticReplyCondition =
  (typeof AutomaticReplyCondition)[keyof typeof AutomaticReplyCondition];

export const ConversationTicketStatus: {
  open: 'open';
  closed: 'closed';
};

export type ConversationTicketStatus =
  (typeof ConversationTicketStatus)[keyof typeof ConversationTicketStatus];

export const AutomationTypeSlug: {
  tracking_code: 'tracking_code';
  abandoned_cart: 'abandoned_cart';
  order_status_update: 'order_status_update';
  new_order: 'new_order';
  welcome_registration: 'welcome_registration';
  order_confirmation: 'order_confirmation';
  order_payment_confirmation: 'order_payment_confirmation';
  custom: 'custom';
};

export const SourceIntegration: {
  hubspot_crm: 'hubspot_crm';
  magento2_ecommerce: 'magento2_ecommerce';
  nuvem_shop: 'nuvem_shop';
  vtex_ecommerce: 'vtex_ecommerce';
  file_import: 'file_import';
  direct_message: 'direct_message';
  unknown: 'unknown';
  shopify_ecommerce: 'shopify_ecommerce';
  visual_ecommerce: 'visual_ecommerce';
  loja_integrada: 'loja_integrada';
  woo_commerce: 'woo_commerce';
  bling: 'bling';
  magazord: 'magazord';
  unbox: 'unbox';
  magento1_ecommerce: 'magento1_ecommerce';
  ingresse: 'ingresse';
  yampi: 'yampi';
  google_tag_manager: 'google_tag_manager';
  omny: 'omny';
  tray: 'tray';
  omie: 'omie';
  cartPanda: 'cartPanda';
  linx_commerce: 'linx_commerce';
  shoppub: 'shoppub';
  custom: 'custom';
  tiny: 'tiny';
  vnda: 'vnda';
  millennium: 'millennium';
  varejo_online: 'varejo_online';
  venda_ai: 'venda_ai';
  irroba: 'irroba';
  wake_commerce: 'wake_commerce';
  uappi: 'uappi';
};

export type SourceIntegration =
  (typeof SourceIntegration)[keyof typeof SourceIntegration];

export type AutomationTypeSlug =
  (typeof AutomationTypeSlug)[keyof typeof AutomationTypeSlug];

export const CustomerSource: {
  hubspot_crm: 'hubspot_crm';
  magento2_ecommerce: 'magento2_ecommerce';
  vtex_ecommerce: 'vtex_ecommerce';
  file_import: 'file_import';
  direct_message: 'direct_message';
  unknown: 'unknown';
  shopify_ecommerce: 'shopify_ecommerce';
  visual_ecommerce: 'visual_ecommerce';
  loja_integrada: 'loja_integrada';
};

export type CustomerSource =
  (typeof CustomerSource)[keyof typeof CustomerSource];

export const CompanyDefinedFieldTable: {
  customers: 'customers';
};

export type CompanyDefinedFieldTable =
  (typeof CompanyDefinedFieldTable)[keyof typeof CompanyDefinedFieldTable];

export const CompanyDefinedFieldDataType: {
  string: 'string';
  number: 'number';
  boolean: 'boolean';
  date: 'date';
};

export type CompanyDefinedFieldDataType =
  (typeof CompanyDefinedFieldDataType)[keyof typeof CompanyDefinedFieldDataType];

export const MessageStatus: {
  read: 'read';
  failed: 'failed';
  delivered: 'delivered';
  sent: 'sent';
  enqueued: 'enqueued';
  mismatch: 'mismatch';
};

export type MessageStatus = (typeof MessageStatus)[keyof typeof MessageStatus];

export const MediaType: {
  audio: 'audio';
  file: 'file';
  image: 'image';
  video: 'video';
  sticker: 'sticker';
};

export type MediaType = (typeof MediaType)[keyof typeof MediaType];

export const MessageTemplateStatus: {
  pending: 'pending';
  rejected: 'rejected';
  approved: 'approved';
  deleted: 'deleted';
  disabled: 'disabled';
  paused: 'paused';
};

export type MessageTemplateStatus =
  (typeof MessageTemplateStatus)[keyof typeof MessageTemplateStatus];

export const MessageTemplateType: {
  MARKETING: 'MARKETING';
  REVIEW_REQUEST: 'REVIEW_REQUEST';
  INITIAL_MESSAGE: 'INITIAL_MESSAGE';
  ABANDONED_CART: 'ABANDONED_CART';
  ORDER_STATUS_UPDATE: 'ORDER_STATUS_UPDATE';
  CASHBACK: 'CASHBACK';
  TRACKING_CODE: 'TRACKING_CODE';
  NEW_ORDER: 'NEW_ORDER';
  WELCOME_REGISTRATION: 'WELCOME_REGISTRATION';
  ORDER_CONFIRMATION: 'ORDER_CONFIRMATION';
  ORDER_PAYMENT_CONFIRMATION: 'ORDER_PAYMENT_CONFIRMATION';
};

export type MessageTemplateType =
  (typeof MessageTemplateType)[keyof typeof MessageTemplateType];

export const EmailTemplateType: {
  MARKETING: 'MARKETING';
  REVIEW_REQUEST: 'REVIEW_REQUEST';
  ABANDONED_CART: 'ABANDONED_CART';
  TRACKING_CODE: 'TRACKING_CODE';
  ORDER_STATUS_UPDATE: 'ORDER_STATUS_UPDATE';
};

export type EmailTemplateType =
  (typeof EmailTemplateType)[keyof typeof EmailTemplateType];

export const EmailTemplateCategory: {
  MARKETING: 'MARKETING';
  TRANSACTIONAL: 'TRANSACTIONAL';
  UTILITY: 'UTILITY';
};

export type EmailTemplateCategory =
  (typeof EmailTemplateCategory)[keyof typeof EmailTemplateCategory];

export const CommunicationChannel: {
  whatsapp: 'whatsapp';
  sms: 'sms';
};

export type CommunicationChannel =
  (typeof CommunicationChannel)[keyof typeof CommunicationChannel];

export const WhatsappTemplateCategory: {
  MARKETING: 'MARKETING';
  AUTHENTICATION: 'AUTHENTICATION';
  UTILITY: 'UTILITY';
};

export type WhatsappTemplateCategory =
  (typeof WhatsappTemplateCategory)[keyof typeof WhatsappTemplateCategory];

export const MessageButtonType: {
  QUICK_REPLY: 'QUICK_REPLY';
  URL: 'URL';
};

export type MessageButtonType =
  (typeof MessageButtonType)[keyof typeof MessageButtonType];

export const GupshupTemplateType: {
  TEXT: 'TEXT';
  IMAGE: 'IMAGE';
  VIDEO: 'VIDEO';
  DOCUMENT: 'DOCUMENT';
};

export type GupshupTemplateType =
  (typeof GupshupTemplateType)[keyof typeof GupshupTemplateType];

export const WhatsappCampaignStatus: {
  in_progress: 'in_progress';
  completed: 'completed';
  interrupted: 'interrupted';
  failed: 'failed';
  scheduled: 'scheduled';
  canceled: 'canceled';
};

export type WhatsappCampaignStatus =
  (typeof WhatsappCampaignStatus)[keyof typeof WhatsappCampaignStatus];

export const EmailCampaignStatus: {
  in_progress: 'in_progress';
  completed: 'completed';
  interrupted: 'interrupted';
  failed: 'failed';
  scheduled: 'scheduled';
  canceled: 'canceled';
};

export type EmailCampaignStatus =
  (typeof EmailCampaignStatus)[keyof typeof EmailCampaignStatus];

export const SmsCampaignStatus: {
  in_progress: 'in_progress';
  completed: 'completed';
  scheduled: 'scheduled';
  canceled: 'canceled';
  failed: 'failed';
};

export type SmsCampaignStatus =
  (typeof SmsCampaignStatus)[keyof typeof SmsCampaignStatus];

export const LogSeverity: {
  error: 'error';
  info: 'info';
  warning: 'warning';
};

export type LogSeverity = (typeof LogSeverity)[keyof typeof LogSeverity];

export const LogSource: {
  gupshup: 'gupshup';
  internal: 'internal';
  vtex: 'vtex';
  shopify: 'shopify';
  sms_legal: 'sms_legal';
  visual_ecommerce: 'visual_ecommerce';
  eyou: 'eyou';
  loja_integrada: 'loja_integrada';
  woo_commerce: 'woo_commerce';
  bling: 'bling';
  magazord: 'magazord';
  unbox: 'unbox';
  magento2_ecommerce: 'magento2_ecommerce';
  nuvem_shop: 'nuvem_shop';
  magento1_ecommerce: 'magento1_ecommerce';
  yampi: 'yampi';
  revi_public_api: 'revi_public_api';
  omny: 'omny';
  tray: 'tray';
  omie: 'omie';
  cartPanda: 'cartPanda';
  linx_commerce: 'linx_commerce';
  shoppub: 'shoppub';
  ingresse: 'ingresse';
  revi_plataform: 'revi_plataform';
  custom_integration: 'custom_integration';
  tiny: 'tiny';
  vnda: 'vnda';
  millennium: 'millennium';
  varejo_online: 'varejo_online';
  venda_ai: 'venda_ai';
  sendgrid: 'sendgrid';
  irroba: 'irroba';
  wake_commerce: 'wake_commerce';
};

export type LogSource = (typeof LogSource)[keyof typeof LogSource];

export const LogType: {
  syncOrders: 'syncOrders';
  syncAbandonedCarts: 'syncAbandonedCarts';
  sendAbandonedCartMessages: 'sendAbandonedCartMessages';
  executeCompanyCustomPeriodicTask: 'executeCompanyCustomPeriodicTask';
  connectingDatabase: 'connectingDatabase';
  sendTrackingCodeMessages: 'sendTrackingCodeMessages';
  sendOrderStatusUpdateMessages: 'sendOrderStatusUpdateMessages';
  reviPublicApiCall: 'reviPublicApiCall';
  orderEvents: 'orderEvents';
  syncCustomers: 'syncCustomers';
  scheduleAbandonedCarts: 'scheduleAbandonedCarts';
  userActivity: 'userActivity';
  handleEmailNotifications: 'handleEmailNotifications';
  sendEmail: 'sendEmail';
  generateAiAgentSuggestion: 'generateAiAgentSuggestion';
  selectAiAgentSuggestion: 'selectAiAgentSuggestion';
  sendAiAgentSuggestion: 'sendAiAgentSuggestion';
};

export type LogType = (typeof LogType)[keyof typeof LogType];

export const ColumnMappingConfigTable: {
  orders: 'orders';
};

export type ColumnMappingConfigTable =
  (typeof ColumnMappingConfigTable)[keyof typeof ColumnMappingConfigTable];

export const FilterType: {
  customer: 'customer';
};

export type FilterType = (typeof FilterType)[keyof typeof FilterType];

export const AudienceRecommendationClassSlug: {
  champion: 'champion';
  new: 'new';
  inactive: 'inactive';
};

export type AudienceRecommendationClassSlug =
  (typeof AudienceRecommendationClassSlug)[keyof typeof AudienceRecommendationClassSlug];

export const InitialPromptType: {
  generate_whatsapp_message_template: 'generate_whatsapp_message_template';
};

export type InitialPromptType =
  (typeof InitialPromptType)[keyof typeof InitialPromptType];

export type FlowType =
  | 'default'
  | 'abandoned_cart'
  | 'csat'
  | 'initial_contact';

// Check if I need to change this
export const FlowNodeType: {
  trigger: 'trigger';
  send_whatsapp_message: 'send_whatsapp_message';
  send_whatsapp_media: 'send_whatsapp_media';
  move_conversation_to_category: 'move_conversation_to_category';
  csat: 'csat';
  initial_contact: 'initial_contact';
};

export type FlowNodeType = (typeof FlowNodeType)[keyof typeof FlowNodeType];

export const FlowTriggerType: {
  exact_match: 'exact_match';
  keyword_match: 'keyword_match';
  abandoned_cart: 'abandoned_cart';
  quick_reply_message_template: 'quick_reply_message_template';
  csat: 'csat';
  initial_contact: 'initial_contact';
};

export type FlowTriggerType =
  (typeof FlowTriggerType)[keyof typeof FlowTriggerType];

export const WinningMetric: {
  engagement_rate: 'engagement_rate';
  read_rate: 'read_rate';
};

export type WinningMetric = (typeof WinningMetric)[keyof typeof FlowNodeType];

// Model Role
export interface Role {
  id: string;
  name: string;
  appModulePermissions: AppModule[];
  companyId: string;
  createdAt: string;
  updatedAt: string;
}
