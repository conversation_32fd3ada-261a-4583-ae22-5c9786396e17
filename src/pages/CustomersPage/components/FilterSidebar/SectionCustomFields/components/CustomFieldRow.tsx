import { FormControl, Text, Box, Select, VStack } from '@chakra-ui/react';
import { Controller, UseFormReturn } from 'react-hook-form';
import FormLabel from '../../../../../../components/FormLabel';
import InputSelect, {
  SelectOption,
} from '../../../../../../components/InputSelect';
import {
  CompanyDefinedFieldDataTypeEnum,
  CompanyDefinedField,
} from '../../../../../../types/CompanyDefinedField';
import { useEffect, useMemo } from 'react';
import { ComparisonTypesUtils } from '../../../../../../utils/comparison-types.utils';

interface CustomFieldRowProps {
  index: number;
  useFormReturn: UseFormReturn<any>;
  companyDefinedFields: CompanyDefinedField[];
  fieldValues: SelectOption[];
  isDisabled: boolean;
  onCreateCustomFieldValue: (index: number, inputValue: string) => void;
  onCreateNumberValue: (index: number, inputValue: string) => void;
}

const formatSingleSelectValue = (value: string | number | null) => {
  if (value === null || value === undefined || value.toString().trim() === '') {
    return undefined;
  }
  return [{ value: value.toString(), label: value.toString() }];
};

const CustomFieldRow = ({
  index,
  useFormReturn,
  companyDefinedFields,
  fieldValues,
  isDisabled,
  onCreateCustomFieldValue,
  onCreateNumberValue,
}: CustomFieldRowProps) => {
  const {
    control,
    watch,
    setValue,
    formState: { errors },
  } = useFormReturn;

  const fieldIdNumber = index + 1;
  const fieldIdKey = `customFieldId${fieldIdNumber}`;
  const comparisonTypeKey = `customFieldComparisonType${fieldIdNumber}`;
  const valueKey = `customFieldValue${fieldIdNumber}`;

  const selectedFieldId = watch(fieldIdKey);
  const comparisonType = watch(comparisonTypeKey);

  const selectedField = useMemo(
    () => companyDefinedFields.find((field) => field.id === selectedFieldId),
    [selectedFieldId, companyDefinedFields],
  );

  useEffect(() => {
    if (!selectedFieldId || !selectedField) {
      setValue(comparisonTypeKey, null);
      setValue(valueKey, null);
    }
  }, [selectedFieldId, selectedField, setValue, comparisonTypeKey, valueKey]);

  return (
    <VStack spacing={4} align="stretch">
      <FormControl>
        <Box>
          <FormLabel
            size="sm"
            tooltip="Selecione uma coluna customizada para filtrar"
          >
            Nome da coluna {fieldIdNumber}
          </FormLabel>
          <Controller
            name={fieldIdKey}
            control={control}
            render={({ field }) => (
              <Select
                size="md"
                bg="white"
                placeholder="Selecione uma coluna"
                isDisabled={isDisabled}
                {...field}
              >
                {companyDefinedFields?.map((field) => (
                  <option key={field.id} value={field.id}>
                    {field.name}
                  </option>
                ))}
              </Select>
            )}
          />
        </Box>
      </FormControl>

      {selectedField && (
        <>
          <FormControl isInvalid={!!errors[comparisonTypeKey]}>
            <Box>
              <FormLabel
                size="sm"
                tooltip="Escolha como deseja comparar o valor da coluna"
              >
                Tipo de Comparação
              </FormLabel>
              <Controller
                name={comparisonTypeKey}
                control={control}
                rules={{ required: 'Selecione um tipo de comparação' }}
                render={({ field }) => (
                  <Select
                    size="md"
                    bg="white"
                    isDisabled={isDisabled || !selectedFieldId}
                    {...field}
                    placeholder="Selecione o comparador"
                  >
                    {ComparisonTypesUtils.getComparisonOptionsForDataType(
                      selectedField.dataType,
                    ).map((option) => (
                      <option value={option.value}>{option.label}</option>
                    ))}
                  </Select>
                )}
              />
              {errors[comparisonTypeKey] && (
                <Text fontSize="sm" color="red.500">
                  {(errors[comparisonTypeKey]?.message as string) || ''}
                </Text>
              )}
            </Box>
          </FormControl>
          <FormControl isInvalid={!!errors[valueKey]}>
            <Box>
              <FormLabel
                size="sm"
                tooltip="Insira o valor para filtrar a coluna selecionada"
              >
                Valor -{' '}
                {selectedField.dataType ===
                CompanyDefinedFieldDataTypeEnum.NUMBER
                  ? 'Número'
                  : 'Texto'}
              </FormLabel>
              <Controller
                name={valueKey}
                control={control}
                rules={{ required: 'Insira um valor' }}
                render={({ field }) => (
                  <InputSelect
                    onCreateOption={(inputValue: string) =>
                      selectedField.dataType ===
                      CompanyDefinedFieldDataTypeEnum.NUMBER
                        ? onCreateNumberValue(index, inputValue)
                        : onCreateCustomFieldValue(index, inputValue)
                    }
                    placeholder="Selecione ou digite um valor para buscar"
                    options={fieldValues.map((item) => ({
                      value: item.value,
                      label: item.label,
                    }))}
                    value={formatSingleSelectValue(field.value)}
                    onChange={(selectedOption: SelectOption) => {
                      field.onChange(selectedOption?.value ?? null);
                    }}
                    disabled={isDisabled || !comparisonType}
                    createLabel={(inputValue: string) =>
                      `Setar valor: ${inputValue}`
                    }
                  />
                )}
              />
              {errors[valueKey] && (
                <Text fontSize="sm" color="red.500">
                  {errors[valueKey]?.message as string}
                </Text>
              )}
            </Box>
          </FormControl>
        </>
      )}
    </VStack>
  );
};

export default CustomFieldRow;
