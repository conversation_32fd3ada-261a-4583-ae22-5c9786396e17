import React from 'react';
import {
  VStack,
  Flex,
  Text,
  Badge,
  Box,
  Center,
  Spinner,
  SimpleGrid,
  Button,
  Select,
  FormLabel,
  FormControl,
} from '@chakra-ui/react';
import { CatalogType } from '../../../../../../types/Catalog';
import { ProductVariant } from '../../../../../../types/Product';
import { ProcessedIntegration } from '../../../../../../utils/integration.utils';
import { scrollbarStyles } from '../../../../../../styles/scrollbar.styles';
import { ProductCard } from '../ProductCard';
import { ProductSearch } from '../ProductSearch';

interface ProductsStepProps {
  selectedType: CatalogType;
  selectedProducts: string[];
  products: ProductVariant[];
  searchInput: string;
  activeIntegrations: ProcessedIntegration[];
  selectedIntegration: string;
  onSearchInputChange: (value: string) => void;
  onSearch: () => void;
  onProductToggle: (productId: string) => void;
  onLoadMore: () => void;
  onIntegrationChange: (integration: string) => void;
  isLoading: boolean;
  currentPage: number;
  totalPages: number;
  totalItems: number;
}

export const ProductsStep = ({
  selectedType,
  selectedProducts,
  products,
  searchInput,
  activeIntegrations,
  selectedIntegration,
  onSearchInputChange,
  onSearch,
  onProductToggle,
  onLoadMore,
  onIntegrationChange,
  isLoading,
  currentPage,
  totalPages,
  totalItems,
}: ProductsStepProps) => {
  return (
    <VStack spacing={6} align="stretch" flex="1" overflow="hidden">
      <VStack spacing={4} align="stretch" flexShrink={0}>
        <Flex justify="space-between" align="center">
          <VStack align="start" spacing={1}>
            <Text fontSize="lg" fontWeight="bold" color="gray.800">
              Selecione os produtos
            </Text>
            <Text fontSize="sm" color="gray.600">
              {selectedType.maxProducts === 1
                ? 'Escolha 1 produto para destacar'
                : `Selecione de ${selectedType.minProducts} até ${selectedType.maxProducts} produtos`}
            </Text>
          </VStack>
          <Badge
            colorScheme={
              selectedProducts.length >= selectedType.minProducts
                ? 'green'
                : 'gray'
            }
            fontSize="sm"
            px={3}
            py={1}
            borderRadius="full"
          >
            {selectedProducts.length} selecionado(s)
          </Badge>
        </Flex>

        {activeIntegrations.length > 1 && (
          <FormControl>
            <FormLabel fontSize="sm" color="gray.700" mb={2}>
              Fonte dos produtos
            </FormLabel>
            <Select
              value={selectedIntegration}
              onChange={(e) => onIntegrationChange(e.target.value)}
              placeholder="Selecione uma integração"
              bg="white"
              borderColor="gray.300"
              _hover={{ borderColor: 'gray.400' }}
              _focus={{
                borderColor: 'blue.500',
                boxShadow: '0 0 0 1px blue.500',
              }}
            >
              {activeIntegrations.map((integration) => (
                <option key={integration.key} value={integration.key}>
                  {integration.name}
                </option>
              ))}
            </Select>
          </FormControl>
        )}

        <ProductSearch
          searchInput={searchInput}
          onSearchInputChange={onSearchInputChange}
          onSearch={onSearch}
          isLoading={isLoading}
        />
      </VStack>

      <Box flex="1" overflow="auto">
        {!selectedIntegration ? (
          <Center py={12}>
            <VStack spacing={3}>
              <Text fontSize="lg" color="gray.500">
                Nenhuma integração selecionada
              </Text>
              <Text fontSize="sm" color="gray.400" textAlign="center">
                Selecione uma integração para visualizar os produtos disponíveis
              </Text>
            </VStack>
          </Center>
        ) : isLoading && currentPage === 1 ? (
          <Center py={12}>
            <VStack spacing={4}>
              <Spinner size="xl" color="blue.500" thickness="4px" />
              <Text color="gray.600">Carregando produtos...</Text>
            </VStack>
          </Center>
        ) : (
          <VStack spacing={4} align="stretch">
            {products.length > 0 ? (
              <>
                <SimpleGrid
                  columns={{ base: 1, md: 2, lg: 3 }}
                  spacing={4}
                  css={scrollbarStyles({ width: '5px' })}
                >
                  {products.map((product) => (
                    <ProductCard
                      key={product.id}
                      product={product}
                      isSelected={selectedProducts.includes(product.id)}
                      onToggle={onProductToggle}
                    />
                  ))}
                </SimpleGrid>

                {currentPage < totalPages && (
                  <Center pt={4}>
                    <Button
                      onClick={onLoadMore}
                      isLoading={isLoading}
                      loadingText="Carregando..."
                      variant="outline"
                      colorScheme="blue"
                    >
                      Carregar mais produtos ({products.length} de {totalItems})
                    </Button>
                  </Center>
                )}
              </>
            ) : (
              <Center py={12}>
                <VStack spacing={3}>
                  <Text fontSize="lg" color="gray.500">
                    Nenhum produto encontrado
                  </Text>
                  <Text fontSize="sm" color="gray.400" textAlign="center">
                    Tente ajustar os termos de busca ou verifique se há produtos
                    cadastrados para esta integração
                  </Text>
                </VStack>
              </Center>
            )}
          </VStack>
        )}
      </Box>
    </VStack>
  );
};
