import { UsersService } from '../services/users.service';
import { useOptimisticToggle } from './useOptimisticToggle';

export const useUserToggle = (queryKey: string) => {
  const toggleUserStatus = useOptimisticToggle({
    queryKey,
    mutationFn: UsersService.toggleUserStatus,
    field: 'isActive',
    successMessage: {
      titleTrue: 'Usuário Ativado',
      titleFalse: 'Usuário Inativado',
      getDescription: (name: string, isActive: boolean) =>
        `O usuário "${name}" foi ${isActive ? 'ativado' : 'inativado'} com sucesso.`,
    },
    errorMessage: 'Não foi possível alterar o status do usuário.',
  });

  const toggleCanViewAllConversations = useOptimisticToggle({
    queryKey,
    mutationFn: UsersService.toggleUserCanViewAllConversations,
    field: 'canViewAllConversations',
    successMessage: {
      titleTrue: 'Permissão Concedida',
      titleFalse: 'Permissão Revogada',
      getDescription: (name: string, canView: boolean) =>
        `O usuário "${name}" agora ${canView ? 'pode' : 'não pode'} ver todas as conversas.`,
    },
    errorMessage: 'Não foi possível alterar as permissões do usuário.',
  });

  return {
    toggleUserStatus,
    toggleCanViewAllConversations,
  };
};
