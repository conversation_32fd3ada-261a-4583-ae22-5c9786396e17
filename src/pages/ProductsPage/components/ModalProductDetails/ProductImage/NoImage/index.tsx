import { Center, Text, Tooltip, BoxProps } from '@chakra-ui/react';

interface NoImageProps extends BoxProps {
  showTooltip?: boolean;
}

const NoImage = ({ showTooltip = true, ...boxProps }: NoImageProps) => {
  const content = (
    <Center
      bg="gray.50"
      border="1px solid"
      borderColor="gray.200"
      borderRadius="md"
      overflow="hidden"
      flexDirection="column"
      gap={2}
      cursor={showTooltip ? 'pointer' : 'default'}
      {...boxProps}
    >
      <Text
        fontSize="xs"
        color="gray.400"
        textAlign="center"
        px={2}
        whiteSpace="pre-line"
      >
        Sem{'\n'}Imagem
      </Text>
    </Center>
  );

  if (!showTooltip) {
    return content;
  }

  return (
    <Tooltip
      label="Imagem não disponível"
      placement="right"
      hasArrow
      bg="white"
      border="1px solid"
      borderColor="gray.200"
      borderRadius="md"
      p={2}
      boxShadow="lg"
      openDelay={300}
      closeDelay={100}
    >
      {content}
    </Tooltip>
  );
};

export default NoImage;
