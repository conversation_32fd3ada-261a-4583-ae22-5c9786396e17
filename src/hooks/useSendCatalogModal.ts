import { useState, useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { useMutation, useQuery } from 'react-query';
import { useToast } from '@chakra-ui/react';
import { ProductsService } from '../services/products.service';
import { ProductCatalogService } from '../services/product-catalog.service';
import { MessagesService } from '../services/messages.service';
import { IntegrationsService } from '../services/integrations.service';
import { CATALOG_TYPES, CatalogFormData } from '../types/Catalog';
import { ProductVariant } from '../types/Product';
import {
  IntegrationUtils,
  ProcessedIntegration,
} from '../utils/integration.utils';
import { apiRoutes } from '../constants/api-routes';

type Step = 'type' | 'content' | 'products' | 'review';

interface UseSendCatalogModalProps {
  conversationId: string;
  onClose: () => void;
}

export const useSendCatalogModal = ({
  conversationId,
  onClose,
}: UseSendCatalogModalProps) => {
  const toast = useToast();

  const [step, setStep] = useState<Step>('type');
  const [searchQuery, setSearchQuery] = useState('');
  const [searchInput, setSearchInput] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [products, setProducts] = useState<ProductVariant[]>([]);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [selectedIntegration, setSelectedIntegration] = useState<string>('');

  const form = useForm<CatalogFormData>({
    mode: 'onChange',
    defaultValues: {
      type: 'catalog',
      header: '',
      body: '',
      footer: '',
      selectedProducts: [],
    },
  });

  const watchedValues = form.watch();
  const selectedType = CATALOG_TYPES[watchedValues.type];

  const {
    data: companyCatalog,
    isLoading: catalogLoading,
    error: catalogError,
  } = useQuery(
    apiRoutes.listCatalogByCompany(),
    async () => {
      const response = await ProductCatalogService.listCatalogByCompany();
      return response.data;
    },
    {
      retry: false,
    },
  );

  const hasValidCatalog = companyCatalog?.metaCatalogId;

  const { data: activeIntegrations = [], isLoading: integrationsLoading } =
    useQuery(
      apiRoutes.getIntegrationStatusSummary(),
      async () => {
        const { data } =
          await IntegrationsService.getIntegrationStatusSummary();
        return IntegrationUtils.getActiveIntegrations(data);
      },
      {
        enabled: !!hasValidCatalog,
        onSuccess: (integrations: ProcessedIntegration[]) => {
          if (integrations.length > 0 && !selectedIntegration) {
            setSelectedIntegration(integrations[0].key);
          }
        },
      },
    );

  const { refetch: fetchProducts, isLoading: isLoadingProducts } = useQuery(
    apiRoutes.listCatalogProducts({
      searchQuery: searchQuery.trim(),
      source: selectedIntegration,
      page: currentPage,
      perPage: 10,
    }),
    async () => {
      if (!selectedIntegration) {
        return {
          data: [],
          meta: { page: 1, perPage: 10, totalPages: 1, totalItems: 0 },
        };
      }

      const { data } = await ProductsService.listCatalogProducts({
        searchQuery: searchQuery.trim(),
        source: selectedIntegration,
        page: currentPage,
        perPage: 10,
      });
      return data;
    },
    {
      enabled:
        !!selectedIntegration && step === 'products' && !!hasValidCatalog,
      onSuccess: (response) => {
        const { data: newProducts, meta } = response;

        if (currentPage === 1) {
          setProducts(newProducts);
        } else {
          setProducts((prev) => [...prev, ...newProducts]);
        }

        setTotalPages(meta.totalPages);
        setTotalItems(meta.totalItems);
      },
    },
  );

  const sendCatalogMutation = useMutation(
    async (catalogData: any) =>
      MessagesService.sendMessageSessionCatalog({
        ...catalogData,
        conversationId,
      }),
    {
      onSuccess: () => {
        toast({
          title: 'Catálogo enviado com sucesso!',
          description: 'Sua mensagem foi enviada para o cliente.',
          status: 'success',
          duration: 4000,
          isClosable: true,
        });
        onClose();
        handleReset();
      },
    },
  );

  const handleReset = useCallback(() => {
    form.reset();
    setStep('type');
    setSearchQuery('');
    setSearchInput('');
    setCurrentPage(1);
    setProducts([]);
  }, [form]);

  const handleSearch = useCallback(
    (page = 1, newQuery = searchQuery) => {
      setCurrentPage(page);
      setSearchQuery(newQuery);
      fetchProducts();
    },
    [fetchProducts, searchQuery],
  );

  const handleProductToggle = useCallback(
    (productId: string) => {
      const currentSelected = watchedValues.selectedProducts || [];
      const isSelected = currentSelected.includes(productId);

      if (isSelected) {
        form.setValue(
          'selectedProducts',
          currentSelected.filter((id) => id !== productId),
        );
        return;
      }

      if (selectedType.maxProducts === 1) {
        form.setValue('selectedProducts', [productId]);
      } else if (currentSelected.length < selectedType.maxProducts) {
        form.setValue('selectedProducts', [...currentSelected, productId]);
      } else {
        toast({
          title: `Máximo de ${selectedType.maxProducts} produtos permitidos`,
          status: 'warning',
          duration: 3000,
          isClosable: true,
        });
      }
    },
    [watchedValues.selectedProducts, selectedType.maxProducts, form, toast],
  );

  const handleIntegrationChange = useCallback(
    (newIntegration: string) => {
      setSelectedIntegration(newIntegration);
      setCurrentPage(1);
      setProducts([]);
      form.setValue('selectedProducts', []);

      if (step === 'products') {
        handleSearch(1, searchQuery);
      }
    },
    [step, searchQuery, handleSearch, form],
  );

  const buildCatalogPayload = useCallback(
    (data: CatalogFormData) => {
      const selectedProductData = products.filter((p) =>
        data.selectedProducts.includes(p.id),
      );

      const catalogId = companyCatalog?.id;

      const basePayload = {
        conversationId,
        catalogId,
        type: data.type,
        bodyText: data.body,
        footerText: data.footer || '',
      };

      switch (data.type) {
        case 'catalog':
          return {
            ...basePayload,
            headerText: data.header,
            thumbnailProductId: selectedProductData[0]?.id || '',
          };
        case 'spm':
          return {
            ...basePayload,
            singleProductId: selectedProductData[0]?.id || '',
          };
        case 'mpm':
          return {
            ...basePayload,
            headerText: data.header,
            sections: [
              {
                title: 'Produtos Selecionados',
                productIds: selectedProductData.map((product) => product.id),
              },
            ],
          };
        default:
          return basePayload;
      }
    },
    [products, conversationId, companyCatalog?.metaCatalogId],
  );

  const onSubmit = useCallback(
    (data: CatalogFormData) => {
      const payload = buildCatalogPayload(data);
      sendCatalogMutation.mutate(payload);
    },
    [buildCatalogPayload, sendCatalogMutation],
  );

  const canProceed = useCallback(() => {
    switch (step) {
      case 'type':
        return !!watchedValues.type;
      case 'content':
        return !!(
          watchedValues.body?.trim() &&
          (!selectedType.needsHeader || watchedValues.header?.trim())
        );
      case 'products':
        const selected = watchedValues.selectedProducts?.length || 0;
        return (
          selected >= selectedType.minProducts &&
          selected <= selectedType.maxProducts &&
          !!selectedIntegration
        );
      case 'review':
        return form.formState.isValid && !!companyCatalog?.metaCatalogId;
      default:
        return false;
    }
  }, [
    step,
    watchedValues,
    selectedType,
    selectedIntegration,
    form.formState.isValid,
    companyCatalog?.metaCatalogId,
  ]);

  const handleNext = useCallback(async () => {
    const isValid = await form.trigger();
    if (!isValid) return;

    const nextSteps = {
      type: 'content',
      content: 'products',
      products: 'review',
    };
    setStep(nextSteps[step as keyof typeof nextSteps] as Step);
  }, [form, step]);

  const handleBack = useCallback(() => {
    const prevSteps = {
      content: 'type',
      products: 'content',
      review: 'products',
    };
    setStep(prevSteps[step as keyof typeof prevSteps] as Step);
  }, [step]);

  const handleStepChange = useCallback(
    (newStep: Step) => {
      setStep(newStep);
      if (newStep === 'products') {
        handleSearch(1, '');
      }
    },
    [handleSearch],
  );

  const getSelectedProductsData = useCallback(
    () =>
      products.filter((p) => watchedValues.selectedProducts?.includes(p.id)),
    [products, watchedValues.selectedProducts],
  );

  const handleSearchClick = useCallback(() => {
    setCurrentPage(1);
    setSearchQuery(searchInput);
    handleSearch(1, searchInput);
  }, [searchInput, handleSearch]);

  const handleLoadMore = useCallback(() => {
    if (currentPage < totalPages) {
      handleSearch(currentPage + 1);
    }
  }, [currentPage, totalPages, handleSearch]);

  const handleTypeChange = useCallback(
    (newType: string) => {
      form.setValue('type', newType as any);
      form.setValue('selectedProducts', []);
    },
    [form],
  );

  return {
    step,
    searchInput,
    products,
    currentPage,
    totalPages,
    totalItems,
    selectedIntegration,
    watchedValues,
    selectedType,
    isLoadingProducts,
    integrationsLoading,
    catalogLoading,
    activeIntegrations,
    companyCatalog,
    hasValidCatalog,
    catalogError,
    form,
    sendCatalogMutation,
    setStep: handleStepChange,
    setSearchInput,
    handleSearch: handleSearchClick,
    handleLoadMore,
    handleProductToggle,
    handleNext,
    handleBack,
    handleReset,
    handleTypeChange,
    handleIntegrationChange,
    onSubmit,
    canProceed,
    getSelectedProductsData,
  };
};
