import {
  <PERSON>dal,
  <PERSON>dal<PERSON><PERSON>lay,
  <PERSON>dal<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  Modal<PERSON>ody,
  Modal<PERSON>ooter,
  Button,
  Box,
  Text,
  ModalCloseButton,
} from '@chakra-ui/react';
import { Faq } from '../../../../../services/faq.service';

interface ViewFaqModalProps {
  isOpen: boolean;
  onClose: () => void;
  faq: Faq | null;
}

export const ViewFaqModal = ({ isOpen, onClose, faq }: ViewFaqModalProps) => {
  return (
    <Modal isOpen={isOpen} onClose={onClose} isCentered>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>Pergunta Frequente</ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <Box display="flex" flexDirection="column" gap="12px">
            <Box display="flex" flexDirection="column" gap="4px">
              <Text as="b" size="sm">
                Pergunta:
              </Text>
              <Text size="sm">{faq?.question}</Text>
            </Box>
            <Box display="flex" flexDirection="column" gap="4px">
              <Text as="b" size="sm" mt={4}>
                Resposta:
              </Text>
              <Text size="sm">{faq?.answer}</Text>
            </Box>
          </Box>
        </ModalBody>
        <ModalFooter>
          <Button colorScheme="blue" onClick={onClose}>
            Fechar
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};
