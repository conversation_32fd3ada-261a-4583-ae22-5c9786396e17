import { useMutation, useQuery, useQueryClient } from 'react-query';
import { useToast } from '@chakra-ui/react';
import { FaqService } from '../services/faq.service';
import { apiRoutes } from '../constants/api-routes';

export const useFaqOperations = (onDeleteSuccess?: () => void) => {
  const toast = useToast();
  const queryClient = useQueryClient();

  const {
    data: faqs = [],
    isLoading,
    error,
  } = useQuery(apiRoutes.listFaqs(), async () => {
    const { data } = await FaqService.listFaqs();
    return data;
  });

  const createFaqsMutation = useMutation(FaqService.bulkCreateFaqs, {
    onSuccess: () => {
      queryClient.invalidateQueries(apiRoutes.listFaqs());
      toast({
        title: 'FAQs criadas com sucesso',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    },
  });

  const updateFaqMutation = useMutation(FaqService.bulkUpdateFaqs, {
    onSuccess: () => {
      queryClient.invalidateQueries(apiRoutes.listFaqs());
      toast({
        title: 'FAQ atualizada com sucesso',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    },
  });

  const deleteFaqsMutation = useMutation(FaqService.bulkDeleteFaqs, {
    onSuccess: () => {
      queryClient.invalidateQueries(apiRoutes.listFaqs());
      toast({
        title: 'FAQs excluídas com sucesso',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
      if (onDeleteSuccess) {
        onDeleteSuccess();
      }
    },
  });

  return {
    faqs,
    isLoading,
    error,
    createFaqsMutation,
    updateFaqMutation,
    deleteFaqsMutation,
  };
};
