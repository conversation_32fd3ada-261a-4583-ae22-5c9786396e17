import { apiRoutes } from '../constants/api-routes';
import { request } from '../constants/request';
import { WinningMetric } from '../types/CampaignExperiment';

interface Variant {
  templateId: string;
  templateArgs?: {
    [key: string]: string | undefined;
  };
}

export interface StartOrScheduleCampaignExperimentDto {
  customerIds: string[];
  filterCriteria?: string;
  scheduledExecutionTime?: string | null;
  variants: Variant[];
  winningMetric: WinningMetric;
  testSizePercentage: number;
  durationInMinutes: number;
  name: string;
}

const buildFormDataForCampaignExperiment = (
  startOrScheduleCampaignExperimentDto: StartOrScheduleCampaignExperimentDto,
): FormData => {
  const formData = new FormData();

  const customerIdsBlob = new Blob(
    [JSON.stringify(startOrScheduleCampaignExperimentDto.customerIds)],
    { type: 'application/json' },
  );
  formData.append('file', customerIdsBlob, 'customerIds.json');

  if (startOrScheduleCampaignExperimentDto.filterCriteria) {
    formData.append(
      'filterCriteria',
      startOrScheduleCampaignExperimentDto.filterCriteria,
    );
  }

  if (startOrScheduleCampaignExperimentDto.scheduledExecutionTime) {
    formData.append(
      'scheduledExecutionTime',
      startOrScheduleCampaignExperimentDto.scheduledExecutionTime,
    );
  }

  formData.append(
    'winningMetric',
    startOrScheduleCampaignExperimentDto.winningMetric,
  );

  formData.append(
    'testSizePercentage',
    startOrScheduleCampaignExperimentDto.testSizePercentage.toString(),
  );

  formData.append(
    'durationInMinutes',
    startOrScheduleCampaignExperimentDto.durationInMinutes.toString(),
  );

  formData.append(
    'variants',
    JSON.stringify(startOrScheduleCampaignExperimentDto.variants),
  );

  formData.append('name', startOrScheduleCampaignExperimentDto.name);

  return formData;
};

const startOrScheduleCampaignExperiment = async (
  startOrScheduleCampaignExperimentDto: StartOrScheduleCampaignExperimentDto,
) => {
  const formData = buildFormDataForCampaignExperiment(
    startOrScheduleCampaignExperimentDto,
  );

  return request.post(apiRoutes.startOrScheduleCampaignExperiment(), formData, {
    timeout: 90000,
    headers: { 'Content-Type': 'multipart/form-data' },
  });
};

export const CampaignExperimentsService = {
  startOrScheduleCampaignExperiment,
};
