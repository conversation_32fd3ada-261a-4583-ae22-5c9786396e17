import { AxiosResponse } from 'axios';
import { request } from '../constants/request';
import { apiRoutes } from '../constants/api-routes';

export interface CreateFaqDto {
  question: string;
  answer: string;
}

export interface BulkCreateFaqsDto {
  faqs: CreateFaqDto[];
}

export interface BulkUpdateFaqsDto {
  id: string;
  question?: string;
  answer?: string;
}

export interface DeleteFaqsDto {
  ids: string[];
}

export interface Faq {
  id: string;
  question: string;
  answer: string;
  companyId: string;
  createdAt: string;
  updatedAt: string;
}

const listFaqs = async (): Promise<AxiosResponse<Faq[]>> => {
  return request.get(apiRoutes.listFaqs());
};

const showFaq = async (id: string): Promise<AxiosResponse<Faq>> => {
  return request.get(apiRoutes.showFaq(id));
};

const bulkCreateFaqs = async (
  data: BulkCreateFaqsDto,
): Promise<AxiosResponse<Faq | Faq[]>> => {
  return request.post(apiRoutes.bulkCreateFaqs(), data);
};

const bulkUpdateFaqs = async (
  data: BulkUpdateFaqsDto,
): Promise<AxiosResponse<Faq | Faq[]>> => {
  return request.put(apiRoutes.bulkUpdateFaqs(), data);
};

const bulkDeleteFaqs = async (data: DeleteFaqsDto): Promise<void> => {
  return request.delete(apiRoutes.bulkDeleteFaqs(), { data });
};

export const FaqService = {
  listFaqs,
  showFaq,
  bulkCreateFaqs,
  bulkUpdateFaqs,
  bulkDeleteFaqs,
};
