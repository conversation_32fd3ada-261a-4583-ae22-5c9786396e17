import { Box, useDisclosure } from '@chakra-ui/react';
import { useState } from 'react';
import { useFaqOperations } from '../../../../hooks/useFaqOperations';
import {
  Faq,
  BulkCreateFaqsDto,
  BulkUpdateFaqsDto,
} from '../../../../services/faq.service';
import AlertDialogBase from '../../../../components/AlertDialog';
import { FaqHeader } from './components/FaqHeader';
import { FaqTable } from './components/FaqTable';
import { CreateFaqModal } from './components/CreateFaqModal';
import { EditFaqModal } from './components/EditFaqModal';
import { ViewFaqModal } from './components/ViewFaqModal';
import { EmptyState } from './components/EmptyState';
import { LoadingState } from './components/LoadingState';

const FaqPage = () => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [isEditMode, setIsEditMode] = useState(false);
  const [faqToDelete, setFaqToDelete] = useState<string | null>(null);
  const [isDeleteAlertOpen, setIsDeleteAlertOpen] = useState(false);
  const [isDeleteManyAlertOpen, setIsDeleteManyAlertOpen] = useState(false);
  const [selectedFaqIds, setSelectedFaqIds] = useState<string[]>([]);
  const [selectedFaq, setSelectedFaq] = useState<Faq | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);

  const {
    faqs,
    isLoading,
    createFaqsMutation,
    updateFaqMutation,
    deleteFaqsMutation,
  } = useFaqOperations(() => {
    setSelectedFaqIds([]);
  });

  const handleEditFaq = (faq: Faq) => {
    setIsEditMode(true);
    setSelectedFaq(faq);
    onOpen();
  };

  const handleViewFaq = (faq: Faq) => {
    setSelectedFaq(faq);
    setIsViewModalOpen(true);
  };

  const handleDeleteFaq = (faqId: string) => {
    setFaqToDelete(faqId);
    setIsDeleteAlertOpen(true);
  };

  const handleDeleteManyFaqs = () => {
    if (selectedFaqIds.length > 0) {
      setIsDeleteManyAlertOpen(true);
    }
  };

  const confirmDeleteMany = () => {
    if (selectedFaqIds.length > 0) {
      const ids = selectedFaqIds;
      deleteFaqsMutation.mutate({ ids });
      setIsDeleteManyAlertOpen(false);
      setSelectedFaqIds([]);
    }
  };

  const cancelDeleteMany = () => {
    setIsDeleteManyAlertOpen(false);
  };

  const confirmDelete = () => {
    if (faqToDelete) {
      deleteFaqsMutation.mutate({ ids: [faqToDelete] });
      setFaqToDelete(null);
      setIsDeleteAlertOpen(false);
      setSelectedFaqIds((prev) => prev.filter((id) => id !== faqToDelete));
    }
  };

  const cancelDelete = () => {
    setFaqToDelete(null);
    setIsDeleteAlertOpen(false);
  };

  const handleCloseModal = () => {
    onClose();
    setIsEditMode(false);
    setSelectedFaq(null);
  };

  const handleOpenCreateModal = () => {
    setIsEditMode(false);
    setSelectedFaq(null);
    onOpen();
  };

  const toggleSelectAll = () => {
    if (selectedFaqIds.length > 0) {
      setSelectedFaqIds([]);
    } else {
      setSelectedFaqIds(faqs.map((faq) => faq.id));
    }
  };

  const toggleSelectOne = (id: string) => {
    setSelectedFaqIds((prev) =>
      prev.includes(id) ? prev.filter((faqId) => faqId !== id) : [...prev, id],
    );
  };

  const handleCreateFaqs = (data: BulkCreateFaqsDto) => {
    createFaqsMutation.mutate(data);
    handleCloseModal();
  };

  const handleUpdateFaq = (data: BulkUpdateFaqsDto) => {
    updateFaqMutation.mutate(data);
    handleCloseModal();
  };

  return (
    <Box
      bg="white"
      borderRadius="md"
      border="1px solid"
      borderColor="gray.200"
      p={8}
    >
      <FaqHeader
        selectedFaqIds={selectedFaqIds}
        onDeleteMany={handleDeleteManyFaqs}
        onOpenCreateModal={handleOpenCreateModal}
      />

      {isLoading ? (
        <LoadingState />
      ) : faqs.length === 0 ? (
        <EmptyState />
      ) : (
        <FaqTable
          faqs={faqs}
          selectedFaqIds={selectedFaqIds}
          onToggleSelectAll={toggleSelectAll}
          onToggleSelectOne={toggleSelectOne}
          onViewFaq={handleViewFaq}
          onEditFaq={handleEditFaq}
          onDeleteFaq={handleDeleteFaq}
        />
      )}

      <CreateFaqModal
        isOpen={isOpen && !isEditMode}
        onClose={handleCloseModal}
        onSubmit={handleCreateFaqs}
        isLoading={createFaqsMutation.isLoading}
      />

      <EditFaqModal
        isOpen={isOpen && isEditMode}
        onClose={handleCloseModal}
        onSubmit={handleUpdateFaq}
        isLoading={updateFaqMutation.isLoading}
        faq={selectedFaq}
      />

      <ViewFaqModal
        isOpen={isViewModalOpen}
        onClose={() => setIsViewModalOpen(false)}
        faq={selectedFaq}
      />

      <AlertDialogBase
        isOpen={isDeleteAlertOpen}
        onClose={cancelDelete}
        title="Excluir FAQ"
        onConfirm={confirmDelete}
        buttonConfirmColor="red"
        buttonConfirmText="Excluir"
        buttonRejectText="Cancelar"
        isConfirmLoading={deleteFaqsMutation.isLoading}
      >
        Tem certeza que deseja excluir esta Pergunta Frequente? Esta ação não
        pode ser desfeita.
      </AlertDialogBase>

      <AlertDialogBase
        isOpen={isDeleteManyAlertOpen}
        onClose={cancelDeleteMany}
        title="Excluir FAQs Selecionadas"
        onConfirm={confirmDeleteMany}
        buttonConfirmColor="red"
        buttonConfirmText="Excluir"
        buttonRejectText="Cancelar"
        isConfirmLoading={deleteFaqsMutation.isLoading}
      >
        Tem certeza que deseja excluir as {selectedFaqIds.length} pergunta(s)
        selecionada(s)? Esta ação não pode ser desfeita.
      </AlertDialogBase>
    </Box>
  );
};

export default FaqPage;
