import { Box, Tab, TabList, TabPanel, TabPanels, Tabs } from '@chakra-ui/react';
import { colors } from '../../../constants/colors';
import GeneralSettingsPage from './GeneralSettingsPage';
import AccountSettingsPage from './AccountSettingsPage';
import { Link, useLocation } from 'react-router-dom';
import React, { useEffect, useState } from 'react';

interface TabConfiguration {
  index: number;
  name: string;
  path: string;
  component: React.FC;
}

const TAB_PARAM_NAME = 'tab';
const tabConfiguration: TabConfiguration[] = [
  {
    index: 0,
    name: '<PERSON><PERSON>ada<PERSON>',
    path: 'dados',
    component: GeneralSettingsPage,
  },
  {
    index: 1,
    name: 'Seguran<PERSON>',
    path: 'seguranca',
    component: AccountSettingsPage,
  },
];

const BackgroundSettingsPage = () => {
  const [tabIndex, setTabIndex] = useState(0);
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const tabParam = searchParams.get(TAB_PARAM_NAME);

  useEffect(() => {
    if (tabParam) {
      const tab = tabConfiguration.find((tab) => tab.path === tabParam);
      setTabIndex(tab?.index ?? 0);
    }
  }, [tabParam]);

  return (
    <Box>
      <Tabs variant="unstyled" index={tabIndex}>
        <TabList borderBottom="1px solid" borderColor="gray.200" mb={4}>
          {tabConfiguration.map((tab) => (
            <Tab
              key={tab.path}
              _selected={{
                color: `${colors.primary}`,
                borderBottom: `2px solid ${colors.primary}`,
              }}
              px={4}
              py={2}
              fontWeight="medium"
              color={colors.darkGrey}
            >
              <Link to={`?${TAB_PARAM_NAME}=${tab.path}`}>{tab.name}</Link>
            </Tab>
          ))}
        </TabList>

        <TabPanels>
          {tabConfiguration.map((tab) => (
            <TabPanel px={0} key={tab.path}>
              <tab.component />
            </TabPanel>
          ))}
        </TabPanels>
      </Tabs>
    </Box>
  );
};

export default BackgroundSettingsPage;
