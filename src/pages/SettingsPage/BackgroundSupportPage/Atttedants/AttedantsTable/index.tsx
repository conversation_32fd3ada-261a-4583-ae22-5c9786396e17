import {
  Avatar,
  Table,
  <PERSON><PERSON>ontainer,
  <PERSON>body,
  Td,
  Thead,
  Tr,
  Switch,
  Flex,
} from '@chakra-ui/react';
import { useQuery } from 'react-query';
import { apiRoutes } from '../../../../../constants/api-routes';
import { colors } from '../../../../../constants/colors';
import { TextTd, Th } from '../../../../../components/CustomTable';
import { UsersService } from '../../../../../services/users.service';
import { User } from '../../../../../types/Prisma';
import { scrollbarStyles } from '../../../../../styles/scrollbar.styles';
import { useUserToggle } from '../../../../../hooks/useUserToggle';

const TableAttedants = () => {
  const queryKey = apiRoutes.listCompanyAgents({
    skipInactiveUsers: false,
    skipAiAgents: false,
  });

  const { data: companyAgents = [] } = useQuery(queryKey, async () => {
    const { data } = await UsersService.listCompanyAgents({
      skipInactiveUsers: false,
      skipAiAgents: false,
    });
    return data;
  });

  const { toggleUserStatus, toggleCanViewAllConversations } =
    useUserToggle(queryKey);

  return (
    <TableContainer
      borderRadius="md"
      border="1px solid"
      borderColor={colors.border}
      maxHeight="300px"
      overflowY="auto"
      css={scrollbarStyles({ width: '4px' })}
    >
      <Table variant="simple" size="sm">
        <Thead>
          <Tr bgColor={colors.slateSuperLight}>
            <Th width="30%" minWidth="200px">NOME</Th>
            <Th width="30%" minWidth="200px">EMAIL</Th>
            <Th width="15%" textAlign="center" minWidth="120px">
              USUÁRIO ATIVO
            </Th>
            <Th width="25%" textAlign="center" minWidth="180px">
              PODE VER TODAS AS CONVERSAS
            </Th>
          </Tr>
        </Thead>
        <Tbody>
          {companyAgents?.map((agent: User) => (
            <Tr key={agent.id} bg={colors.white}>
              <TextTd width="30%" textForTooltip={agent.name}>
                <Flex align="center" gap="2">
                  <Avatar name={agent.name} size="sm" />
                  {agent.name}
                </Flex>
              </TextTd>
              <TextTd width="30%" textForTooltip={agent.email}>
                {agent.email}
              </TextTd>
              <Td
                width="15%"
                textAlign="center"
              >
                <Switch
                  isChecked={agent.isActive}
                  colorScheme="black"
                  isDisabled={toggleUserStatus.isLoading}
                  onChange={() => toggleUserStatus.mutate(agent.id)}
                />
              </Td>
              <Td
                width="25%"
                textAlign="center"
              >
                <Switch
                  isChecked={agent.canViewAllConversations}
                  colorScheme="black"
                  isDisabled={toggleCanViewAllConversations.isLoading}
                  onChange={() =>
                    toggleCanViewAllConversations.mutate(agent.id)
                  }
                />
              </Td>
            </Tr>
          ))}
        </Tbody>
      </Table>
    </TableContainer>
  );
};

export default TableAttedants;
