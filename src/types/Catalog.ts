export interface CatalogFormData {
  type: 'catalog' | 'spm' | 'mpm';
  header?: string;
  body: string;
  footer?: string;
  selectedProducts: string[];
}

export interface CatalogType {
  label: string;
  description: string;
  needsHeader: boolean;
  maxProducts: number;
  minProducts: number;
}

export const CATALOG_TYPES: Record<string, CatalogType> = {
  catalog: {
    label: 'Catálogo Completo',
    description: 'Lista com todos seus produtos sincronizados',
    needsHeader: true,
    maxProducts: 1,
    minProducts: 1,
  },
  spm: {
    label: 'Produto Único (SPM)',
    description: 'Destaque um produto específico para o cliente',
    needsHeader: false,
    maxProducts: 1,
    minProducts: 1,
  },
  mpm: {
    label: 'Múltiplos Produtos (MPM)',
    description: 'Seleção curada de produtos específicos',
    needsHeader: true,
    maxProducts: 30,
    minProducts: 1,
  },
};
