import {
  Button,
  Flex,
  <PERSON>,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  TableContainer,
  Tag,
} from '@chakra-ui/react';
import { format } from 'date-fns';
import { IntegrationsConfigDto } from '../../services/integrations.service';
import { SourceIntegrationType } from '../../types/SourceIntegration';

export interface IntegrationsTableColumn {
  key: 'isOrderActive' | 'isProductActive' | 'isAbandonedCartActive';
  label: string;
  show: boolean;
}

/**
 * Componente reutilizável para exibir tabelas de integrações
 *
 * @example
 * // Para mostrar apenas sincronização de pedidos (como VendaAi e Irroba)
 * const columns = [
 *   { key: 'isOrderActive', label: 'Sinc. de Pedidos', show: true },
 *   { key: 'isProductActive', label: 'Sinc. de Produtos', show: false },
 *   { key: 'isAbandonedCartActive', label: 'Sinc. de Carrinhos Abandonados', show: false },
 * ];
 *
 * @example
 * // Para mostrar todas as colunas
 * const columns = [
 *   { key: 'isOrderActive', label: 'Sinc. de Pedidos', show: true },
 *   { key: 'isProductActive', label: 'Sinc. de Produtos', show: true },
 *   { key: 'isAbandonedCartActive', label: 'Sinc. de Carrinhos Abandonados', show: true },
 * ];
 */

export interface IntegrationsTableProps {
  integrationsConfig?: IntegrationsConfigDto[] | IntegrationsConfigDto;
  sourceIntegration: SourceIntegrationType;
  columns?: IntegrationsTableColumn[];
  onEdit: (integration: IntegrationsConfigDto) => void;
  isLoading?: boolean;
  renderAdditionalActions?: (
    integration: IntegrationsConfigDto,
  ) => React.ReactNode;
}

const defaultColumns: IntegrationsTableColumn[] = [
  { key: 'isOrderActive', label: 'Sinc. de Pedidos', show: true },
  { key: 'isProductActive', label: 'Sinc. de Produtos', show: true },
  {
    key: 'isAbandonedCartActive',
    label: 'Sinc. de Carrinhos Abandonados',
    show: true,
  },
];

const IntegrationsTable = ({
  integrationsConfig,
  sourceIntegration,
  columns = defaultColumns,
  onEdit,
  isLoading = false,
  renderAdditionalActions,
}: IntegrationsTableProps) => {
  const visibleColumns = columns.filter((col) => col.show);
  const integrations = Array.isArray(integrationsConfig)
    ? integrationsConfig
    : integrationsConfig
      ? [integrationsConfig]
      : [];

  const filteredIntegrations = integrations.filter(
    (integration) => integration.source === sourceIntegration,
  );

  const renderStatusTag = (isActive: boolean) => (
    <Tag colorScheme={isActive ? 'green' : 'red'}>
      {isActive ? 'Ativo' : 'Inativo'}
    </Tag>
  );

  const getColumnSpan = () => {
    // Base columns: Descrição, Criado em, Status, Ações = 4
    // Plus visible sync columns
    return 4 + visibleColumns.length;
  };

  return (
    <TableContainer mt={6}>
      <Table variant="simple">
        <Thead>
          <Tr>
            <Th>Descrição</Th>
            {visibleColumns.map((column) => (
              <Th key={column.key}>{column.label}</Th>
            ))}
            <Th>Criado em</Th>
            <Th>Status</Th>
            <Th>Ações</Th>
          </Tr>
        </Thead>
        <Tbody>
          {filteredIntegrations.length > 0 ? (
            filteredIntegrations.map((integration) => (
              <Tr key={integration.id}>
                <Td>{integration.description}</Td>
                {visibleColumns.map((column) => (
                  <Td key={column.key}>
                    {renderStatusTag(integration[column.key])}
                  </Td>
                ))}
                <Td>
                  {format(new Date(integration.createdAt), 'dd/MM/yyyy HH:mm')}
                </Td>
                <Td>{renderStatusTag(integration.isActive)}</Td>
                <Td>
                  <Flex gap={2}>
                    {renderAdditionalActions &&
                      renderAdditionalActions(integration)}
                    <Button
                      size="sm"
                      colorScheme="blue"
                      onClick={() => onEdit(integration)}
                      isLoading={isLoading}
                    >
                      Editar
                    </Button>
                  </Flex>
                </Td>
              </Tr>
            ))
          ) : (
            <Tr>
              <Td colSpan={getColumnSpan()} textAlign="center">
                Nenhuma integração encontrada
              </Td>
            </Tr>
          )}
        </Tbody>
      </Table>
    </TableContainer>
  );
};

export default IntegrationsTable;
